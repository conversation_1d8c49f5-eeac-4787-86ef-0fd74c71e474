import { ERROR_STATUS, SUCCE<PERSON>_STATUS, TABLE_NAMES } from '../constants';
import { NOTIFICATION_SERVICE, NOTIFICATION_DATA_ACTIONS, ROLE_TRANSITION_DIALOG } from './actionTypes';
import { ROLES_MODAL, ROLES_TRANSITION_ACTION } from '../constants/rolesConsts';

const { START_CONNECTION, CONNECTION_STATUS, CLOSE_CONNECTION } = NOTIFICATION_SERVICE;
const {
    LOAD_NOTIFICATION_HISTORY,
    NOTIFICATIONS_LOAD_DATA,
    LOAD_REAL_TIME_NOTIFICATION_HISTORY_DATA,
    CH<PERSON><PERSON>_NOTIFICATION_READ_UNREAD_STATUS,
    MARK_ALL_NOTIFICATION_READ,
    DELETE_NOTIFICATIONS,
    DELETE_ENTITY_FROM_NOTIFICATION_EW,
    UPDATE_NOTIFICATIONS_ENTITIES_TABLE_DATA,
    LOAD_NOTIFICATIONS_UNREAD_DATA_COUNT } = NOTIFICATION_DATA_ACTIONS;
const { PROGRESS_ROLES_DATA_ALIAS } = ROLES_MODAL;

//Notification data related actions are below
export function notificationsLoadData(params) {
    return {
        type: `${NOTIFICATIONS_LOAD_DATA}`,
        payload: {
            params
        }
    };
}

export function loadNotificationHistory(payload) {
    return {
        type: `${LOAD_NOTIFICATION_HISTORY}`,
        payload
    };
}

export function loadNotificationHistorySuccess(payload) {
    return {
        type: `${LOAD_NOTIFICATION_HISTORY}_${SUCCESS_STATUS}`,
        payload
    };
}

export function loadNotificationHistoryError(payload) {
    return {
        type: `${LOAD_NOTIFICATION_HISTORY}_${ERROR_STATUS}`,
        payload
    };
}

export function loadRealTimeNotificationHistorySuccess(payload) {
    return {
        type: `${LOAD_REAL_TIME_NOTIFICATION_HISTORY_DATA}_${SUCCESS_STATUS}`,
        payload
    };
}

export function onChangeNotificationReadUnreadStatus(guid, notificationRead) {
    return {
        type: `${CHANGE_NOTIFICATION_READ_UNREAD_STATUS}`,
        payload: {
            guid,
            notificationRead
        }
    };
}

export function onChangeNotificationReadUnreadStatusSuccess(payload) {
    return {
        type: `${CHANGE_NOTIFICATION_READ_UNREAD_STATUS}_${SUCCESS_STATUS}`,
        payload
    };
}

export function markAllNotificationRead(requestPreferenceKeys = []) {
    return {
        type: `${MARK_ALL_NOTIFICATION_READ}`,
        payload: requestPreferenceKeys
    };
}

export function markAllNotificationReadSuccess() {
    return {
        type: `${MARK_ALL_NOTIFICATION_READ}_${SUCCESS_STATUS}`
    };
}

export function deleteNotification(payload) {
    return {
        type: `${DELETE_NOTIFICATIONS}`,
        payload
    };
}

export function deleteNotificationSuccess(payload) {
    return {
        type: `${DELETE_NOTIFICATIONS}_${SUCCESS_STATUS}`,
        payload
    };
}

export function deleteEntityFromNotificationEWSuccess(payload) {
    return {
        type: `${DELETE_ENTITY_FROM_NOTIFICATION_EW}_${SUCCESS_STATUS}`,
        payload
    };
}

export function loadNotificationHistoryUnreadCount(payload) {
    return {
        type: `${LOAD_NOTIFICATIONS_UNREAD_DATA_COUNT}`,
        payload
    };
}

export function loadNotificationHistoryUnreadCountSuccess(payload) {
    return {
        type: `${LOAD_NOTIFICATIONS_UNREAD_DATA_COUNT}_${SUCCESS_STATUS}`,
        payload
    };
}

//Notification service related actions are below
export function onNotificationConnectionStart() {
    return {
        type: `${START_CONNECTION}`
    };
}

export function onConnectionSuccessfull(payload) {
    return {
        type: `${CONNECTION_STATUS}_${SUCCESS_STATUS}`,
        payload
    };
}

export function onConnectionFail(payload) {
    return {
        type: `${CONNECTION_STATUS}_${ERROR_STATUS}`,
        payload
    };
}

export function onConnectionClose(payload) {
    return {
        type: `${CLOSE_CONNECTION}`,
        payload
    };
}

export function createBookingAction(entityId) {
    return {
        type: `${ROLE_TRANSITION_DIALOG.OPEN_DIALOG_REQUEST}_${ROLES_MODAL.MAKE_LIVE}`,
        payload: {
            tableName: TABLE_NAMES.ROLEREQUEST,
            roleGuids: [entityId],
            alias: PROGRESS_ROLES_DATA_ALIAS,
            roleTransitionActionType: ROLES_TRANSITION_ACTION.ROLES_ACTION_MAKE_LIVE
        }
    };
}

export function updateNotificationsEntitiesForTableData(payload) {
    return {
        type: `${UPDATE_NOTIFICATIONS_ENTITIES_TABLE_DATA}`,
        payload
    };
}

export function loadFirstBookingInTheSeries(payload) {
    return {
        type: `${NOTIFICATION_DATA_ACTIONS.LOAD_FIRST_BOOKING_IN_THE_SERIES}`,
        payload
    };
}

export function loadUnconfirmedBookingsNotificationSettingsData(payload) {
    return {
        type: `${NOTIFICATION_DATA_ACTIONS.UNCONFIRMED_BOOKINGS_NOTIFICATION_LOAD_DATA}`,
        payload
    };
}