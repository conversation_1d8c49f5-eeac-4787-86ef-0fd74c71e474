import { RESOURCE_SKILLS, SK<PERSON>LS_CHANGES, RECOMMENDATION_SKILLS, API_SUFFIX, RESOURCE_SKILLS_APPROVALS } from './actionTypes';

const { SUCCESS, FAILURE } = API_SUFFIX;

export const populateResourceSkills = (alias, { entityId }, skills) => {
    return {
        type: RESOURCE_SKILLS.POPULATE.SKILLS,
        payload: {
            entityId,
            skills
        }
    };
};

export const populateResourceSkillsError = (alias, { entityId }) => {
    return {
        type: RESOURCE_SKILLS.POPULATE.SKILLS,
        payload: {
            entityId,
            skills: []
        }
    };
};

export const loadResourceSkills = (id) => {
    return {
        type: RESOURCE_SKILLS.LOAD.SKILLS,
        id
    };
};

export const loadSkillPreferences = () => {
    return {
        type: RESOURCE_SKILLS.LOAD.SKILL_PREFERENCES
    };
};

export const populateSkillPreferences = (alias, payload, preferences) => {
    return {
        type: RESOURCE_SKILLS.POPULATE.SKILL_PREFERENCES,
        payload: {
            preferences
        }
    };
};

export const deleteResourceSkill = (resourceId, skillId) => {
    return {
        type: RESOURCE_SKILLS.DELETE,
        payload: {
            resourceId,
            skillId
        }
    };
};

export const populateUISkillData = (entityId, skill) => {
    return {
        type: RESOURCE_SKILLS.UI.INIT,
        payload: {
            entityId,
            skill
        }
    };
};

export const uiSkillFieldChange = (entityId, skillId, fieldInfo, fieldValue, skillInfo = {}, errors = []) => {
    return {
        type: RESOURCE_SKILLS.UI.PRE_FIELD_CHANGE,
        payload: {
            entityId,
            skillId,
            fieldInfo,
            fieldValue,
            errors,
            skillInfo
        }
    };
};

export const updateUISkillFieldChange = (entityId, skillId, fieldInfo, fieldValue, skillInfo = {}, errors = []) => {
    return {
        type: RESOURCE_SKILLS.UI.FIELD_CHANGE,
        payload: {
            entityId,
            skillId,
            fieldInfo,
            fieldValue,
            errors,
            skillInfo
        }
    };
};

export const updateResourceSkill = (resourceId, skillId) => {
    return {
        type: RESOURCE_SKILLS.UPDATE,
        payload: {
            resourceId,
            skillId
        }
    };
};


/**
 * The action is used to load the authorized resource skills.
 */
export const loadAuthorizeResourceSkills = () => {
    return {
        type: RESOURCE_SKILLS.LOAD.AUTHORIZE_SKILLS
    };
};

/**
 * The action is used to populate the authorized resource skills.
 * @param {*} skills
 * @returns {{ type: string; payload: { skills: import('../types/authorizeResourceSkillDTO.jsdoc').AuthorizeResourceSkillDTO[]; }; }}
 */
export const populateAuthorizeResourceSkills = (skills) => {
    return {
        type: `${RESOURCE_SKILLS.POPULATE.AUTHORIZE_SKILLS}_${SUCCESS}`,
        payload: {
            skills
        }
    };
};

/**
 * The action is used to handle the failure of loading authorized resource skills.
 */
export const loadAuthorizeResourceSkillsFailure = () => {
    return {
        type: `${RESOURCE_SKILLS.LOAD.AUTHORIZE_SKILLS}_${FAILURE}`
    };
};

//proper action names RESOURCE_SKILLS_CHANGES_ actions
export const markSkillForDeletion = (entityId, skillId) => {
    return {
        type: SKILLS_CHANGES.REMOVE_SKILL,
        payload: {
            entityId,
            skillId
        }
    };
};

export const resourceSkillDiscardDelete = (entityId, skillId) => {
    return {
        type: SKILLS_CHANGES.DISCARD_DELETE,
        payload: {
            entityId,
            skillId
        }
    };
};

export const resourceSkillsDiscardChanges = (entityId) => {
    return {
        type: SKILLS_CHANGES.DISCARD_CHANGES,
        payload: {
            entityId
        }
    };
};

export const addResourceSkills = (entityId, sectionId, skillIds) => {
    return {
        type: SKILLS_CHANGES.ADD_SKILLS,
        payload: {
            entityId,
            sectionId,
            skillIds
        }
    };
};

export const skillFormSetFieldErrors = (entityId, skillId, errors) => {
    return {
        type: SKILLS_CHANGES.SET_SKILL_FIELDS_ERRORS,
        payload: {
            entityId,
            skillId,
            errors
        }
    };
};

export const recommendationSkillsWindowLoad = (entityId) => {
    return {
        type: `${RECOMMENDATION_SKILLS.LOAD_RECOMMENDATION_SKILLS}`,
        payload: { entityId }
    };
};

export const recommendationSkillsWindowLoadSuccess = (entityId, recommendationData) => {
    return {
        type: `${RECOMMENDATION_SKILLS.LOAD_RECOMMENDATION_SKILLS}${SUCCESS}`,
        payload: { entityId, recommendationData }
    };
};

export const recommendationSkillsWindowLoadError = (entityId) => {
    return {
        type: `${RECOMMENDATION_SKILLS.LOAD_RECOMMENDATION_SKILLS}${FAILURE}`,
        payload: { entityId }
    };
};

export const resourceSkillsApprovalsPending = (entityId) => {
    return {
        type: RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_PENDING,
        payload: { entityId }
    };
};

export const resourceSkillsApprovalsPendingSuccess = (entityId, data) => {
    return {
        type: `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_PENDING}${SUCCESS}`,
        payload: { entityId, data }
    };
};

export const resourceSkillsApprovalsPendingError = (entityId) => {
    return {
        type: `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_PENDING}${FAILURE}`,
        payload: { entityId }
    };
};

export const resourceSkillsApprovalsHistory = (entityId, sortBy, pageSize, pageNumber) => {
    return {
        type: RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_HISTORY,
        payload: { entityId, sortBy, pageSize, pageNumber }
    };
};

export const resourceSkillsApprovalsHistorySuccess = (entityId, data, count, pageNumber) => {
    return {
        type: `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_HISTORY}${SUCCESS}`,
        payload: { entityId, data, count, pageNumber }
    };
};

export const resourceSkillsApprovalsHistoryError = (entityId) => {
    return {
        type: `${RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_HISTORY}${FAILURE}`,
        payload: { entityId }
    };
};

export const onAcceptRecommendSkills = (entityId, sectionId, skillIds, skillInfos) => {
    return {
        type: RECOMMENDATION_SKILLS.ACCEPT_RECOMMENDATION_SKILLS,
        payload: {
            entityId,
            sectionId,
            skillIds,
            skillInfos
        }
    };
};

export const onIgnoreRecommendSkills = (entityId, skillIds) => {
    return {
        type: RECOMMENDATION_SKILLS.IGNORE_RECOMMENDATION_SKILLS,
        payload: {
            entityId,
            skillIds
        }
    };
};

export const discardIgnoreRecommendationSkills = (entityId) => {
    return {
        type: RECOMMENDATION_SKILLS.DISCARD_IGNORE_RECOMMENDATION_SKILLS,
        payload: {
            entityId
        }
    };
};
