/**
 * Action type for initiating options fetch
 * @constant {string}
 */
export const FETCH_OPTIONS = 'FETCH_OPTIONS';

/**
 * Action type for successful options fetch
 * @constant {string}
 */
export const FETCH_OPTIONS_SUCCESS = 'FETCH_OPTIONS_SUCCESS';

/**
 * Action type for failed options fetch
 * @constant {string}
 */
export const FETCH_OPTIONS_ERROR = 'FETCH_OPTIONS_ERROR';

/**
 * Action type for setting selected value
 * @constant {string}
 */
export const SET_SELECTED_VALUE = 'SET_SELECTED_VALUE';

/**
 * Creates an action to fetch options
 * @param {string} searchTerm - Term to filter results
 * @param {string} sourceId - Identifier for the data source
 * @param {boolean} isTypeahead - Whether this is a typeahead search
 * @param {string} methodType - Defines the type of request 'GET' or 'POST'.
 * @param {object} payload - The payload of the request.
 * @returns {Object} Redux action
 */
export const fetchOptions = (searchTerm, sourceId, isTypeahead, methodType, payload) => ({
    type: FETCH_OPTIONS,
    payload: { searchTerm, sourceId, isTypeahead, methodType, payload }
});

/**
 * Creates an action for successful options fetch
 * @param {Array} options - Fetched and transformed options
 * @param {string} sourceId - Identifier for the data source
 * @param {boolean} isTypeahead - Whether this was a typeahead search
 * @returns {Object} Redux action
 */
export const fetchOptionsSuccess = (options, sourceId, isTypeahead) => ({
    type: FETCH_OPTIONS_SUCCESS,
    payload: { options, sourceId, isTypeahead }
});

/**
 * Creates an action for failed options fetch
 * @param {Error} error - The error that occurred
 * @param {string} sourceId - Identifier for the data source
 * @returns {Object} Redux action
 */
export const fetchOptionsError = (error, sourceId) => ({
    type: FETCH_OPTIONS_ERROR,
    payload: { error, sourceId }
});

/**
 * Creates an action to set the selected value
 * @param {(string|number|Array)} value - The selected value(s)
 * @param {string} sourceId - Identifier for the data source
 * @returns {Object} Redux action
 */
export const setSelectedValue = (value, sourceId) => ({
    type: SET_SELECTED_VALUE,
    payload: { value, sourceId }
});
