import { COMMAND_BAR_WORKSPACES_SECTION, JOBS_COMMAND_BAR, LIST_PAGE_ACTIONS } from './actionTypes';

export function updateListView(payload) {
    return {
        type: LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW,
        payload
    };
}

// List page command bar actions
export function commandBarSetSectionVisibility(sectionKey, visible, pageAlias) {
    return {
        type: JOBS_COMMAND_BAR.SET_SECTION_VISIBILITY,
        payload: {
            sectionKey,
            visible,
            pageAlias
        }
    };
}

export function commandBarPopulateWorkspacesSection(workspaces) {
    return {
        type: COMMAND_BAR_WORKSPACES_SECTION.POPULATE,
        payload: {
            workspaces,
            selectedWorkspaceGuid: workspaces?.selected
        }
    };
}

export function updateWorkspaceViewType(payload) {
    return {
        type: LIST_PAGE_ACTIONS.UPDATE_WORKSPACE_VIEW_TYPE,
        payload
    };
}

export function loadListPageWorkspaces(groupByType) {
    return {
        type: LIST_PAGE_ACTIONS.LOAD_WORKSPACES,
        payload: {
            groupByType
        }
    };
}

export function createListPageWorkspace(uuid, selectCreatedWorkspace) {
    return {
        type: LIST_PAGE_ACTIONS.CREATE_WORKSPACE,
        payload: {
            uuid,
            selectCreatedWorkspace
        }
    };
}

export function digestCreateListPageWorkspace(wsAccessType, wsEditRights, newWorkspaceTemplateGuid, privatePlans, newPlanLabel, doCreate, selectCreatedWorkspace) {
    return {
        type: LIST_PAGE_ACTIONS.CREATE_WORKSPACE,
        payload: {
            wsAccessType,
            wsEditRights,
            newWorkspaceTemplateGuid,
            privatePlans,
            newPlanLabel,
            doCreate,
            selectCreatedWorkspace
        }
    };
}

export function digestCreateListPageWorkspaceSuccess(alias, payload, data) {
    return {
        type: LIST_PAGE_ACTIONS.CREATE_WORKSPACE_SUCCESSFUL,
        payload: {
            ...payload,
            data
        }
    };
}

export function renameListPageWorkspace(workspaceGuid, workspaceData) {
    return {
        type: LIST_PAGE_ACTIONS.RENAME_WORKSPACE,
        payload: {
            workspaceGuid,
            workspaceData
        }
    };
}

export function renameListPageWorkspaceSuccess(alias, payload, data) {
    return {
        type: LIST_PAGE_ACTIONS.RENAME_WORKSPACE_SUCCESS,
        payload: {
            ...payload,
            data
        }
    };
}

export function deleteListPageWorkspace(workspaceGuid) {
    return {
        type: LIST_PAGE_ACTIONS.DELETE_WORKSPACE,
        payload: {
            workspaceGuid
        }
    };
}

export function deleteListPageWorkspaceSuccess(alias, payload, data) {
    return {
        type: LIST_PAGE_ACTIONS.DELETE_WORKSPACE_SUCCESS,
        payload: {
            ...payload,
            data
        }
    };
}

export function setCreateListPageWorkspaceChange(newWSUUID, newWorkspaceStructure, newWorkspaceSettings, doCreate, selectCreatedWorkspace) {
    return {
        type: LIST_PAGE_ACTIONS.SET_CREATE_WORKSPACE_CHANGE,
        payload: {
            newWSUUID,
            newWorkspaceStructure,
            newWorkspaceSettings,
            doCreate,
            selectCreatedWorkspace
        }
    };
}

export function digestCreateListPageWorkspaceAction(wsAccessType, wsEditRights, newWorkspaceTemplateGuid, privatePlans, newPlanLabel, doCreate, selectCreatedWorkspace) {
    return {
        type: LIST_PAGE_ACTIONS.DIGEST_CREATE_WORKSPACE,
        payload: {
            wsAccessType,
            wsEditRights,
            newWorkspaceTemplateGuid,
            privatePlans,
            newPlanLabel,
            doCreate,
            selectCreatedWorkspace
        }
    };
}

export function saveListPageWorkspaceIfAnyChanges(saveWorkspaceGuid, selectWorkspaceGuid) {
    return {
        type: LIST_PAGE_ACTIONS.SAVE_WORKSPACE_IF_ANY_CHANGES,
        payload: {
            saveWorkspaceGuid,
            selectWorkspaceGuid
        }
    };
}

export function digestSelectListPageWorkspace(workspaceGuid) {
    return {
        type: LIST_PAGE_ACTIONS.DIGEST_SELECT_WORKSPACE,
        payload: {
            workspaceGuid
        }
    };
}

export function moveListPageWorkspace(workspaceGuid, workspaceData) {
    return {
        type: LIST_PAGE_ACTIONS.MOVE_WORKSPACE,
        payload: {
            workspaceGuid,
            workspaceData
        }
    };
}

export function digestListPageWorkspaceStructureChange(workspaceGuid, changes) {
    return {
        type: LIST_PAGE_ACTIONS.DIGEST_WORKSPACE_STRUCTURE_CHANGE,
        payload: {
            workspaceGuid,
            changes
        }
    };
}

export function removeCreateListPageWorkspaceChange(workspaceChangeUUID) {
    return {
        type: LIST_PAGE_ACTIONS.REMOVE_CREATE_WORKSPACE_CHANGE,
        payload: {
            workspaceChangeUUID
        }
    };
}

export function loadCopyListPageWorkspaceTemplate(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, selectWorkspace) {
    return {
        type: LIST_PAGE_ACTIONS.LOAD_COPY_WORKSPACE_TEMPLATE,
        payload: {
            newWorkspaceAccessType,
            newWorkspaceEditRights,
            newWorkspaceTemplateGuid,
            doCreate,
            originalWorkspaceName,
            workspaceGuid: newWorkspaceTemplateGuid,
            selectWorkspace
        }
    };
}

export function digestCopyListPageWorkspace(wsAccessType, wsEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, workspaceColourthemeGuid, workspaceCustomColourTheme) {
    return {
        type: LIST_PAGE_ACTIONS.DIGEST_COPY_WORKSPACE,
        payload: {
            wsAccessType,
            wsEditRights,
            newWorkspaceTemplateGuid,
            doCreate,
            originalWorkspaceName,
            workspaceColourthemeGuid,
            workspaceCustomColourTheme
        }
    };
}
