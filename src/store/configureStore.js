import {createStore, applyMiddleware, compose} from 'redux';
import {persistStore, persistReducer} from 'redux-persist';
import storageSession from 'redux-persist/lib/storage/session';
import autoMergeLevel1 from 'redux-persist/lib/stateReconciler/autoMergeLevel1';
import {createEpicMiddleware} from 'redux-observable';
import rootReducer from '../reducers/rootReducers';
import rootEpic from '../epics/rootEpic';
import apisMap from '../api/index';
import {errorHandlingMiddleware} from './middlewares/errorHandlingMiddleware';
import {createNotificationMiddleware} from './middlewares/notificationMiddleware';
import {notificationCallbacks, notificationConnection} from '../utils/notificationService';
import { PERSIST_DATA_LOCAL_STORAGE_KEY } from '../constants/localStorageConsts';
import { loadUser } from '../authentication/authenticationHandlers';
import { browserHistory } from '../history';
import { ENTITY_WINDOW_MODULES } from '../constants';

const epicMiddleware = createEpicMiddleware({
    dependencies: {apis: apisMap}
});

const persistConfig = {
    key: PERSIST_DATA_LOCAL_STORAGE_KEY,
    storage: storageSession,
    whitelist: ['persistData'],
    stateReconciler: autoMergeLevel1
};
const persistedReducer = persistReducer(persistConfig, rootReducer);
const composeEnhancers = window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__ || compose;

const configuredStore = configureStore();
const store = configuredStore.newStore;
const persistor = configuredStore.persistor;

function middleWareLogger(store) {
    return (next) => (action) => {
        if (action.type === 'BATCHING_REDUCER.BATCH') {
            const { payload } = action;

            if (payload && payload.length) {
                payload.forEach((action) => {
                    console.log(store.getState().entityWindow.settings[ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]);
                    console.log(next(action));
                    console.log(store.getState().entityWindow.settings[ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM]);
                });
            }
        }
        //console.log('I am error', action);
        return next(action);
    };
}

function configureStore() {
    const newStore = createStore(
        persistedReducer,
        composeEnhancers(
            applyMiddleware(
                // (state) => (next) => (action) => {
                //     console.log('actions dispatched', {action, state: state.getState()})
                //     return next(action);
                // },
                epicMiddleware,
                errorHandlingMiddleware,
                createNotificationMiddleware(notificationCallbacks, notificationConnection)
                //middleWareLogger
                // TODO: hook this to store when we want to start using user analytics
                // userAnalyticsMiddlewareCreator(trackedActions, trackedActionProvider)
            )
        )
    );

    const persistor = persistStore(newStore, null, () => loadUser(newStore, browserHistory.location.pathname, window.localStorage));
    epicMiddleware.run(rootEpic);

    return {newStore, persistor};
}

export {persistor};
export default store;