import Base<PERSON>pi from './base.api';
import RxPhoenixHttp from './RxPhoenixHttp/RxPhoenixHttp';
import { modifyResponseMapPipe, simpleMapPipe } from './pipes.api';
import { WORKSPACES_URL, IDWorkspacesUrl, WORKSPACES_MOST_RECENTLY_USED_URL, IDMostRecentlyUsedUrl } from '../constants';
import { getWorkspaceModificationMapFunction } from '../utils/workspaceUtils';

export default class WorkspacesApi extends BaseApi {
    constructor(pipe = simpleMapPipe){
        super(new RxPhoenixHttp(), pipe);
    }

    getWorkspace$(id){
        return this.http.get(IDWorkspacesUrl(id), id).pipe(modifyResponseMapPipe(getWorkspaceModificationMapFunction));
    }

    getWorkspaces$(groupByType, wsViewTypeId = ''){
        return this.http.get(WORKSPACES_URL, this.getQueryParams({groupByType: groupByType.toString(), wsviewtypeId: wsViewTypeId})).pipe(this.pipe);
    }

    getMostRecentlyUsed$(getWorkspaceDetail){
        const responsePipe = this.pipe;
        return this.http.get(WORKSPACES_MOST_RECENTLY_USED_URL, this.getQueryParams({getWorkspaceDetail: getWorkspaceDetail.toString()})).pipe(responsePipe);
    }

    addMostRecentlyUsed$(id){
        return this.http.put(IDMostRecentlyUsedUrl(id), id).pipe(this.pipe);
    }
    
    insertWorkspace$(value){
        return this.http.post(WORKSPACES_URL, JSON.stringify(value)).pipe(this.pipe);
    }

    deleteWorkspace$(id){
        return this.http.delete(IDWorkspacesUrl(id), id).pipe(this.pipe);
    }

    patchWorkspace$(id, data){
        return this.http.patch(IDWorkspacesUrl(id), id, data).pipe(this.pipe);
    }
}
