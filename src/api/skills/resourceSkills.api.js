import RxPhoenixHttp from '../RxPhoenixHttp/RxPhoenixHttp';
import BaseApi from '../base.api';
import { simpleMapPipe } from '../pipes.api';
import { ResourceSkillsByIdUrl, ResourceSkillsRecommendationByIdUrl, ResourceSkillsApprovalsPendingByIdUrl, ResourceSkillsApprovalsHistoryByIdUrl, FilteredResourceSkillsUrl, SkillPreferenceUrl } from '../../constants';
import { getSkillExpiryFieldSelector } from '../../selectors/skillExpirySelector';
import { AuthorizedResourceSkills } from '../../constants/globalConsts';
import { of } from 'rxjs';

const toServerSkill = (({ id, levelId, fields = [], skillPreference, isIgnored = false }) => {
    const skillExpiryFieldId = getSkillExpiryFieldSelector().baseSkillExpiryField.id;

    return {
        resourceskill_skill_guid: id,
        resourceskill_skilllevel_guid: levelId,
        // filter skill expiry field from fields
        fieldList: fields.filter(field => field.fieldId !== skillExpiryFieldId),
        resourceskill_skillpreference: skillPreference,
        // get skill expiry date from fields
        resourceskill_skillexpirydate: fields.find(field => field.fieldId === skillExpiryFieldId)?.fieldValue,
        resourceskill_isignored: isIgnored
    };
});

export default class ResourceSkillsApi extends BaseApi {
    constructor(pipe = simpleMapPipe) {
        super(new RxPhoenixHttp(), pipe);
    }

    getResourceSkills$(resourceId) {
        return this.http.get(ResourceSkillsByIdUrl(resourceId)).pipe(this.pipe);
    }

    getAutoCompleteSkills$(sectionId, searchTerm, maxResults, filterByResourcePermission) {
        let queryParams = this.getQueryParams({ searchTerm: encodeURIComponent(searchTerm), section: sectionId, maxResults, filterByResourcePermission });

        return this.http.post(FilteredResourceSkillsUrl, '', queryParams).pipe(this.pipe);
    }

    updateResourceSkills$(resourceId, skills) {
        return this.http.post(ResourceSkillsByIdUrl(resourceId), skills.map(toServerSkill)).pipe(this.pipe);
    }

    getSkillPreferences$() {
        return this.http.get(SkillPreferenceUrl).pipe(this.pipe);
    }

    getResourceSkillsRecommendations$(resourceId) {
        return this.http.get(ResourceSkillsRecommendationByIdUrl(resourceId)).pipe(this.pipe);
    }

    getResourceSkillsApprovalsPending$(resourceId) {
        return this.http.get(ResourceSkillsApprovalsPendingByIdUrl(resourceId)).pipe(this.pipe);
    }

    /**
     * The method is used to get the skill approval history.
     * @param {string} resourceId Represent the userId.
     * @param {number} from Represent the count from where next set of record should be considered
     * @param {number} count Set of record that should be fetched.
     * @param {string} sortBy Represent the way record should be sorted 'Ascending' or 'Descending'
     * @returns {Observable} Returns skill approval history.
     */
    getResourceSkillsApprovalsHistory$(resourceId, from, count, sortBy) {
        // If the API url changes then should be changed.
        const queryParams = `id=${resourceId}&from=${from}&count=${count}&sortby=${sortBy}`;

        return this.http.get(ResourceSkillsApprovalsHistoryByIdUrl, queryParams).pipe(this.pipe);
    }

    /**
     * The method is used get the authorized resource skills.
     * @returns {Observable<import('../../types/authorizeResourceSkillDTO.jsdoc').AuthorizeResourceSkillDTO[]>}
     */
    getAuthorizeResourceSkills$() {
        return this.http.get(AuthorizedResourceSkills).pipe(this.pipe);
    }

    /**
     * Resends the approval request for resource skills.
     * This is a sample method which simulates the operation by returning an Observable of `true` for now.
     * In a real implementation, this would likely perform an HTTP request.
     */
    resendApprovalRequest$(resourceId) {
        return of(true);
    }
}
