import { NOTIFICATION_EMAIL_SETTINGS_URL, NOTIFICATION_HISTORY_URL, UnconfirmedBookingsNotificationSettings, UNREAD_NOTIFICATION_COUNT_URL } from '../constants/globalConsts';
import { simpleMapPipe, loadMoreNotificationPipe } from './pipes.api';
import RxPhoenixHttp from './RxPhoenixHttp/RxPhoenixHttp';

const http = new RxPhoenixHttp();

export const getNotificationHistory$ = (queryParams) => {
    return http.get(NOTIFICATION_HISTORY_URL, queryParams).pipe(loadMoreNotificationPipe);
};

export const updateNotificationReadUnreadStatus$ = (payload) => {
    return http.patch(`${NOTIFICATION_HISTORY_URL}/update`, '', payload).pipe(simpleMapPipe);
};

export const batchUpdateNotificationMarkAllRead$ = (payload) => {
    return http.put(`${NOTIFICATION_HISTORY_URL}/BatchUpdate`, null, payload).pipe(simpleMapPipe);
};

export const deleteNotifications$ = (payload) => {
    return http.deleteMultiple(NOTIFICATION_HISTORY_URL, payload).pipe(simpleMapPipe);
};

export const getNotificationDataUnreadCount$ = () => {
    return http.get(UNREAD_NOTIFICATION_COUNT_URL).pipe(simpleMapPipe);
};

export const updateNotificationsEmailSettingsForUser$ = () => {
    return http.post(NOTIFICATION_EMAIL_SETTINGS_URL, {}, '').pipe(simpleMapPipe);
};

export const getUnconfirmedBookingsNotificationSettings$ = () => {
    return http.get(UnconfirmedBookingsNotificationSettings).pipe(simpleMapPipe);
};

export const updateUnconfirmedBookingsNotificationSettings$ = (payload) => {
    return http.put(UnconfirmedBookingsNotificationSettings, null, payload).pipe(simpleMapPipe);
};