import { SkillCategoriesUrl, SkillsUrl, DEPARTMENT_API_URL, DIVISION_API_URL, SkillEntityTypesUrl, FilteredTableAccessUrl, TABLE_NAMES } from '../constants';
/** @typedef {import('../types/configurableSelectAPIConfig.jsdoc').ConfigurableSelectAPIConfig} ConfigurableSelectAPIConfig */
/**
 * Configuration for different API endpoints and their data structure
 * @type {Object.<string, ConfigurableSelectAPIConfig>}
 */
export const Configurable_Select_API_Config = {
    categories: {
        endpoint: SkillCategoriesUrl,
        valueKey: 'categoryDescription',
        labelKey: 'categoryDescription',
        /**
         * @param {Array} data - Raw API response
         * @returns {Array} Transformed categories array
         */
        transformResponse: (data) => data // if API returns direct array
    },
    skills: {
        endpoint: SkillsUrl,
        valueKey: 'skillDescription',
        labelKey: 'skillDescription',
        /**
         * @param {Array} data - Raw API response
         * @returns {Array} Transformed skills array
         */
        transformResponse: (data) => data

    },
    departments: {
        endpoint: DEPARTMENT_API_URL,
        valueKey: 'department_description',
        labelKey: 'department_description',
        /**
         * @param {Array} data - Raw API response
         * @returns {Array} Transformed departments array
         */
        transformResponse: (data) => data // if API returns direct array
    },
    divisions: {
        endpoint: DIVISION_API_URL,
        valueKey: 'division_description',
        labelKey: 'division_description',
        /**
         * @param {Array} data - Raw API response
         * @returns {Array} Transformed divisions array
         */
        transformResponse: (data) => data // if API returns direct array
    },
    skillEntityTypes: {
        endpoint: SkillEntityTypesUrl,
        valueKey: 'description',
        labelKey: 'description',
        /**
         * @param {Array} data - Raw API response
         * @returns {Array} Transformed divisions array
         */
        transformResponse: (data) => data // if API returns direct array
    },
    resource: {
        endpoint: FilteredTableAccessUrl(TABLE_NAMES.RESOURCE),
        valueKey: 'id',
        labelKey: 'value',
        /**
        * @param {Array} data - Raw API response
        * @returns {Array} Transformed divisions array
        */
        transformResponse: (data) => data
    }
};