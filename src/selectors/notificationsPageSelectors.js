import { createSelector } from 'reselect';
import { NOTIFICATION_REQUEST_KEYS, NOTIFICATION_TIME_ALLOCATION_TYPES, NOTIFICATIONS_PAGE_ALIAS } from '../constants/notificationsPageConsts';
import { formatLocalDate, getDisplayLongDateTimeFormat, parseLocalDate } from '../utils/dateUtils';
import { getNotificationEntityName, getNotificationEventMessage, getNotificationEventMessageDetails, getSortedNotificationsByDate, isOldNotificationPayloadStructure, isValidNotificationsTabExists } from '../utils/notificationsPageUtils';
import { getTranslationsSelector } from './internationalizationSelectors';
import { isEmpty } from 'lodash';
import { DISPLAY_DATE_TIME_FORMATS } from '../constants/globalConsts';

export const getNotificationsActiveTabSelector = createSelector(
    (state) => state.notificationsPage.pageState,
    (pageState) => (notificationStaticMessagesTabs) => {
        const { params } = pageState;
        const { notificationHistoryLabel } = notificationStaticMessagesTabs;
        const { tab = notificationHistoryLabel } = params || {};
        const validTab = isValidNotificationsTabExists(tab, notificationStaticMessagesTabs);

        return validTab ? validTab.tabName : notificationHistoryLabel;
    }
);

export const getNotificationsHistory = (state) => {
    const notificationsPage = state.notificationsPage || {};
    const notificationsHistory = notificationsPage.notificationsHistory || {
        notificationsHistoryData: [],
        ids: [],
        isLoading: true,
        isLoadingMoreInProgress: false,
        isAllNotificationsRead: true,
        isAllRequestNotificationRead:true,
        notificationsTimeAllocationTypes: NOTIFICATION_TIME_ALLOCATION_TYPES
    };

    // Notification Request Tab data
    const notificationsRequestData = notificationsHistory.notificationsHistoryData
        .filter(x => NOTIFICATION_REQUEST_KEYS.includes(x.notificationPreferenceKey));
    const isAllRequestNotificationRead = notificationsRequestData.every(x => x.notificationRead);

    return { ...notificationsHistory, notificationsRequestData,isAllRequestNotificationRead };
};

export const getNotificationsTimeAllocationTypes = createSelector(
    getNotificationsHistory,
    (notificationHistory) => {
        return notificationHistory.notificationsTimeAllocationTypes;
    }
);

export const getNotificationsHistoryData = createSelector(
    getNotificationsHistory,
    (notificationHistory) => {
        return getSortedNotificationsByDate(notificationHistory.notificationsHistoryData);
    }
);

export const getUnconfirmedBookingsNotification = createSelector(
    getNotificationsHistory,
    (notificationHistory) => {
        const { unconfirmedBookingsData } = notificationHistory;

        return unconfirmedBookingsData || false;
    }
);

export const getNotificationTemplate = createSelector(
    getNotificationsHistoryData,
    getNotificationsTimeAllocationTypes,
    getUnconfirmedBookingsNotification,
    (notificationHistoryData, notificationsTimeAllocationTypes, unconfirmedBookingsData) => (staticMessages) => {
        const notificationHistoryList = [];
        notificationHistoryData.forEach((event) => {
            const { guid, notificationDescription = {}, notificationRead = false, notificationDate, notificationPreferenceKey } = event;
            const oldNotificactionPayloadStructure = isOldNotificationPayloadStructure(event);
            const entityName = oldNotificactionPayloadStructure ? notificationDescription.EntityName : notificationDescription.entityName;
            const notificationMessage = getNotificationEventMessage(event, staticMessages, notificationsTimeAllocationTypes, unconfirmedBookingsData);
            const notificationMessageDetails = getNotificationEventMessageDetails(event, staticMessages);
            const notificationLocalDate = parseLocalDate(notificationDate);
            notificationHistoryList.push({
                notificationMessage,
                notificationMessageDetails,
                entityName: !isEmpty(entityName) ? entityName.toLowerCase() : '',
                notificationDate: formatLocalDate(notificationLocalDate, getDisplayLongDateTimeFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR, DISPLAY_DATE_TIME_FORMATS._24_HOURS_SHORT_TIME)),
                isMarkedRead: notificationRead,
                notificationId: guid,
                notificationPreferenceKey
            });
        });

        return notificationHistoryList;
    }
);

export const getStaticMessagesBySubSectionSelector = createSelector(
    (state, translationSubSectionName) =>{
        const translationIdsArray = [translationSubSectionName];
        const translationConfig = { sectionName: NOTIFICATIONS_PAGE_ALIAS, idsArray: translationIdsArray };
        const translation = getTranslationsSelector(state, translationConfig)[translationSubSectionName];

        return translation ? translation : {};
    },
    (messages) => messages
);

export const getNotificationsEntityIds = createSelector(
    (result) => (notificationType = '') => {
        const entityIds = [];
        result.forEach(item => {
            const { notificationDescription = {} } = item;
            const oldNotificactionPayloadStructure = isOldNotificationPayloadStructure(item);
            const entityName = oldNotificactionPayloadStructure ? notificationDescription.EntityName : notificationDescription.entityName;
            const entityId = oldNotificactionPayloadStructure ? notificationDescription.EntityGuid : notificationDescription[`${getNotificationEntityName(entityName)}_guid`];

            if (notificationType.toLowerCase() === entityName.toLowerCase()) {
                entityIds.push(entityId);
            }
        });

        return entityIds;
    },
    (entityIds) => entityIds
);

export const getNotificationBadgeStatus = createSelector(
    getNotificationsHistory,
    (notificationHistory) => {
        const { isAllNotificationsRead } = notificationHistory;

        return !isAllNotificationsRead;
    }
);

export const getNotificationByIdSelector = createSelector(
    getNotificationsHistoryData,
    (notificationHistoryData = []) => (notificationId) => {
        return notificationHistoryData.find(notification => notification.guid === notificationId) || {};
    }
);
