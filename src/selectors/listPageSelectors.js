import { createSelector } from "reselect";

export const getActiveListViewSelector = createSelector(
    state => state.listPage,
    ({ activeListView }) => activeListView
);

export const getWorkspaceViewTypeSelector = createSelector(
    state => state.listPage,
    ({ wsViewType }) => wsViewType
);

export const getPageFiltersSelector = createSelector(
    state => state,
    (state) => (pageAlias, tableName) => {
        if (state && state[pageAlias] && state[pageAlias].filters && state[pageAlias].filters[tableName]) {
            const filterState = state[pageAlias].filters[tableName];
            return {
                [tableName]: {
                    hidden: filterState.hidden,
                    selection: filterState.selection,
                    baseFilter: {
                        applied: filterState.baseFilter.applied,
                        selectedBaseFilter: filterState.baseFilter.selectedBaseFilter,
                    }
                }
            };
        }
        return {};
    }
)