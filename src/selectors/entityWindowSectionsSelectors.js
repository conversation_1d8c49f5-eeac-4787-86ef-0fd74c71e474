import { createSelector } from 'reselect';
import * as rsSelectors from './resourceSkillsSelectors';
import * as ewRoleGroupSelectors from './roleGroupSelectors';
import * as rolesSelectors from './roleRequestsSelector';
import { intersection, intersectionBy, mapValues } from 'lodash';
import { EDIT_ALL_ENTITY_ID, ENTITY_WINDOW_MODULES, ENTITY_WINDOW_NON_COLLAPSIBLE_SECTIONS_MODULES, ENTITY_WINDOW_NO_TITLE_SECTIONS_PER_MODULE, ENTITY_WINDOW_OPERATIONS, ENTITY_WINDOW_SECTION_KEYS, ENTITY_WINDOW_SECTION_TYPES } from '../constants/entityWindowConsts';
import { getFieldInfo, getFieldInfoSelector } from './tableStructureSelectors';
import { getEntityMessageById, resourceChargeRateAndDiaryMessage } from '../utils/messages';
import { getTranslationsSelector } from './internationalizationSelectors';
import { getLicenseValuesByKeySelector } from './commonSelectors';
import { checkEWConditions, getIsSectionVisibleByModuleName } from '../utils/entityWindowUtils';
import { filterSectionField, getIsSectionHasWarning, modifySectionField } from '../utils/entityWindowSectionsUtils';
import { SCOPE } from '../constants/messageConsts';
import { getIsMultipleAssigneesEnabled } from './functionalityConfigurationSelectors';
import { disableFieldConditionsMap } from '../utils/entityWindowConditionsUtils';
import { buildCommonEntityAliasesPlaceholdersSelector, populateStringTemplates } from '../utils/translationUtils';
import { getFieldsForMarketplaceApplyDateSectionMessageSelector } from './entityWindowMessagesSelectors';
import { BOOKING_CHARGE_RATES_VIEW_MODE_FIELDS_ROW, BOOKING_CHARGEMODE_SECTION_FIELD, BOOKING_OVERRIDDEN_CHARGERATE_GUID, FIELD_DATA_ACCESS_LEVEL, ROLEREQUEST_CHARGEMODE_SECTION_FIELD, ROLEREQUEST_ESTIMATE_CHARGEMODE_SECTION_FIELD, ROLEREQUEST_FIELDS } from '../constants/fieldConsts';
import { getFunctionalAccessSelector } from './applicationFnasSelectors';
import { getCMeColourValuesSelector } from './plannerPageSelectors';
import { FEATURE_FLAGS, TABLE_NAMES } from '../constants/globalConsts';
import { resourceChargeRateField, resourceDiaryGroupField } from '../state/entityWindow/fieldsConfig';
import { stringReplacePlaceholders } from '../utils/commonUtils';

const getShouldIncludeFieldSelector = createSelector(
    state => getIsMultipleAssigneesEnabled(state),
    (useMultipleAssignees) => (field) => {
        const { excludeFieldConditions } = field;
        let disableEWCheck = null;

        if (excludeFieldConditions) {
            const context = { useMultipleAssignees };
            disableEWCheck = checkEWConditions(excludeFieldConditions, disableFieldConditionsMap, context);
        }

        return disableEWCheck;
    }
);

export const getEntityWindowSectionMessagesSelector = createSelector(
    state => state.internationalization,
    buildCommonEntityAliasesPlaceholdersSelector,
    state => getFieldsForMarketplaceApplyDateSectionMessageSelector(state),
    (internationalization, commonEntityAliasesPlaceholders, marketplaceFields) => {
        const messages = getTranslationsSelector({ internationalization }, { sectionName: 'entityWindow', idsArray: ['messages'] }).messages || {};
        const populatedStrings = populateStringTemplates(messages,
            {
                ...commonEntityAliasesPlaceholders,
                applyDate: marketplaceFields.applyDate
            });

        return populatedStrings;
    }
);

const getFieldAccessLevel = (tableName, fieldName, getFieldInfo) => getFieldInfo(tableName, fieldName);

export const getFieldAccessLevelSelector = createSelector(
    state => getFieldInfoSelector(state),
    (getFieldInfo) => (tableName, fieldName) => getFieldAccessLevel(tableName, fieldName, getFieldInfo)
);

const getSectionFields = (section, tableStructure, tableName, activeMessages, entity, entityId, moduleName, operation, staticMessages, getShouldIncludeField) => {
    const fields = (section.fields || []).reduce((accumulator, field) => {
        let fieldResult = { ...field };
        const { name, actualFieldName, table } = fieldResult;
        const fieldName = actualFieldName ? actualFieldName : name;
        const fieldInfo = getFieldInfo(tableStructure, table, fieldName);
        const reducerFieldMessages = intersectionBy(activeMessages, field.fieldMessages, 'id')
            .filter(({ scope }) => scope === SCOPE.ENTITY_FIELD)
            .map((message) => message.text ? message : getEntityMessageById(tableName, message.id, staticMessages));

        fieldResult.fieldMessages = [...reducerFieldMessages];

        const getFieldInfoWrapper = (fieldResult) => getFieldInfo(tableStructure, table, fieldResult);
        let sectionField = filterSectionField(fieldResult, moduleName, operation, fieldInfo, getFieldInfoWrapper, section, entityId, entity, table, getShouldIncludeField);
        sectionField = sectionField != null
            ? modifySectionField(sectionField, entity, moduleName)
            : sectionField;

        sectionField && accumulator.push(sectionField);

        return accumulator;
    }, []);

    return fields;
};

const filterHiddenFields = (field, tableName, getFieldInfo) => {
    let fieldAccessLevel = null;

    if (field.name === BOOKING_CHARGEMODE_SECTION_FIELD) {
        fieldAccessLevel = (getFieldInfo(tableName, BOOKING_OVERRIDDEN_CHARGERATE_GUID));
    } else if (field.name === ROLEREQUEST_CHARGEMODE_SECTION_FIELD || field.name === BOOKING_CHARGE_RATES_VIEW_MODE_FIELDS_ROW || field.name === ROLEREQUEST_ESTIMATE_CHARGEMODE_SECTION_FIELD) {
        fieldAccessLevel = (getFieldInfo(tableName, ROLEREQUEST_FIELDS.CHARGE_RATE_GUID));
    } else {
        fieldAccessLevel = (getFieldInfo(tableName, field.name));
    }

    return !fieldAccessLevel || !fieldAccessLevel.accessLevel ? true : fieldAccessLevel.accessLevel.toLowerCase() !== FIELD_DATA_ACCESS_LEVEL.HIDDEN.toLowerCase();
};

export function createGetEntityWindowSectionsSelector() {
    return createSelector(
        state => state.entityWindow,
        state => state.settings,
        state => state.sections,
        state => state.moduleName,
        // Formated like this because of unit tests
        ((rsSelectors || {}).getGroupedResourceSkills || (() => { })),
        (getEntityWindowSectionMessagesSelector || (() => { })),
        ((ewRoleGroupSelectors || {}).getRoleGroups || (() => { })),
        //
        state => state.applicationSettings.tableStructure,
        ((rolesSelectors || {}).getRolesForRoleRequestGroupSelector || (() => { })),
        state => getTranslationsSelector(state, { sectionName: 'common' }),
        state => getLicenseValuesByKeySelector(state),
        state => getShouldIncludeFieldSelector(state),
        (state) => state.sectionsToHide,
        state => getFieldInfoSelector(state),
        state => getFunctionalAccessSelector(state),
        state => getCMeColourValuesSelector(state),
        state => state.getFeatureFlag,
        (entityWindow, settings, sections, moduleName, getGroupedResourceSkills, staticMessages, getRoleGroups, tableStructure, getRolesForRoleRequestGroupSelector, translationsMessages, getLicenseValuesByKey, getShouldIncludeField, sectionsToHide = [], getFieldInfo, getFunctionalAccess, getCMeColourValues, getFeatureFlag) => {
            const { tableName, operation, uiEntity, entityId, messages: activeMessages, entity } = entityWindow;
            const isNonCollapsibleSectionsModule = ENTITY_WINDOW_NON_COLLAPSIBLE_SECTIONS_MODULES.includes(moduleName);
            const moduleHideTitleSections = ENTITY_WINDOW_NO_TITLE_SECTIONS_PER_MODULE[moduleName] || [];
            const cMeColourValues = getCMeColourValues(tableName, entityId);
            const isEnabledBudgetHoursAndRevenue = getFeatureFlag ? getFeatureFlag(FEATURE_FLAGS.BUDGET_HOURS_AND_REVENUE) : false;
            const selectedFieldsKeys = settings?.[moduleName]?.selectedFieldsKeys || [];
            const chargeRateInfo = getFieldInfo(TABLE_NAMES.RESOURCE, resourceChargeRateField.name);
            const diaryGroupInfo = getFieldInfo(TABLE_NAMES.RESOURCE, resourceDiaryGroupField.name);

            return (sections[tableName] || [])
                .filter((section) => { //TODO: this does not filter properly as we may have some other conditions for hiding sections
                    const isLicenseValid = (getLicenseValuesByKey(section.licenseKey) || {}).subscribedCount !== false;
                    const isModuleVisible = getIsSectionVisibleByModuleName(moduleName, section);
                    const hasFunctionalAccess = getFunctionalAccess(section.functionalAccessName) || false;
                    let toggleSectionForJobBudgetHoursAndRevenueFeature = false;

                    //This condition is to be removed when isEnabledBudgetHoursAndRevenue feature flag is removed.
                    if (tableName === TABLE_NAMES.JOB) {
                        if (section.key === ENTITY_WINDOW_SECTION_KEYS.BUDGET_DETAILS || section.key === ENTITY_WINDOW_SECTION_KEYS.BUDGET) {
                            toggleSectionForJobBudgetHoursAndRevenueFeature = isEnabledBudgetHoursAndRevenue;
                        }
                        if (section.key === ENTITY_WINDOW_SECTION_KEYS.TIME_AND_FINANCIALS) {
                            toggleSectionForJobBudgetHoursAndRevenueFeature = !isEnabledBudgetHoursAndRevenue;
                        }
                    }

                    return isLicenseValid && isModuleVisible && hasFunctionalAccess && !toggleSectionForJobBudgetHoursAndRevenueFeature;
                })
                .reduce((accumulator, section) => {
                    const shouldHideSectionTitle = moduleHideTitleSections.includes(section.key);
                    const result = {
                        ...section,
                        ...(isNonCollapsibleSectionsModule && { collapsed: null }),
                        ...(shouldHideSectionTitle && { shouldDisplayTitle: false })
                    };

                    result.fields = getSectionFields(section, tableStructure, tableName, activeMessages, entity, entityId, moduleName, operation, staticMessages, getShouldIncludeField).filter(field => filterHiddenFields(field, tableName, getFieldInfo));

                    const reducerDrivenMessages = intersectionBy(activeMessages, section.messages, 'id')
                        .filter(({ scope }) => scope === SCOPE.ENTITY_SECTION)
                        .map((message) => message.text ? message : getEntityMessageById(tableName, message.id, staticMessages));

                    const componentDrivenMessages = Object.values(section.messages || {})
                        .filter(message => message.componentDrivenMessage == true && message.scope === SCOPE.ENTITY_SECTION);

                    let additionalSectionMessages = [];

                    if (
                        tableName === TABLE_NAMES.RESOURCE &&
                        entityId === EDIT_ALL_ENTITY_ID &&
                        moduleName === ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL &&
                        operation === ENTITY_WINDOW_OPERATIONS.EDIT &&
                        section.key === ENTITY_WINDOW_SECTION_KEYS.EMPLOYMENT_DETAILS &&
                        Array.isArray(selectedFieldsKeys) &&
                        (selectedFieldsKeys.includes(resourceChargeRateField.name) || selectedFieldsKeys.includes(resourceDiaryGroupField.name))
                    ) {
                        const translatedAdditionalSectionMessage = getEntityMessageById(TABLE_NAMES.RESOURCE, resourceChargeRateAndDiaryMessage.id, staticMessages);

                        if (translatedAdditionalSectionMessage?.text) {
                            translatedAdditionalSectionMessage.text = stringReplacePlaceholders(translatedAdditionalSectionMessage.text, { chargeRateAlias: chargeRateInfo.alias, diaryGroupAlias: diaryGroupInfo.alias });
                            additionalSectionMessages.push(translatedAdditionalSectionMessage);
                        }
                    }

                    result.messages = [...reducerDrivenMessages, ...componentDrivenMessages, ...additionalSectionMessages];

                    const erroredFields = [];
                    mapValues(uiEntity, field => {
                        const { errors } = field;
                        if (Array.isArray(errors) && errors.length > 0) {
                            errors.forEach(({ field }) => erroredFields.push(field));
                        }
                    });

                    const sectionsFields = (result.fields || []).map((field) => field.name);
                    result.hasErrors = intersection(erroredFields, sectionsFields).length > 0;
                    result.shouldDisplayWarning = getIsSectionHasWarning(section, uiEntity, moduleName, operation);

                    if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.TAB_SECTION_TYPE) {
                        const { subSections } = result;

                        (subSections || []).forEach((subSection, index) => {
                            result.subSections[index].fields = getSectionFields(
                                subSection,
                                tableStructure,
                                tableName,
                                activeMessages,
                                entity,
                                entityId,
                                moduleName,
                                operation,
                                staticMessages,
                                getShouldIncludeField
                            ).filter(field => filterHiddenFields(field, tableName, getFieldInfo));
                        });

                    }

                    if (isEnabledBudgetHoursAndRevenue) {
                        if (result.subSectionItems) {
                            const { subSectionItems } = result;
                            (subSectionItems || []).forEach((subSection, index) => {
                                result.subSectionItems[index].fields = getSectionFields(
                                    subSection,
                                    tableStructure,
                                    tableName,
                                    activeMessages,
                                    entity,
                                    entityId,
                                    moduleName,
                                    operation,
                                    staticMessages,
                                    getShouldIncludeField
                                ).filter(field => filterHiddenFields(field, tableName, getFieldInfo));
                            });
                        }
                    }

                    if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.SKILLS_LIST_SECTION_TYPE) {
                        result.skills = getGroupedResourceSkills(entityId) || {};
                    }

                    if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.CME_SECTION_TYPE && !cMeColourValues.hasCMeData) {
                        result.fields = [];
                    }

                    if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.ROLE_GROUP_LIST_SECTION_TYPE) {
                        result.roleGroups = getRoleGroups(entityId) || [];
                    }

                    if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.ROLE_LIST_SECTION_TYPE) {
                        result.roles = getRolesForRoleRequestGroupSelector(entityId) || [];
                    }

                    if (section.sectionType === ENTITY_WINDOW_SECTION_TYPES.OVERLAPPING_BOOKING_SECTION_TYPE) {
                        const overlappingBookingItems = rolesSelectors.getOverlappingBookingItems(uiEntity);
                        const overlappingRolerequestItems = rolesSelectors.getOverlappingRolerequestItems(uiEntity);
                        const hasOverlappingBookings = overlappingBookingItems && overlappingBookingItems.length > 0;
                        const hasOverlappingRolerequest = overlappingRolerequestItems && overlappingRolerequestItems.length > 0;

                        if (!hasOverlappingBookings && !hasOverlappingRolerequest) {
                            result.title = translationsMessages.noOverlappingBookings;
                            result.collapsed = null;
                        }
                        result.overlappingBookings = [...overlappingBookingItems, ...overlappingRolerequestItems];
                    }

                    if (sectionsToHide.includes(section.key)) {
                        if (result.subSections) {
                            result.subSections = result.subSections.filter(subSection => subSection.fields.length > 0);
                        } else {
                            result.subSections = [];
                        }

                        if (isEnabledBudgetHoursAndRevenue) {
                            if (result.subSectionItems) {
                                result.subSectionItems = result.subSectionItems.filter(subSection => subSection.fields.length > 0);
                            } else {
                                result.subSectionItems = [];
                            }
                        }

                        if (result.fields && result.fields.length <= 0
                            && result.subSections && result.subSections.length <= 0
                            && result.subSectionItems && result.subSectionItems.length <= 0) {
                            return accumulator;
                        }
                    }

                    return [...accumulator, result];
                }, []);
        }
    );
}