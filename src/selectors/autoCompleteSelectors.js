import { createSelector } from 'reselect';
import { getFieldInfoSelector } from './tableStructureSelectors';
import { getCalcFieldWithParams } from '../utils/plannerQuerySelectionUtils';
import { TABLE_NAMES } from '../constants';
import { BOOKING_RESOURCE_GUID, BOOKING_RESOURCE_GUIDS, RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMERED, RESOURCE_CMEYELLOW, RESOURCE_USERSTATUS } from '../constants/fieldConsts';
import { isLookupField } from '../utils/tableStructureUtils';

export function isInAutoCompleteState(autoComplete = {}, fieldName) {
    const isInAutoCompleteState =
        autoComplete && autoComplete[fieldName] &&
        autoComplete[fieldName].isAutoCompleting;

    return isInAutoCompleteState;
}

export function createEntityWindowAutoCompleteGetAdditionalFieldsSelector() {
    return createSelector(
        (state) => getFieldInfoSelector(state),
        (state, entity) => entity,
        (getFieldInfo, entity) => (currentFieldInfo) => {
            const additionalFields = [];

            if (currentFieldInfo.name === BOOKING_RESOURCE_GUID || currentFieldInfo.name === BOOKING_RESOURCE_GUIDS) {
                const { booking_start: startDate, booking_end: endDate } = entity;
                const parameters = {
                    startDate,
                    endDate
                };

                if (startDate != null && endDate != null) {
                    additionalFields.push(getCalcFieldWithParams(getFieldInfo(TABLE_NAMES.RESOURCE, 'availability'), parameters));
                    additionalFields.push(getCalcFieldWithParams(getFieldInfo(TABLE_NAMES.RESOURCE, 'available_time'), parameters));
                }
            }

            const { linkTable } = currentFieldInfo;

            if (linkTable === TABLE_NAMES.RESOURCE && isLookupField(linkTable, currentFieldInfo)) {
                additionalFields.push({ fieldName: RESOURCE_USERSTATUS }, { fieldName: RESOURCE_CMERED }, { fieldName: RESOURCE_CMEBLUE }, { fieldName: RESOURCE_CMEGREEN }, { fieldName: RESOURCE_CMEYELLOW });
            }

            return additionalFields;
        }
    );
}