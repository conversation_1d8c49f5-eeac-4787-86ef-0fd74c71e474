import {
    getSkillSectionId, getSectionName, getSkillData, getSectionLevelField,
    getSectionFieldInfos, getSkillInfoSelector, getSkillFieldInfoSelector
} from './skillStructureSelectors';
import { createSelector } from 'reselect';
import { createUISkill } from '../reducers/resourceSkillsChangesReducer';
import { isEmptyObject } from '../utils/commonUtils';
import { FIELD_DATA_TYPES } from '../constants';
import { orderBy } from 'lodash';

export const getSortedResourceSkills = (skills = []) => {
    return orderBy(skills, [skill => skill.level.value, skill => ((skill || {}).name || '').toLowerCase()], ['desc', 'asc']);
};

export const getResourceSkillsSelector = createSelector(
    state => state.resourceSkills,
    (resourceSkills) =>
        (id) => getResourceSkills(id, resourceSkills)
);

export const getResourceSkillsChangesSelector = createSelector(
    getResourceSkillsSelector,
    (getResourceSkills) =>
        (resourceId) => getResourceSkills(resourceId).skillsChanges
);

export const getResourceSkills = (id, resourceSkills) => {
    return resourceSkills[id] || {
        skills: {
            map: {},
            orderedKeys: []
        },
        skillsChanges: {
            updates: {
                map: {},
                orderedKeys: []
            },
            inserts: {
                map: {},
                orderedKeys: []
            },
            deletes: {
                map: {},
                orderedKeys: []
            }
        }
    };
};

const getResourceSkillLevel = createSelector(
    getResourceSkillsSelector,
    getSectionLevelField,
    (getResourceSkills, getSectionLevelField) =>
        (resourceId, resourceSkillId, skillSectionId) => {
            const {
                levelId
            } = getResourceSkills(resourceId).skills.map[resourceSkillId] || {};

            const levelField = getSectionLevelField(skillSectionId);
            const { levels = [] } = levelField;

            let levelInfo = {};

            if (levels.length) {
                levelInfo = {
                    ...levelField,
                    ...levels.find(({ id }) => id === levelId) || {}
                };
            }

            return levelInfo;
        }
);

const getResourceSkillFields = createSelector(
    getResourceSkillsSelector,
    getSectionFieldInfos,
    getSkillFieldInfoSelector,
    (getResourceSkills, getSectionFields, getSkillFieldInfo) =>
        (resourceId, resourceSkillId, skillSectionId) => {
            const {
                fields = []
            } = getResourceSkills(resourceId).skills.map[resourceSkillId] || {};

            const sectionFields = getSectionFields(skillSectionId) || {};

            return Object
                .keys(sectionFields)
                .map(id => {
                    const { fieldValue } = fields.find(f => f.fieldId === id) || {};
                    const fieldInfo = getSkillFieldInfo(id);

                    return {
                        ...sectionFields[id],
                        id,
                        value: fieldValue,
                        fieldInfo
                    };
                });
        }
);

export const getResourceSkillData = createSelector(
    getSkillSectionId,
    getSectionName,
    getSkillData,
    getResourceSkillLevel,
    getResourceSkillFields,
    (getSkillSectionId, getSectionName, getSkillData, getResourceSkillLevel, getResourceSkillFields) =>
        (resourceId, resourceSkillId) => {
            const skillSectionId = getSkillSectionId(resourceSkillId);

            return {
                ...getSkillData(resourceSkillId, skillSectionId),
                sectionName: getSectionName(skillSectionId),
                level: getResourceSkillLevel(resourceId, resourceSkillId, skillSectionId),
                fields: getResourceSkillFields(resourceId, resourceSkillId, skillSectionId)
            };
        }
);

export const getResourceSkillPreferenceSelector = createSelector(
    state => state.resourceSkills,
    (resourceSkills) =>
        (resourceId, skillId) => {
            const resourceSkill = getResourceSkills(resourceId, resourceSkills);
            const currentSkill = resourceSkill.skillsChanges.inserts.map[skillId] || resourceSkill.skillsChanges.updates.map[skillId] || {};
            if (currentSkill.skillPreference) {
                return currentSkill.skillPreference;
            }
            const existingSkill = resourceSkill.skills.map[skillId];

            return existingSkill ? existingSkill.skillPreference : 'NoPreference';
        }
);

export const getResourceSkillUiData = createSelector(
    getResourceSkillsChangesSelector,
    getResourceSkillData,
    getResourceSkillPreferenceSelector,
    (getSkillsChanges, getResourceSkillData, getResourceSkillPreference) =>
        (resourceId, skillId) => {
            return {
                ...getResourceSkillData(resourceId, skillId),
                removed: !!getSkillsChanges(resourceId).deletes.map[skillId],
                skillPreference: getResourceSkillPreference(resourceId, skillId) // Add skillPreference here
            };
        }
);

export const getResourceSkillsIds = createSelector(
    getResourceSkillsSelector,
    (getResourceSkills) => {
        const internalCache = {};

        return (resourceId) => {
            if (!(resourceId in internalCache)) {
                internalCache[resourceId] = getResourceSkills(resourceId).skills.orderedKeys;
            }

            return internalCache[resourceId];
        };
    }
);

export const getIgnoredResourceSkillsSelector = createSelector(
    getResourceSkillsSelector,
    (getResourceSkills) =>
        (resourceId) => (getResourceSkills(resourceId) || {}).ignoredRecommendations || []
);

export const resourceSkillsHasChangesSelector = createSelector(
    getResourceSkillsChangesSelector,
    getIgnoredResourceSkillsSelector,
    (getSkillsChanges, getIgnoredSkillsChanges) =>
        (resourceId) => {
            const {
                inserts,
                updates,
                deletes
            } = getSkillsChanges(resourceId);
            const ignoredChanges = getIgnoredSkillsChanges(resourceId);

            return !!Object.keys(inserts.map).length
                || !!Object.keys(updates.map).length
                || !!Object.keys(deletes.map).length
                || ignoredChanges.length;
        }
);

const fieldHasErrors = (fieldErrors = []) => {
    return fieldErrors.length > 0;
};

const skillsChangesMapHasErrors = (map) => {
    for (const skillId of Object.keys(map)) {
        const errors = (map[skillId] || {}).errors;

        if (!isEmptyObject(errors) && Object.keys(errors).some(fieldId => fieldHasErrors(errors[fieldId])))
            return true;
    }

    return false;
};

export const resourceSkillsChangesHasErrorsSelector = createSelector(
    getResourceSkillsChangesSelector,
    (getSkillsChanges) =>
        (resourceId) => {
            const {
                updates,
                inserts,
                deletes
            } = getSkillsChanges(resourceId);
            let updatesToVerify = {};

            Object.keys(updates.map).forEach((key) => {
                const markForDeletion = Object.keys(deletes.map[key] || {}).length;

                if (!markForDeletion) {
                    updatesToVerify[key] = updates.map[key];
                }
            });

            return skillsChangesMapHasErrors(updatesToVerify) || skillsChangesMapHasErrors(inserts.map);
        }
);

export const getResourceSkillsCanSaveSelector = createSelector(
    resourceSkillsHasChangesSelector,
    resourceSkillsChangesHasErrorsSelector,
    (resourceSkillsHasChanges, resourceSkillsChangesHasErrors) =>
        (resourceId) => {
            return resourceSkillsHasChanges(resourceId) && !resourceSkillsChangesHasErrors(resourceId); // add checking for mandatory fields with no value on Add skill
        }
);

export const mapResourceSkillFields = (skill, getFieldInfo) => {
    const { fields: skillFields, id, skillPreference } = skill;
    let fields = [];
    let levelId = null;

    Object.keys(skillFields || {})
        .filter(fieldId => skillFields[fieldId] != null)
        .map(fieldId => {
            const { id, name, dataType } = getFieldInfo(fieldId);

            if (dataType === FIELD_DATA_TYPES.SKILL_LEVEL) {
                levelId = skillFields[id];
            } else {
                fields.push({
                    fieldId: id,
                    fieldName: name,
                    fieldValue: skillFields[id]
                });
            }
        });

    return {
        id,
        fields,
        levelId,
        skillPreference
    };
};

export const mergeResourceSkillsSelector = createSelector(
    getResourceSkillsSelector,
    getSkillFieldInfoSelector,
    getSkillInfoSelector,
    (getResourceSkills, getFieldInfo, getSkillInfo) =>
        (resourceId) => {
            const {
                skills = {},
                skillsChanges = {}
            } = getResourceSkills(resourceId);

            let mergedSkills = [];

            if (skillsChanges) {
                const {
                    inserts,
                    deletes,
                    updates
                } = skillsChanges;

                const {
                    map,
                    orderedKeys
                } = skills;

                mergedSkills = [
                    ...orderedKeys.map(resourceSkillId => {
                        const uiSkill = createUISkill(map[resourceSkillId], getSkillInfo(resourceSkillId));
                        const updatedUISkill = {
                            id: resourceSkillId,
                            fields: {
                                ...uiSkill.fields,
                                ...((updates.map[resourceSkillId] || {}).fields || {})
                            },
                            skillPreference: ((updates.map[resourceSkillId] || {}).skillPreference || map[resourceSkillId].skillPreference)
                        };

                        return mapResourceSkillFields(updatedUISkill, getFieldInfo);
                    }),
                    ...inserts.orderedKeys.map(insertedSkillId => mapResourceSkillFields(inserts.map[insertedSkillId], getFieldInfo))
                ].filter(({ id }) => !(id in deletes.map));
            }

            return mergedSkills;
        }
);

export const getGroupedResourceSkills = createSelector(
    getResourceSkillsIds,
    getResourceSkillUiData,
    getSectionLevelField,
    (getResourceSkillsIds, getResourceSkillUiData, getSectionLevelField) =>
        (resourceId) => {
            let resourceSkillsBySection = getResourceSkillsIds(resourceId)
                .map(id => getResourceSkillUiData(resourceId, id))
                .filter(({ name }) => name)
                .reduce((accumulator, { sectionId, sectionName, ...skill }) => {
                    const {
                        skills: sectionSkills
                    } = accumulator[sectionId] || {};

                    return {
                        ...accumulator,
                        [sectionId]: {
                            sectionName,
                            sectionId,
                            levelDefinition: getSectionLevelField(sectionId),
                            skills: [
                                ...sectionSkills || [],
                                {
                                    ...skill,
                                    sectionId
                                }
                            ]
                        }
                    };
                }, {});

            Object.keys(resourceSkillsBySection).forEach(sectionId => {
                const resourceSkills = resourceSkillsBySection[sectionId].skills || [];
                resourceSkillsBySection = {
                    ...resourceSkillsBySection,
                    [sectionId]: {
                        ...resourceSkillsBySection[sectionId],
                        skills: getSortedResourceSkills(resourceSkills)
                    }
                };
            });

            return resourceSkillsBySection;
        }
);

export const getUIResourceSkillSelector = createSelector(
    getResourceSkillsSelector,
    getSkillInfoSelector,
    (getResourceSkills, getSkillInfo) =>
        (entityId, skillId) => {
            const { skillsChanges = {}, skills = {} } = getResourceSkills(entityId);

            return (skillsChanges.inserts.map[skillId] || false) ||
                (skillsChanges.updates.map[skillId] || false) ||
                (skills.map[skillId] ? createUISkill(skills.map[skillId], getSkillInfo(skillId)) : false) ||
                {};
        }
);

export const getSkillPreferenceSelector = createSelector(
    state => state.resourceSkills.skillPreferences,
    (skillPreferences) =>
        () => skillPreferences
);

export const getNewlyAddedResourceSkillIdsSelector = createSelector(
    getResourceSkillsChangesSelector,
    (getSkillsChanges) =>
        (resourceId) => {
            const {
                inserts
            } = getSkillsChanges(resourceId);

            return ((inserts || {}).orderedKeys) || [];
        }
);

/**
 * Selector to get the authorized resource skills.
 * @type {import('../types/authorizeResourceSkillDTO.jsdoc').AuthorizeResourceSkillDTO[]}
 */
export const getAuthorizeResourceSkillsSelector = createSelector(
    (state) => state.resourceSkills.resourceSkillsAllowed,
    (resourceSkillsAllowed) => resourceSkillsAllowed
);


// The selector is used to get the pending approvals for a resource
export const getPendingApprovalsSelector = createSelector(
    getResourceSkillsSelector,
    (getResourceSkills) => {
        return (resourceId) => {
            const resource = getResourceSkills(resourceId);

            return resource?.approvals?.pending ?? [];
        };
    }
);

// The selector checks if a skill is pending approval for a resource
export const checkSkillPendingApproval = createSelector(
    [
        getResourceSkillsSelector,
        (state, resourceId, skillId) => resourceId,
        (state, resourceId, skillId) => skillId
    ],
    (getResourceSkills, resourceId, skillId) => {
        // Get the resource skills data
        const resourceSkills = getResourceSkills(resourceId);

        // Check pending approvals (pendingData)
        const isInPending = resourceSkills?.approvals?.pending?.data?.some(
            pendingSkill => pendingSkill.skillId === skillId
        ) ?? false;

        if (isInPending) return true;
    }
);

//selector to check the resourceSkills approvals pending loaded flag
export const isResourceSkillsApprovalsPending = createSelector(
    getResourceSkillsSelector,
    (getResourceSkills) => {
        return (resourceId) => {
            const resource = getResourceSkills(resourceId);

            return resource?.approvals?.pending?.data?.loading ?? false;
        };
    }
);

/**
 * The selector is used to get the skills approval history
 */
export const getResourceSkillApprovalHistorySelector = createSelector(
    getResourceSkillsSelector, // base selector
    (getResourceSkills) => {
        return (resourceId) => {
            const resource = getResourceSkills(resourceId);

            return resource?.approvals?.history ?? [];
        };
    }
);