import { entityWindowOpen, entityWindowOpenForMultiple } from "../actions/entityWindowActions";
import { digestCreateListPageWorkspace } from "../actions/listPageActions";
import { promptAction } from "../actions/promptActions";
import { ENTITY_WINDOW_MODULES, TABLE_NAMES, WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS } from "../constants";
import { ENTITY_ACCESS_TYPES } from "../constants/entityAccessConsts";
import { ENTITY_WINDOW_OPERATIONS } from "../constants/entityWindowConsts";
import { RESOURCES_PAGE_ALIAS } from "../constants/resourcesPageConsts";
import { EDIT_FNAS_PER_TABLENAME } from "../constants/tablesConsts";
import { getSelectedEntitiesSelector } from "../selectors/commonSelectors";
import { getAccessibleEntitiesIdsSelector } from "../selectors/userEntityAccessSelectors";
import { getDefaultWorkspaceStructure } from "../selectors/workspaceSelectors";

const editResource = (state, dispatch) => {
    const selectedJobPane = state.entityWindow.window.resourcesPageDetailsPane;
    const { ids } = getSelectedEntitiesSelector(state, RESOURCES_PAGE_ALIAS);
    const { entityId } = selectedJobPane;

    const editedIds = entityId ? [entityId] : ids;
    const accessibleIds = getAccessibleEntitiesIdsSelector(state)(TABLE_NAMES.RESOURCE, editedIds, ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.RESOURCE]);

    if (accessibleIds.length === 0) {
        return;
    }

    let dispatchAction;

    if (accessibleIds.length === 1) {
        dispatchAction = entityWindowOpen(
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_MODAL,
            TABLE_NAMES.RESOURCE,
            TABLE_NAMES.RESOURCE,
            ENTITY_WINDOW_OPERATIONS.EDIT,
            {},
            accessibleIds[0],
            true
        );
    } else {
        dispatchAction = entityWindowOpenForMultiple(
            ENTITY_WINDOW_MODULES.PLANNER_PAGE_BATCH_MODAL,
            TABLE_NAMES.RESOURCE,
            TABLE_NAMES.RESOURCE,
            ENTITY_WINDOW_OPERATIONS.EDIT,
            [],
            accessibleIds
        );
    }

    dispatch(promptAction(dispatchAction, RESOURCES_PAGE_ALIAS));
};

const createListPageWorkspace = (state, dispatch, pageAlias) => {
    const doCreate = true;
    const selectCreatedWorkspace = true;
    const defaultWorkspaceStructure = getDefaultWorkspaceStructure(state.listPage.workspaces);
    const newWorkspaceTemplateGuid = defaultWorkspaceStructure.workspace_guid;
    const privatePlans = [];
    const newPlanName = '';
    const dispatchAction = digestCreateListPageWorkspace(
        WORKSPACE_ACCESS_TYPES.PRIVATE,
        WORKSPACE_EDIT_RIGHTS.READ_ONLY,
        newWorkspaceTemplateGuid,
        privatePlans,
        newPlanName,
        doCreate,
        selectCreatedWorkspace
    );

    dispatch(promptAction(dispatchAction, pageAlias));
};

export {
    editResource,
    createListPageWorkspace
};