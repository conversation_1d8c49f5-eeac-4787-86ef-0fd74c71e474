import { ROLE_GROUP_DETAILS_PAGE, TABLE_NAMES } from '../../constants';
import { SCOPE } from '../../constants/messageConsts';
import { ENTITY_WINDOW_MODULES, ENTITY_WINDOW_OPERATIONS } from '../../constants/entityWindowConsts';
import { getAttachmentsMessages, attachmentMessages } from './attachmentMessages';
import {
    ASSIGN_RESOURCE_ROLE_TO_CRITERIA,
    BOOKING_BUDGET_DETAILS_TEXT,
    BOOKING_MULTIPLE_RESOURCES_BUDGET_DETAILS_TEXT,
    CHANGE_ASSIGNED_CRITERIA_RESOURCE,
    CRITERIA_ROLE_ASSIGN_RESOURCE_TO_CALCULATE_BUDGET_TEXT,
    REQUIREMENTS_SECTION_INSUFFICIENT_PERMISSION_TEXT,
    ROLEREQUEST_UNASSIGNED_CRITERIA_ROLE_TEXT,
    ROLE_POST_ON_MARKETPLACE,
    ROLE_APPLICATION_TEXT,
    BOOKING_JOB_OVERBUDGET_TEXT,
    BOOKING_RESOURCE_OVERBUDGET_TEXT,
    ROLE_BUDGET_DETAILS_TEXT,
    ASSIGNEES_TOTALS_DIFFERENCE_TEXT,
    BOOKING_JOB_HOURS_OVERBUDGET_TEXT,
    RESOURCE_CHARGE_RATE_AND_DIARY_TEXT
} from '../../constants/fieldConsts';
import { translateConfig } from '../translationUtils';
import { MARKETPLACE_PAGE_ALIAS, PREVIEW_ENTITY_PAGE_ALIAS } from '../../constants/marketplacePageConsts';
import { isAssigneeBudgetModal } from '../commonUtils';

const {
    RESOURCE: resourceTableName,
    JOB: jobTableName,
    BOOKING: bookingTableName,
    CLIENT: clientTableName,
    ROLEREQUEST: rolerequestTableName,
    ROLEREQUESTRESOURCE: rolerequestresourceTableName,
    ROLEMARKETPLACE: roleMarketplaceTableName,
    ROLEREQUESTGROUP: roleRequestGroup
} = TABLE_NAMES;

export const bookingBudgetDetails = {
    id: `${bookingTableName}BudgetDetails`,
    text: `##key##bookingBudgetDetailsMessageText###${BOOKING_BUDGET_DETAILS_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage'
};

export const boookingJobOverBudgetMessage = {
    id: `${bookingTableName}_${jobTableName}_overbudget`,
    text: `##key##bookingJobOverBudgetMessageText###${BOOKING_JOB_OVERBUDGET_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'overBudget-sectionMessage',
    showIcon: true,
    type: 'warning'
};

export const bookingResourceOverBudgetMessage = {
    id: `${bookingTableName}_${resourceTableName}_overbudget`,
    text: `##key##bookingResourceOverBudgetMessageText###${BOOKING_RESOURCE_OVERBUDGET_TEXT}`,
    scope: SCOPE.ENTITY_FIELD,
    className: 'overBudget-sectionMessage',
    showIcon: true,
    type: 'warning'
};

export const bookingMultipleResourcesBudgetDetails = {
    id: `${bookingTableName}MultipleResourcesBudgetDetails`,
    text: `##key##bookingMultipleResourcesBudgetDetailsMessageText###${BOOKING_MULTIPLE_RESOURCES_BUDGET_DETAILS_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage'
};

export const boookingJobHoursOverBudgetMessage = {
    id: `${bookingTableName}_${jobTableName}_hours_overbudget`,
    text: `##key##bookingJobHoursOverBudgetMessageText###${BOOKING_JOB_HOURS_OVERBUDGET_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'overBudget-sectionMessage',
    showIcon: true,
    type: 'warning'
};

export const rolerequestCriteriaUnassignedResourceMessage = {
    id: `${rolerequestTableName}CriteriaUnassignedResourceMessage`,
    text: `##key##criteriaRoleUnassignedResourceText###${ROLEREQUEST_UNASSIGNED_CRITERIA_ROLE_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage'
};

export const rolerequestCriteriaAssignResourcesToCalculateBudget = {
    id: `${rolerequestTableName}CriteriaAssignResourceMessage`,
    text: `##key##criteriaRoleAssignedResourceToViewBudgetText###${CRITERIA_ROLE_ASSIGN_RESOURCE_TO_CALCULATE_BUDGET_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage'
};

export const requirementSectionInsufficientPermissionMessage = {
    id: 'requirementSectionInsufficientPermisionMessage',
    text: `##key##requirementSectionInsufficientPermissionsText###${REQUIREMENTS_SECTION_INSUFFICIENT_PERMISSION_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage'
};

export const roleResourceWarning = {
    id: 'roleResourceWarning',
    text: 'Assign a resource in order to submit request.',
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage',
    type: 'warning',
    showIcon: true
};

export const rolerequestCriteriaDPSuggestionPaneMessageText = {
    id: 'rolerequestCriteriaDPSuggestionPaneMessageText',
    text: 'Assign Resources to the role through the Suggestion pane.',
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage',
    componentDrivenMessage: true,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: [ENTITY_WINDOW_OPERATIONS.READ, ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT],
        [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: [ENTITY_WINDOW_OPERATIONS.READ]
    }
};

export const criteriaRoleAssignResourceMessage = {
    id: 'criteriaRoleAssignResourceMessage',
    text: `##key##criteriaRoleAssignResourceText###${ASSIGN_RESOURCE_ROLE_TO_CRITERIA}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage',
    type: 'warning',
    excludeModules: {
        [ENTITY_WINDOW_MODULES.MARKETPLACE_DETAILS_PANE]: [ENTITY_WINDOW_OPERATIONS.READ, ENTITY_WINDOW_OPERATIONS.CONTEXTUAL_EDIT],
        [ENTITY_WINDOW_MODULES.PREVIEW_ENTITY_MODAL]: [ENTITY_WINDOW_OPERATIONS.READ],
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: [ENTITY_WINDOW_OPERATIONS.READ]
    }
};

export const criteriaRoleAssignedResourceChangeMessage = {
    id: 'criteriaRoleAssignedResourceChangeMessage',
    text: `##key##criteriaRoleAssignedResourceChangeMessageText###${CHANGE_ASSIGNED_CRITERIA_RESOURCE}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage',
    type: 'warning'
};

export const criteriaRolePublishMessage = {
    id: 'criteriaRolePublishMessage',
    text: `##key##criteriaRolePublishMessageText###${ROLE_POST_ON_MARKETPLACE}`,
    scope: SCOPE.ENTITY_FORM,
    class: 'sectionMessage',
    type: 'warning'
};

export const roleApplicationMessage = {
    id: 'roleApplicationMessage',
    text: `##key##roleApplicationMessageText###${ROLE_APPLICATION_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage',
    type: 'info',
    showIcon: true,
    excludeModules: {
        [ENTITY_WINDOW_MODULES.MANAGE_ROLE_TEMPLATES_MODAL]: [ENTITY_WINDOW_OPERATIONS.READ]
    }
};

export const manageBudgetCalculationsMessage = {
    id: `${rolerequestTableName}BudgetDetails`,
    text: `##key##roleBudgetDetailsMessageText###${ROLE_BUDGET_DETAILS_TEXT}`,
    scope: SCOPE.ENTITY_FORM,
    className: 'sectionMessage',
    type: 'warning',
    showIcon: true
};

export const roleBudgetAssigneeTotalsDifferenceMessage = {
    id: `${rolerequestTableName}AssigneeTotalsDifference`,
    text: `##key##assigneeTotalsDifferenceText###${ASSIGNEES_TOTALS_DIFFERENCE_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage',
    type: 'warning',
    showIcon: true
};

export const resourceChargeRateAndDiaryMessage = {
    id:`${resourceTableName}ChargeRateAandDiaryMessage`,
    text: `${RESOURCE_CHARGE_RATE_AND_DIARY_TEXT}`,
    scope: SCOPE.ENTITY_SECTION,
    className: 'sectionMessage',
    type: 'warning',
    showIcon: true
};

export const entityMessages = {
    [resourceTableName]: {
        [resourceChargeRateAndDiaryMessage.id]: resourceChargeRateAndDiaryMessage,
        ...attachmentMessages
    },
    [jobTableName]: {
        ...attachmentMessages
    },
    [bookingTableName]: {
        [boookingJobOverBudgetMessage.id]: boookingJobOverBudgetMessage,
        [bookingResourceOverBudgetMessage.id]: bookingResourceOverBudgetMessage,
        [bookingBudgetDetails.id]: bookingBudgetDetails,
        [bookingMultipleResourcesBudgetDetails.id]: bookingMultipleResourcesBudgetDetails,
        ...attachmentMessages
    },
    [clientTableName]: {
        ...attachmentMessages
    },
    [rolerequestTableName]: {
        [roleResourceWarning.id]: roleResourceWarning,
        [rolerequestCriteriaUnassignedResourceMessage.id] : rolerequestCriteriaUnassignedResourceMessage,
        [requirementSectionInsufficientPermissionMessage.id]: requirementSectionInsufficientPermissionMessage,
        [criteriaRoleAssignResourceMessage.id]: criteriaRoleAssignResourceMessage,
        [criteriaRoleAssignedResourceChangeMessage.id]: criteriaRoleAssignedResourceChangeMessage,
        [rolerequestCriteriaAssignResourcesToCalculateBudget.id]: rolerequestCriteriaAssignResourcesToCalculateBudget,
        [roleApplicationMessage.id]: roleApplicationMessage,
        [manageBudgetCalculationsMessage.id]: manageBudgetCalculationsMessage,
        [roleBudgetAssigneeTotalsDifferenceMessage.id]: roleBudgetAssigneeTotalsDifferenceMessage
    },
    [roleMarketplaceTableName]: {
        [criteriaRolePublishMessage.id] : criteriaRolePublishMessage,
        [rolerequestCriteriaAssignResourcesToCalculateBudget.id]: rolerequestCriteriaAssignResourcesToCalculateBudget,
        [roleApplicationMessage.id]: roleApplicationMessage
    },
    [roleRequestGroup]: {}
};

const getResourceMessages = ({ operation, attachments, staticMessages = {} }) => getAttachmentsMessages(operation, attachments, staticMessages);

const getJobMessages = ({ operation, attachments, staticMessages = {} }) => getAttachmentsMessages(operation, attachments, staticMessages);

const getClientMessages = ({ operation, attachments, staticMessages = {} }) => getAttachmentsMessages(operation, attachments, staticMessages);

const getRolerequestMessages = (options, alias) => {
    const {
        operation,
        staticMessages = {},
        hasValidResource,
        hasUnassignedResource,
        insufficientCreateBookingRequestFNA,
        isCriteriaRole = false,
        hasHiddenRequirements = false,
        hasAssignedResources = false,
        isRoleRejectable,
        isRoleApplied,
        pageAlias,
        isRoleSuggestible = false
    } = options;

    const messages = [];

    const {
        roleResourceWarningText,
        roleResourcesContainUnassigedWarningText,
        requirementSectionInsufficientPermissionsText,
        criteriaRoleAssignResourceText,
        criteriaRoleAssignResourceToCalculateBudgetText,
        roleApplicationAppliedOnText,
        roleBudgetDetailsMessageText,
        roleAssigneesTotalsDifferenceText
    } = staticMessages;

    if (operation === ENTITY_WINDOW_OPERATIONS.CREATE && !isCriteriaRole) {
        if (!hasValidResource && !insufficientCreateBookingRequestFNA) {
            messages.push({
                ...roleResourceWarning,
                text: hasUnassignedResource ? roleResourcesContainUnassigedWarningText : roleResourceWarningText
            });
        }
    }

    if ((operation === ENTITY_WINDOW_OPERATIONS.EDIT || operation === ENTITY_WINDOW_OPERATIONS.READ) && isCriteriaRole) {
        if (!isRoleSuggestible || (isRoleSuggestible && pageAlias !== ROLE_GROUP_DETAILS_PAGE)) {
            messages.push({ ...criteriaRoleAssignResourceMessage, text: criteriaRoleAssignResourceText });
        }
    }

    if (isCriteriaRole && hasHiddenRequirements) {
        messages.push({
            ...requirementSectionInsufficientPermissionMessage,
            text: requirementSectionInsufficientPermissionsText
        });
    }

    if (isCriteriaRole && (!hasAssignedResources || isRoleRejectable)) {
        messages.push({
            ...rolerequestCriteriaAssignResourcesToCalculateBudget,
            text: criteriaRoleAssignResourceToCalculateBudgetText
        });
    }

    if (isRoleApplied) {
        if (pageAlias === MARKETPLACE_PAGE_ALIAS || pageAlias === PREVIEW_ENTITY_PAGE_ALIAS) {
            messages.push({
                ...roleApplicationMessage,
                text: roleApplicationAppliedOnText
            });
        }
    }

    isAssigneeBudgetModal(alias) && messages.push({ ...manageBudgetCalculationsMessage, text: roleBudgetDetailsMessageText });

    !isAssigneeBudgetModal(alias) && isCriteriaRole
        && messages.push({ ...roleBudgetAssigneeTotalsDifferenceMessage, text: roleAssigneesTotalsDifferenceText });

    return messages;
};

const getBookingMessages = (options) => {
    const {
        operation,
        attachments,
        staticMessages = {},
        bookingHasMultipleResources = false,
        isOverBudget
    } = options;

    const messages = [];
    const { bookingBudgetDetailsMessage, bookingMultipleResourcesBudgetDetailsMessage, bookingJobOverBudgetMessageText, bookingResourceOverBudgetMessageText } = staticMessages;
    const shouldAddOverBudgetMessage = isOverBudget && !bookingHasMultipleResources;

    if (operation === ENTITY_WINDOW_OPERATIONS.EDIT) {
        shouldAddOverBudgetMessage && messages.push({
            ...boookingJobOverBudgetMessage,
            text: bookingJobOverBudgetMessageText
        });
    }

    if (operation === ENTITY_WINDOW_OPERATIONS.CREATE) {
        shouldAddOverBudgetMessage && messages.push({
            ...boookingJobOverBudgetMessage,
            text: bookingJobOverBudgetMessageText
        },
        {
            ...bookingResourceOverBudgetMessage,
            text: bookingResourceOverBudgetMessageText
        });

        bookingHasMultipleResources == true
            ? messages.push({
                ...bookingMultipleResourcesBudgetDetails,
                text: bookingMultipleResourcesBudgetDetailsMessage
            })
            : messages.push({
                ...bookingBudgetDetails,
                text: bookingBudgetDetailsMessage
            });
    }

    return [
        ...messages,
        ...getAttachmentsMessages(operation, attachments, staticMessages)
    ];
};

const getRoleMarketplaceMessages = (options) => {
    const {
        staticMessages = {}
    } = options;

    const {
        criteriaRolePublishMessageText
    } = staticMessages;

    const messages = [];

    messages.push({ ...criteriaRolePublishMessage, text: criteriaRolePublishMessageText });

    return messages;
};

const getRoleRequestGroupMessages = (options) => {

    return [];
};

const entitiesMessagesFuncsMap = {
    [resourceTableName]: getResourceMessages,
    [jobTableName]: getJobMessages,
    [bookingTableName]: getBookingMessages,
    [rolerequestTableName]: getRolerequestMessages,
    [clientTableName]: getClientMessages,
    [rolerequestresourceTableName]: getRolerequestMessages,
    [roleMarketplaceTableName]: getRoleMarketplaceMessages,
    [roleRequestGroup]: getRoleRequestGroupMessages
};

export const getEntityMessages = (entityName, messagesParams, alias) =>
    entitiesMessagesFuncsMap[entityName](messagesParams, alias);

export const getEntityMessageById = (entityName, id, staticMessages) => {
    const translatedEntityMessages = translateConfig(entityMessages, entityMessages, staticMessages);

    return entityName && id
        ? translatedEntityMessages[entityName][id]
        : [];
};

export const excludeMessage = (message, moduleName, operation) => {
    const { excludeModules } = message;

    return excludeModules && excludeModules[moduleName] && excludeModules[moduleName].includes(operation);
};