import { isValid, parse } from 'date-fns';
import { sortBy } from 'lodash';
import { DATE_FORMATS } from '../constants';
import { APPLICATION_TIMEZONE, DISPLAY_DATE_TIME_FORMATS, USER_SELECTED_BROWSER_LANGUAGE } from '../constants/globalConsts';
import { range } from '../components/adminSetting/diaries/diary/calendar/utils';
import PropTypes from 'prop-types';
import { dayjsInstance } from '../setup/dayjsInstanceSetup';
import withTimezoneHof from '../setup/withTimezoneHof';

export const dateObjectPropType = PropTypes.instanceOf(dayjsInstance);

export const DATE_TIME_UNITS = {
    DATE: 'date',
    DAY: 'day',
    WEEK: 'week',
    MONTH: 'month',
    YEAR: 'year',
    HOUR: 'hour',
    MINUTE: 'minute',
    SECOND: 'second',
    MILLISECOND: 'millisecond'
};

export const DATE_INCLUSION_PARAMS = {
    EXCLUDE_START_END: '()',
    INCLUDE_START_END: '[]',
    INCLUDE_START_EXCLUDE_END: '[)'
};

export const TIMEZONE_MODE = {
    NORMAL: 1,
    REVERSE: -1
};

export const dayValueText = {
    0: 'Sunday',
    1: 'Monday',
    2: 'Tuesday',
    3: 'Wednesday',
    4: 'Thursday',
    5: 'Friday',
    6: 'Saturday'
};

// dayValues and monthValues represent the month / day const for js Date class
export const dayValues = {
    sunday: 0,
    monday: 1,
    tuesday: 2,
    wednesday: 3,
    thursday: 4,
    friday: 5,
    saturday: 6
};

export const monthValues = {
    january: 0,
    february: 1,
    march: 2,
    april: 3,
    may: 4,
    june: 5,
    july: 6,
    august: 7,
    september: 8,
    october: 9,
    november: 10,
    december: 11
};

export const TIME_MULTIPLIERS = {
    HOURS_TO_MILISECONDS: 3600000,
    MINUTES_TO_MILISECONDS: 60000,
    SECONDS_TO_MILISECONDS: 1000
};

// dayjs does not accept js Date object as a first param if we pass format as well
// dayjs(dateValue) always uses the local timezone, even if dayjs.tz.setDefault is used;
// only dayjs.tz(dateValue) (without second parameter) uses the default timezone
// timezone hof mode is used to signify whether to add or subtract the timezone offset
export const parseToUtcDate = withTimezoneHof((date) => dayjsInstance(date), TIMEZONE_MODE.NORMAL);
export const parseUtcToLocalDate = withTimezoneHof((date) => dayjsInstance(date), TIMEZONE_MODE.REVERSE);
export const parseLocalDate = (date, format) => format ? dayjsInstance(date, format) : dayjsInstance(date);

export const getCurrentDate = () => dayjsInstance().tz();
export const getCurrentLocalDate = () => dayjsInstance();

export const convertToJSDateObject = (date) => dayjsInstance(date).toDate();
export const convertToISOString = (date) => dayjsInstance(date).toISOString();

export const isDateObject = (value) => dayjsInstance.isDayjs(value);
export const isJSDateObject = (date) => date instanceof Date;

// This can be extended to cover cases such as '2020-02-30', which is considered valid by dayjs and Date
export const isValidDate = (date) => dayjsInstance(date).isValid();

export const getDay = (value) => isDateObject(value) ? value.date() : dayjsInstance(value).date();

// Months are zero indexed. By using 0 as the day it gives the last day of the previous month
export const getDaysInMonth = (date) => {
    const month = (isJSDateObject(date) ? date.getMonth() : date.month()) + 1;
    const year = isJSDateObject(date) ? date.getYear() : date.year();
    const day = 0;

    return new Date(year, month, day).getDate();
};

export const getWeekDay = (date) => dayjsInstance(date).weekday();
export const setWeekDay = (date, number) => dayjsInstance(date).weekday(number);

export const mergeDateIntervals = (intervals) => {
    // test if there are at least 2 intervals
    if (intervals.length <= 1) {
        return intervals;
    }

    const stack = [];
    let top = null;

    // sort the intervals based on their start values
    // intervals = intervals.sort((a,b) => isBefore(b.startDate, a.startDate));
    intervals = sortBy(intervals, (value) => value.startDate);

    // push the 1st interval into the stack
    stack.push(intervals[0]);

    // start from the next interval and merge if needed
    for (let i = 1; i < intervals.length; i++) {
        // get the top element
        top = stack[stack.length - 1];

        // if the current interval doesn't overlap with the
        // stack top element, push it to the stack
        if (isBefore(top.endDate, intervals[i].startDate)) {
            stack.push(intervals[i]);
        } else if (isBefore(top.endDate, intervals[i].endDate)) {
            // otherwise update the end value of the top element
            // if end of current interval is higher
            top.endDate = intervals[i].endDate;
            stack.pop();
            stack.push(top);
        }
    }

    return stack;
};

// formatDate & formatLocalDate return strings!
export const formatDate = (date, dateFormat, fallBackValue = '') => {
    return date && isValid(parse(date))
        ? dayjsInstance(date).format(dateFormat)
        : fallBackValue;
};

export const formatParsedDate = (date, dateFormat, fallBackValue = '') => {
    return date && isValid(parse(date))
        ? date.format(dateFormat)
        : fallBackValue;
};

export const formatLocalDate = (date, dateFormat, fallBackValue = '') => {
    return date && isValid(parse(date))
        ? dayjsInstance(date).local().format(dateFormat)
        : fallBackValue;
};

//Passing keepLocalTime: true will change the time zone to UTC without changing the current time.
export const setDateToUtc = (dayJSDate, keepLocalTime = true) => {
    return dayJSDate.utc(keepLocalTime);
};

export const formatToUtcISOString = (dayJSDate) => {
    //Passing true will change the time zone to UTC without changing the current time.
    return dayJSDate.utc(true).toISOString();
};

export const setDefaultTimeZone = (timeZone = APPLICATION_TIMEZONE) => dayjsInstance.tz.setDefault(timeZone);
export const setDateTimeZone = (date, timeZone = APPLICATION_TIMEZONE) => dayjsInstance(date).tz(timeZone);
export const getCurrentTimeZone = () => dayjsInstance.tz.guess();
export const getUtcOffset = () => dayjsInstance().tz(getCurrentTimeZone()).utcOffset();
export const getLocalTodayDate = () => parseToUtcDate().add(getUtcOffset(), DATE_TIME_UNITS.MINUTE);
export const getUtcTime = (date, keepLocalTime) => keepLocalTime ? parseLocalDate(date) : parseToUtcDate(date);

export const getLocalDate = (date) => {
    const localDate = parseToUtcDate(date).add(getUtcOffset() * (-1), DATE_TIME_UNITS.MINUTE);

    return isDaylightSavingsTimeDate(localDate) ? localDate : localDate.add(1, DATE_TIME_UNITS.HOUR);
};

export const setFullTime = (date, hours = 0, minutes = 0, seconds = 0, milliseconds = 0) =>
    dayjsInstance(date)
        .set(DATE_TIME_UNITS.HOUR, hours)
        .set(DATE_TIME_UNITS.MINUTE, minutes)
        .set(DATE_TIME_UNITS.SECOND, seconds)
        .set(DATE_TIME_UNITS.MILLISECOND, milliseconds);

export const isToday = (date) => isSameDay(dayjsInstance(date), getLocalTodayDate());
export const isTodayUnit = (date, unit = DATE_TIME_UNITS.DAY) => date.isSame(getLocalTodayDate(), unit);

export const isSameDay = (left, right) => {
    const leftDate = new Date(left);
    const rightDate = new Date(right);

    return leftDate.getFullYear() === rightDate.getFullYear() &&
        leftDate.getMonth() === rightDate.getMonth() &&
        leftDate.getDate() === rightDate.getDate();
};

export const isWeekendDay = (date) => {
    const day = isDateObject(date) ? date.get(DATE_TIME_UNITS.DAY) : dayjsInstance(date).get(DATE_TIME_UNITS.DAY);

    return day === dayValues.sunday || day === dayValues.saturday;
};

export const isStartOfWeek = date => dayjsInstance(date).get(DATE_TIME_UNITS.DAY) === 1;
export const isStartOfMonth = date => dayjsInstance(date).date() === 1;

export const isBetween = (date, start, end, unit, inclusiveParam = DATE_INCLUSION_PARAMS.EXCLUDE_START_END) => {
    return dayjsInstance(date).isBetween(dayjsInstance(start), dayjsInstance(end), unit, inclusiveParam);
};

export const isWithinRange = (date, startDate, endDate, dateUnits = null, inclusivityParameter = '()') =>
    dayjsInstance(date).isBetween(dayjsInstance(startDate), dayjsInstance(endDate), dateUnits, inclusivityParameter);

export const isBefore = (date, compareDate, units, inclusive) => inclusive
    ? dayjsInstance(date).isSameOrBefore(dayjsInstance(compareDate), units)
    : dayjsInstance(date).isBefore(dayjsInstance(compareDate), units);

export const isAfter = (date, compareDate, units, inclusive) => inclusive
    ? dayjsInstance(date).isSameOrAfter(dayjsInstance(compareDate), units)
    : dayjsInstance(date).isAfter(dayjsInstance(compareDate), units);

export const isSame = (date, compareDate, unit) => unit
    ? dayjsInstance(date).isSame(dayjsInstance(compareDate), unit)
    : dayjsInstance(date).isSame(dayjsInstance(compareDate));

export const isDaylightSavingsTimeDate = (value) => {
    const date = new Date(value);
    const january = new Date(date.getFullYear(), 0, 1).getTimezoneOffset();
    const july = new Date(date.getFullYear(), 6, 1).getTimezoneOffset();

    return Math.max(january, july) !== date.getTimezoneOffset();
};

export const areDifferentDstOffset = (firstDate, secondDate) => {
    const firstDateOffest = new Date(firstDate).getTimezoneOffset();
    const secondDateOffest = new Date(secondDate).getTimezoneOffset();

    return firstDateOffest !== secondDateOffest;
};

export const applyDstOffset = (originalDate, newDate) => {
    const originalDateOffset = new Date(originalDate).getTimezoneOffset();
    const originalNewDateOffset = new Date(newDate).getTimezoneOffset();

    const offsetDifference = originalNewDateOffset - originalDateOffset;

    return newDate.add(offsetDifference, DATE_TIME_UNITS.MINUTE);
};

export const getAdjustedDstOffset = (originalDate, processedDate) => {
    return areDifferentDstOffset(originalDate, processedDate) ? applyDstOffset(originalDate, processedDate) : processedDate;
};

export const getISODay = (date) => parseToUtcDate(date).isoWeekday();
export const getYearFromDate = (date) => parseToUtcDate(date).year();
export const getMonth = (date) => parseToUtcDate(date).get(DATE_TIME_UNITS.MONTH);
export const getWeekNumber = (date) => Number(formatDate(date, DATE_FORMATS.WEEK)) || 0;
export const getIsoWeek = (date) => date.isoWeek();

export const getYear = (date) => parseToUtcDate(date).year();

export const startOfDay = date => {
    if (!isValidDate(date) || date == null) {
        return dayjsInstance(date);
    }

    const newDate = new Date(date);

    const hours = newDate.getHours();
    const minutes = newDate.getMinutes();
    const seconds = newDate.getSeconds();
    const miliseconds = newDate.getMilliseconds();

    let totalMiliseconds = hours * TIME_MULTIPLIERS.HOURS_TO_MILISECONDS;
    totalMiliseconds += minutes * TIME_MULTIPLIERS.MINUTES_TO_MILISECONDS;
    totalMiliseconds += seconds * TIME_MULTIPLIERS.SECONDS_TO_MILISECONDS;
    totalMiliseconds += miliseconds;

    newDate.setTime(newDate.getTime() - totalMiliseconds);

    return dayjsInstance(newDate);
};

export const startOfUtcDay = date => {
    if (!isValidDate(date) || date == null) {
        return dayjsInstance(date);
    }

    return dayjsInstance(date).startOf(DATE_TIME_UNITS.DAY);
};

export const customEndOfDay = date => {
    if (!isValidDate(date) || date == null) {
        return dayjsInstance(date);
    }

    const newDate = new Date(date);

    const hours = newDate.getHours();
    const minutes = newDate.getMinutes();
    const seconds = newDate.getSeconds();
    const miliseconds = newDate.getMilliseconds();

    let totalMiliseconds = hours * TIME_MULTIPLIERS.HOURS_TO_MILISECONDS;
    totalMiliseconds += minutes * TIME_MULTIPLIERS.MINUTES_TO_MILISECONDS;
    totalMiliseconds += seconds * TIME_MULTIPLIERS.SECONDS_TO_MILISECONDS;
    totalMiliseconds += miliseconds;

    newDate.setDate(newDate.getDate() + 1);
    newDate.setTime(newDate.getTime() - totalMiliseconds - 1000);

    return dayjsInstance(newDate);
};

export const startOfDayAtMidnight = (date) => {
    if (!isValidDate(date) || date == null) {
        return dayjsInstance(date);
    }

    return convertToISOString(date).split('T')[0] + 'T00:00:00.000Z';
};

export const endOfDayAtMidnight = (date) => {
    if (!isValidDate(date) || date == null) {
        return dayjsInstance(date);
    }

    return convertToISOString(date).split('T')[0] + 'T23:59:59.000Z';
};

export const endOfDay = date => dayjsInstance(date).endOf(DATE_TIME_UNITS.DAY).set(DATE_TIME_UNITS.MILLISECOND, 0);
export const getStartOfDay = date => dayjsInstance(date).startOf(DATE_TIME_UNITS.DAY).set(DATE_TIME_UNITS.MILLISECOND, 0);
export const getStartOfLocalDate = (date) => parseLocalDate(date).startOf(DATE_TIME_UNITS.DAY);
export const getEndOfLocalDate = (date) => parseLocalDate(date).endOf(DATE_TIME_UNITS.DAY).set(DATE_TIME_UNITS.MILLISECOND, 0);

export const getCurrentStartOfDayIsoString = () => formatToUtcISOString(getStartOfDay());

export const startOfWeek = date => dayjsInstance(date).startOf(DATE_TIME_UNITS.WEEK);
export const lastDayOfWeek = date => dayjsInstance(date).endOf(DATE_TIME_UNITS.WEEK).set(DATE_TIME_UNITS.MILLISECOND, 0);

export const startOfISOWeek = date => dayjsInstance(date).startOf('isoWeek');
export const lastDayOfISOWeek = date => dayjsInstance(date).endOf('isoWeek').set(DATE_TIME_UNITS.MILLISECOND, 0);

export const startOfMonth = date => dayjsInstance(date).startOf(DATE_TIME_UNITS.MONTH);
export const lastDayOfMonth = date => dayjsInstance(date).endOf(DATE_TIME_UNITS.MONTH).set(DATE_TIME_UNITS.MILLISECOND, 0);

export const startOfYear = date => dayjsInstance(date).startOf(DATE_TIME_UNITS.YEAR);
export const lastDayOfYear = date => dayjsInstance(date).endOf(DATE_TIME_UNITS.YEAR).set(DATE_TIME_UNITS.MILLISECOND, 0);

export const differenceInHours = (left, right) => dayjsInstance(left).diff(dayjsInstance(right), DATE_TIME_UNITS.HOUR);
export const differenceInCalendarDays = (left, right) => dayjsInstance(left).diff(dayjsInstance(right), DATE_TIME_UNITS.DAY);

export const differenceInUtcCalendarDays = (left, right) => {
    const leftUtcStartOfDay = dayjsInstance(left).utc().startOf(DATE_TIME_UNITS.DAY);
    const rightUtcStartOfDay = dayjsInstance(right).utc().startOf(DATE_TIME_UNITS.DAY);

    return leftUtcStartOfDay.diff(rightUtcStartOfDay, DATE_TIME_UNITS.DAY);
};

export const differenceBetweenStartDates = (left, right) => dayjsInstance(left)
    .startOf(DATE_TIME_UNITS.DAY)
    .diff(dayjsInstance(right).startOf(DATE_TIME_UNITS.DAY), DATE_TIME_UNITS.DAY);

export const differenceInUnits = (left, right, unit) => dayjsInstance(left).diff(dayjsInstance(right), unit);
export const differenceInWeeks = (left, right) => {
    return dayjsInstance(left).diff(dayjsInstance(right), DATE_TIME_UNITS.WEEK);
};
export const differenceInMonths = (left, right) => dayjsInstance(left).diff(dayjsInstance(right), DATE_TIME_UNITS.MONTH);

export const addHours = (date, hoursCount) => dayjsInstance(date).add(hoursCount, DATE_TIME_UNITS.HOUR);
export const addDays = (date, daysCount) => dayjsInstance(date).add(daysCount, DATE_TIME_UNITS.DAY);
export const addWeeks = (date, weeksCount) => dayjsInstance(date).add(weeksCount, DATE_TIME_UNITS.WEEK);
export const addMonths = (date, monthsCount) => dayjsInstance(date).add(monthsCount, DATE_TIME_UNITS.MONTH);
export const addYears = (date, yearsCount) => dayjsInstance(date).add(yearsCount, DATE_TIME_UNITS.YEAR);

export const addDaysToParsedDate = (date, daysCount) => date.add(daysCount, DATE_TIME_UNITS.DAY);

export const subHours = (date, hoursCount) => dayjsInstance(date).subtract(hoursCount, DATE_TIME_UNITS.HOUR);
export const subDays = (date, daysCount) => dayjsInstance(date).subtract(daysCount, DATE_TIME_UNITS.DAY);
export const subWeeks = (date, weeksCount) => dayjsInstance(date).subtract(weeksCount, DATE_TIME_UNITS.WEEK);
export const subMonths = (date, monthsCount) => dayjsInstance(date).subtract(monthsCount, DATE_TIME_UNITS.MONTH);
export const subYears = (date, yearsCount) => dayjsInstance(date).subtract(yearsCount, DATE_TIME_UNITS.YEAR);

export const greaterDate = (left, right) => isBefore(left, right) ? right : left;
export const lesserDate = (left, right) => isBefore(left, right) ? left : right;

export const getDurationInMinutes = (value) => dayjsInstance.duration(value).asMinutes();
export const getDurationInSeconds = (value) => dayjsInstance.duration(value).asSeconds();

export const getDayJsLocale = () => dayjsInstance.locale();

export function workingDaysBetweenDates(startDate, endDate) {
    let direction = 1;
    let start = startDate;
    let end = endDate;

    if (endDate < startDate) {
        direction = -1;
        start = endDate;
        end = startDate;
    }

    // Calculate days between dates
    let days = Math.abs(differenceInCalendarDays(start, end));

    // Subtract two weekend days for every week in between
    const weeks = Math.floor(days / 7);
    days -= weeks * 2;

    // Handle special cases
    const startDay = start.day();
    const endDay = end.day();

    // Remove weekend not previously removed.
    if (startDay - endDay >= 1 || (endDay == 6 && startDay == 0)) {
        days -= 2;
    } else {
        // Remove start day if span starts on Sunday but ends before Saturday
        if (startDay == 0 && endDay != 6) {
            days--;
        }
        // Remove end day if span ends on Saturday but starts after Sunday
        if (endDay == 6 && startDay != 0) {
            days--;
        }
    }

    return days * direction;
}

export function isDateWeekendDay(date) {
    const dateValue = dayjsInstance(date).get(DATE_TIME_UNITS.DAY);

    return dateValue === dayValues.saturday || dateValue === dayValues.sunday;
}

export function getWeekDatesFromSelectedDate(selectedDate) {
    let dates = [];
    const firstDayOfWeek = 0; // sunday
    const totalDays = 7; // insert days at the end to match up 37 (max number of days in a month + 6)

    range(firstDayOfWeek, totalDays + firstDayOfWeek).forEach(i => {
        const day = formatDate(startOfDay(setWeekDay(selectedDate, i)), DATE_FORMATS.YEAR_MONTH_DATE_HOUR_MIN_SEC);

        dates.push(day);
    });

    return dates;
}

export function getStartAndEndDateOfWeek(date) {
    const weekDates = getWeekDatesFromSelectedDate(date);

    return {
        startDateOfWeek: weekDates[0],
        endDateOfWeek: weekDates[6]
    };
}

export function getDisplayShortDateFormat(yearType = DISPLAY_DATE_TIME_FORMATS.FULL_YEAR, language = USER_SELECTED_BROWSER_LANGUAGE) {
    const currentLocale = getDayJsLocale();
    const instanceLocaleData = dayjsInstance().locale(language || currentLocale).localeData();

    let format = instanceLocaleData.longDateFormat('L');

    switch (yearType) {
        case DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR: {
            format = format.replace('YYYY', 'YY');
            break;
        }
        case DISPLAY_DATE_TIME_FORMATS.NO_YEAR: {
            format = format.replace(/([^a-zA-Z0-9]Y+)|(^Y+[^a-zA-Z0-9])/, '');
            break;
        }
        case DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR_WITH_TIME: {
            format = format.replace('YYYY', 'YY') + ' HH:mm';
            break;
        }
        default: {
            break;
        }
    }

    return format;
}

export function getDisplayLongDateTimeFormat(yearType = DISPLAY_DATE_TIME_FORMATS.FULL_YEAR, timeType = DISPLAY_DATE_TIME_FORMATS.SHORT_TIME, language = USER_SELECTED_BROWSER_LANGUAGE) {
    const dateFormat = getDisplayShortDateFormat(yearType, language);
    const currentLocale = getDayJsLocale();
    const instanceLocaleData = dayjsInstance().locale(language || currentLocale).localeData();
    let timeFormat = null;

    switch (timeType) {
        case DISPLAY_DATE_TIME_FORMATS._24_HOURS_SHORT_TIME: {
            timeFormat = 'HH:mm';
            break;
        }
        case DISPLAY_DATE_TIME_FORMATS.SHORT_TIME:
        case DISPLAY_DATE_TIME_FORMATS.LONG_TIME: {
            timeFormat = instanceLocaleData.longDateFormat(timeType);

            break;
        }
        default: {
            break;
        }
    }

    const format = timeFormat ? `${dateFormat} ${timeFormat}` : dateFormat;

    return format;
}

export function getDisplayLongDateFormat(yearType = DISPLAY_DATE_TIME_FORMATS.FULL_YEAR, language = USER_SELECTED_BROWSER_LANGUAGE) {
    const currentLocale = getDayJsLocale();
    const instanceLocaleData = dayjsInstance().locale(language || currentLocale).localeData();

    let format = instanceLocaleData.longDateFormat('ll');

    switch (yearType) {
        case DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR: {
            format = format.replace('YYYY', 'YY');
            break;
        }
        case DISPLAY_DATE_TIME_FORMATS.NO_YEAR: {
            format = format.replace(/([^a-zA-Z0-9]+Y+)|(^Y+([^a-zA-Z0-9]?)+)/, '');
            break;
        }
        case DISPLAY_DATE_TIME_FORMATS.NO_DAY: {
            format = format.replace(/([^a-zA-Z0-9]+D+)|(^D+([^a-zA-Z0-9]?)+)/, '');
            break;
        }
        default: {
            break;
        }
    }

    return format;
}

export function getConsecutiveRangesFromDatesCollection(dates, unit = DATE_TIME_UNITS.DAY) {
    return dates
        .sort((dateA, dateB) => new Date(dateA) - new Date(dateB))
        .reduce((ranges, date, index) => {
            const lastIndex = ranges.length - 1;
            const difference = lastIndex > -1 && differenceInUnits(date, ranges[lastIndex].endDate, unit);

            if (index === 0 || difference !== 1) {
                ranges.push({ startDate: date, endDate: date });
            } else {
                (ranges[lastIndex] || {}).endDate = date;
            }

            return ranges;
        }, []);
}

// This function was added to combat diary timezone issues
// It takes a date and converts it to a new UTC Date
export function getUtcFullDate(date) {
    const day = date.date();
    const month = date.month();
    const year = date.year();

    const monthString = month < 9 ? `0${month + 1}` : `${month + 1}`;
    const dayString = day < 10 ? `0${day}` : `${day}`;

    return dayjsInstance(`${year}-${monthString}-${dayString}T00:00:00.000Z`);
}

/**
 * Formats a date object into a string in "month/day/year" format, using UTC time zone.
 *
 * @param {Object} dateObj - The date object containing date components.
 * @param {number} dateObj.$y - The year (e.g., 2025).
 * @param {number} dateObj.$M - The month (0-based, where 0 = January, 1 = February, etc.).
 * @param {number} dateObj.$D - The day of the month (1-31).
 * @returns {string} The formatted date string in "month/day/year" format (e.g., "02/01/2025").
 *
 * const formattedDate = formatDateToString(dateObj);
 * console.log(formattedDate); // Output: "02/01/2025"
 */
export function formatDateToString(dateObj) {
    const year = dateObj.$y;
    const month = dateObj.$M;
    const day = dateObj.$D;

    const date = new Date(Date.UTC(year, month, day));
    const formattedDate = `${String(date.getUTCMonth() + 1).padStart(2, '0')}/${String(date.getUTCDate()).padStart(2, '0')}/${date.getUTCFullYear()}`;

    return formattedDate;
}
