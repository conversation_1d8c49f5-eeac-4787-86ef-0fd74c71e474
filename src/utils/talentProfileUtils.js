import React from 'react';
import { RESOURCE_CALC_FIELDS, RESOURCE_LAST_LOGIN, RESOURCE_CMERED, RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMEYELLOW, RESOURCE_CMETOPCOLOUR, RESOURCE_APPROVAL, RECOM<PERSON>NDATION_VIEWED, RESOURCE_SURROGATE_ID, RESOURCE_GUID, RESOURCE_PRIMARY_SKILL, RESOURCE_SECONDARY_SKILL } from '../constants/fieldConsts';
import { FILTER_FIELD_NAMES, SKILL_PREFERENCE_TYPES } from '../constants/globalConsts';
import TpSectionHeader from '../lib/talentProfile/tpSectionHeader';
import { isEmptyObject } from './commonUtils';
import { getLinkedSelectionFields, getCustomLookupSelectionFields } from './linkedDataUtils';
import { TABLE_NAMES, PAGE_NAMES } from '../constants';
import getServerinfo from '../serverInfo';
import { cloneDeep } from 'lodash';
import { SKILLS_SECTION_TITLE } from '../constants/skillsSectionConsts';
import { TALENT_PROFILE_SECTIONS } from '../constants/talentProfileConsts';

const addFieldsToSelection = (selFields, addedIndex, fields = []) => {
    fields.forEach(field => {
        const fieldName = field.FieldName;
        if (undefined === addedIndex[fieldName]) {
            addedIndex[fieldName] = true;
            // Added support for `parameters` so extra details can be included in selected fields.
            field.parameters ? selFields.push({ fieldName, fieldAlias: fieldName, parameters: field.parameters }) : selFields.push({ fieldName, fieldAlias: fieldName });
        }
    });
};

const getSectionsAuditFilterFields = (profileSections = [], sectionsNames = []) => {
    const sectionTypesMap = {
        SKILLS: TABLE_NAMES.RESOURCESKILL,
        EDUCATION: TABLE_NAMES.EDUCATION,
        EXPERIENCE: TABLE_NAMES.EXPERIENCE
    };

    return profileSections.reduce((existingSections, { DisplayType }) => {
        const sectionType = DisplayType.toUpperCase();

        if (sectionsNames.includes(sectionType)) {
            existingSections.push({
                fieldName: 'resource_section_lastupdated',
                fieldAlias: RESOURCE_CALC_FIELDS[sectionType].LAST_UPDATED,
                parameters: {
                    SectionName: sectionTypesMap[sectionType]
                }
            });
        }

        return existingSections;
    }, []);
};

// Added legacy function to support viewing old version of the profile.
export const buildTPSelectionLegacy = (id, profileTableName, config, getFieldInfo, resourceId, SkillApprovalFeatureFlag) => {
    const { ProfileHeaderSection, ProfileSections, QuickReferenceArea } = config;
    const { HeaderFieldGroup, ExplanationFieldsGroup = {}, DetailsFieldGroup, AdditionalFieldGroup } = ProfileHeaderSection;
    const { Contact, Widgets } = QuickReferenceArea;
    let addedIndex = {};

    const sectionsNames = [TABLE_NAMES.EDUCATION, SKILLS_SECTION_TITLE, TABLE_NAMES.EXPERIENCE, TABLE_NAMES.CME].map(x => x.toUpperCase());
    const sectionsAuditFilterFields = getSectionsAuditFilterFields(ProfileSections, sectionsNames);
    const fields = [
        {
            fieldName: RESOURCE_GUID
        },
        {
            fieldName: RESOURCE_SURROGATE_ID
        },
        {
            fieldName: RESOURCE_CMERED
        },
        {
            fieldName: RESOURCE_CMEBLUE
        },
        {
            fieldName: RESOURCE_CMEGREEN
        },
        {
            fieldName: RESOURCE_CMEYELLOW
        },
        {
            fieldName: RESOURCE_CMETOPCOLOUR
        },
        {
            fieldName: RESOURCE_LAST_LOGIN
        },
        {
            fieldName: RECOMMENDATION_VIEWED
        },
        ...(SkillApprovalFeatureFlag ? [{
            fieldName: RESOURCE_APPROVAL,
            TableName: TABLE_NAMES.RESOURCE,
            parameters: { AccessRuleKey: 'ManagerApproval', ResourceGuid: resourceId }
        }] : []),
        ...sectionsAuditFilterFields
    ];

    addFieldsToSelection(fields, addedIndex, HeaderFieldGroup.Fields);
    addFieldsToSelection(fields, addedIndex, ExplanationFieldsGroup.Fields);
    addFieldsToSelection(fields, addedIndex, DetailsFieldGroup.Fields);
    addFieldsToSelection(fields, addedIndex, AdditionalFieldGroup.Fields);

    ProfileSections.forEach(section => {
        addFieldsToSelection(fields, addedIndex, section.Fields);
    });

    addFieldsToSelection(fields, addedIndex, Contact.Fields);
    // Build widgets fields
    //addFieldsToSelection(fields, Widgets.Fields);
    addedIndex = null;

    return {
        fields: [
            ...fields,
            ...getLinkedSelectionFields(profileTableName, fields, getFieldInfo),
            ...getCustomLookupSelectionFields(profileTableName, fields, getFieldInfo)
        ],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: RESOURCE_SURROGATE_ID,
                    operator: 'Equals',
                    value: id
                }
            ],
            subFilters: null
        }
    };
};

// Added  function  to support viewing new version of the profile.
export const buildTPSelection = (id, profileTableName, config, getFieldInfo, resourceId, SkillApprovalFeatureFlag) => {
    const { ProfileHeaderSection, ProfileSections } = config;
    const { HeaderFieldGroup, ExplanationFieldsGroup = {}, DetailsFieldGroup, SummaryFieldGroup, ContactFieldGroup } = ProfileHeaderSection;
    let addedIndex = {};

    const sectionsNames = [TABLE_NAMES.EDUCATION, SKILLS_SECTION_TITLE, TABLE_NAMES.EXPERIENCE, TABLE_NAMES.CME].map(x => x.toUpperCase());
    const sectionsAuditFilterFields = getSectionsAuditFilterFields(ProfileSections, sectionsNames);

    const fields = [
        {
            fieldName: RESOURCE_GUID
        },
        {
            fieldName: RESOURCE_SURROGATE_ID
        },
        {
            fieldName: RESOURCE_CMERED
        },
        {
            fieldName: RESOURCE_CMEBLUE
        },
        {
            fieldName: RESOURCE_CMEGREEN
        },
        {
            fieldName: RESOURCE_CMEYELLOW
        },
        {
            fieldName: RESOURCE_CMETOPCOLOUR
        },
        {
            fieldName: RESOURCE_LAST_LOGIN
        },
        {
            fieldName: RECOMMENDATION_VIEWED
        },
        ...(SkillApprovalFeatureFlag ? [{
            fieldName: RESOURCE_APPROVAL,
            TableName: TABLE_NAMES.RESOURCE,
            parameters: { AccessRuleKey: 'ManagerApproval', ResourceGuid: resourceId }
        }] : []),
        ...sectionsAuditFilterFields
    ];

    addFieldsToSelection(fields, addedIndex, HeaderFieldGroup.Fields);
    addFieldsToSelection(fields, addedIndex, ExplanationFieldsGroup.Fields);
    addFieldsToSelection(fields, addedIndex, DetailsFieldGroup.Fields);
    addFieldsToSelection(fields, addedIndex, SummaryFieldGroup.Fields);
    addFieldsToSelection(fields, addedIndex, ContactFieldGroup.Fields);

    ProfileSections.forEach(section => {
        addFieldsToSelection(fields, addedIndex, section.Fields);
    });

    // Build widgets fields
    //addFieldsToSelection(fields, Widgets.Fields);
    addedIndex = null;

    return {
        fields: [
            ...fields,
            ...getLinkedSelectionFields(profileTableName, fields, getFieldInfo),
            ...getCustomLookupSelectionFields(profileTableName, fields, getFieldInfo)
        ],
        filter: {
            filterGroupOperator: 'And',
            filterLines: [
                {
                    field: RESOURCE_SURROGATE_ID,
                    operator: 'Equals',
                    value: id
                }
            ],
            subFilters: null
        }
    };
};

const addFields = (selFields, addedIndex, fields = []) => {
    fields.forEach(field => {
        const fieldName = field.FieldName;
        if (undefined === addedIndex[fieldName]) {
            addedIndex[fieldName] = true;
            selFields.push(fieldName);
        }
    });
};

// Added legacy function to support viewing old version of the profile.
export const getProfileFieldNamesFromConfigLegacy = (config) => {
    const { ProfileHeaderSection, ProfileSections, QuickReferenceArea } = config;
    const { HeaderFieldGroup, ExplanationFieldsGroup = {}, DetailsFieldGroup, AdditionalFieldGroup } = ProfileHeaderSection;
    const { Contact } = QuickReferenceArea;
    let addedIndex = {};
    let fields = [RESOURCE_GUID, RESOURCE_SURROGATE_ID, RESOURCE_CMERED, RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMEYELLOW, RESOURCE_CMETOPCOLOUR, RESOURCE_LAST_LOGIN, RECOMMENDATION_VIEWED];

    addFields(fields, addedIndex, HeaderFieldGroup.Fields);
    addFields(fields, addedIndex, ExplanationFieldsGroup.Fields);
    addFields(fields, addedIndex, DetailsFieldGroup.Fields);
    addFields(fields, addedIndex, AdditionalFieldGroup.Fields);

    ProfileSections.forEach(section => {
        addFields(fields, addedIndex, section.Fields);
    });

    addFields(fields, addedIndex, Contact.Fields);

    addedIndex = null;

    return fields;
};

// Added functions to support viewing new version of the profile.
export const getProfileFieldNamesFromConfig = (config) => {
    const { ProfileHeaderSection, ProfileSections } = config;
    const { HeaderFieldGroup, ExplanationFieldsGroup = {}, DetailsFieldGroup, SummaryFieldGroup, ContactFieldGroup } = ProfileHeaderSection;
    let addedIndex = {};
    let fields = [RESOURCE_GUID, RESOURCE_SURROGATE_ID, RESOURCE_CMERED, RESOURCE_CMEBLUE, RESOURCE_CMEGREEN, RESOURCE_CMEYELLOW, RESOURCE_CMETOPCOLOUR, RESOURCE_LAST_LOGIN, RECOMMENDATION_VIEWED];

    addFields(fields, addedIndex, HeaderFieldGroup.Fields);
    addFields(fields, addedIndex, ExplanationFieldsGroup.Fields);
    addFields(fields, addedIndex, DetailsFieldGroup.Fields);
    addFields(fields, addedIndex, ContactFieldGroup.Fields);
    addFields(fields, addedIndex, SummaryFieldGroup.Fields);

    ProfileSections.forEach(section => {
        addFields(fields, addedIndex, section.Fields);
    });

    addedIndex = null;

    return fields;
};

export const getTpSectionTitle = (title, resourceAuditInfo) => {
    let sectionTitle = title;

    if (!isEmptyObject(resourceAuditInfo)) {
        const { updatedOn, updatedBy } = resourceAuditInfo;
        sectionTitle = <TpSectionHeader title={title} updatedOn={updatedOn} updatedBy={updatedBy} />;
    }

    return sectionTitle;
};

const getLastUpdateInfo = (info) => {
    const { sectionType } = info;
    const resourceLastUpdate = info[`resource_${sectionType}_lastupdated`];
    const [lastUpdateResourceName, lastUpdateDateAndTime] = resourceLastUpdate.split(', ');

    return {
        sectionType,
        updatedOn: lastUpdateDateAndTime.split(' ')[0].replace(/-/g, '-'),
        updatedBy: lastUpdateResourceName
    };
};

export const getSectionsUpdateAuditInfo = (sectionsInfo = []) => sectionsInfo.reduce((sections, sectionInfo) => {
    return sectionInfo[`resource_${sectionInfo.sectionType}_lastupdated`] ? [...sections, getLastUpdateInfo(sectionInfo)] : sections;
}, []);

export const getResourceSectionAuditFields = (profileSections = [], sectionsNames = [], resource = {}) => {
    const { SKILLS, EDUCATION, EXPERIENCE } = RESOURCE_CALC_FIELDS;
    const sectionsAuditFields = {
        SKILLS: (resource) => ({ [SKILLS.LAST_UPDATED]: resource[SKILLS.LAST_UPDATED], sectionType: TABLE_NAMES.RESOURCESKILL }),
        EDUCATION: (resource) => ({ [EDUCATION.LAST_UPDATED]: resource[EDUCATION.LAST_UPDATED], sectionType: TABLE_NAMES.EDUCATION }),
        EXPERIENCE: (resource) => ({ [EXPERIENCE.LAST_UPDATED]: resource[EXPERIENCE.LAST_UPDATED], sectionType: TABLE_NAMES.EXPERIENCE })
    };

    return profileSections.reduce((existingSections, { DisplayType }) => {
        const sectionType = DisplayType.toUpperCase();

        return sectionsNames.includes(sectionType) ? [...existingSections, sectionsAuditFields[sectionType](resource)] : existingSections;
    }, []);
};

/**
* Function to generate a shareable profile link
* @param {string} id - Resource ID to be included in the link
* @param {Object} config - Configuration object containing navigation details
* @returns {string} - Formatted shareable profile link
*/
export const getShareProfileLink = (id, { navigationLink }) => {
    const { protocol, hostname, port = '' } = window.location;
    const client = getServerinfo().Client;
    const formattedPort = port ? `:${port}` : '';
    const formattedClient = client ? `/${client}` : '';

    return `${protocol}//${hostname}${formattedPort}${formattedClient}${navigationLink}/${id}`;
};

// Restructuring raw config data to align with the requirements of the transformed UI components.
export const transformProfileData = (originalData) => {
    const newData = cloneDeep(originalData);

    const { ProfileSections, QuickReferenceArea, ProfileHeaderSection } = newData;

    // 1. Move Summary Fields to ProfileHeaderSection
    const summarySection = ProfileSections?.find(section => section.SectionTitle === PAGE_NAMES.SUMMARY);
    if (summarySection) {
        ProfileHeaderSection.SummaryFieldGroup = {
            DisplayType: TALENT_PROFILE_SECTIONS.INFO,
            Fields: summarySection.Fields || []
        };
    }

    const displayTypeMap = {
        [PAGE_NAMES.EMPLOYMENT]: TALENT_PROFILE_SECTIONS.EMPLOYMENT,
        [PAGE_NAMES.ADDITIONAL_DETAILS]: TALENT_PROFILE_SECTIONS.ADDITIONAL
    };

    if (ProfileSections) {
        ProfileSections.forEach((section) => {
            const displayType = displayTypeMap[section.SectionTitle];
            if (displayType) {
                section.DisplayType = displayType;
            }
        });
    }
    // 2. Move Contact Fields to ProfileHeaderSection
    const contactFields = QuickReferenceArea?.Contact?.Fields;
    if (contactFields) {
        ProfileHeaderSection.ContactFieldGroup = {
            DisplayType: TALENT_PROFILE_SECTIONS.CONTACT,
            Fields: contactFields
        };
    }

    // 3. Consolidate and filter fields
    const allFields = [
        ...ProfileHeaderSection.ExplanationFieldsGroup?.Fields || [],
        ...ProfileHeaderSection.DetailsFieldGroup?.Fields || [],
        ...ProfileHeaderSection.AdditionalFieldGroup?.Fields || []
    ];

    const pickFields = (fieldNames) => allFields.filter(f => fieldNames.includes(f.FieldName));

    ProfileHeaderSection.DetailsFieldGroup.Fields = pickFields([
        FILTER_FIELD_NAMES.RESOURCE_DIVISION,
        FILTER_FIELD_NAMES.RESOURCE_DEPARTMENT,
        FILTER_FIELD_NAMES.RESOURCE_MANAGER
    ]);

    ProfileHeaderSection.ExplanationFieldsGroup.Fields = [
        ...pickFields([FILTER_FIELD_NAMES.RESOURCE_JOBTITLE]),
        {
            FieldName: RESOURCE_PRIMARY_SKILL,
            TableName: TABLE_NAMES.RESOURCE,
            parameters: { skillPreference: SKILL_PREFERENCE_TYPES.PRIMARY_SKILL }
        },
        {
            FieldName: RESOURCE_SECONDARY_SKILL,
            TableName: TABLE_NAMES.RESOURCE,
            parameters: { skillPreference: SKILL_PREFERENCE_TYPES.SECONDARY_SKILL }
        }
    ];

    // 4. Remove Additional Fields
    delete ProfileHeaderSection.AdditionalFieldGroup;

    // 5.Remove "Summary" section from ProfileSections
    const summaryIndex = newData.ProfileSections.findIndex(
        section => section.SectionTitle === PAGE_NAMES.SUMMARY
    );

    if (summaryIndex !== -1) {
        newData.ProfileSections.splice(summaryIndex, 1);
    }

    // 6. Move "Documents" section to the end of ProfileSections
    const docIndex = newData.ProfileSections.findIndex(s => s.SectionTitle === TALENT_PROFILE_SECTIONS.ATTACHMENTS);
    if (docIndex !== -1) {
        const [docSection] = newData.ProfileSections.splice(docIndex, 1);
        newData.ProfileSections.push(docSection);
    }
    // 7. Reassign SectionKeys in sequence to maintain a predictable order for rendering the profile sections.
    newData.ProfileSections.forEach((section, index) => {
        section.SectionKey = `section${index + 1}`;
    });

    return newData;
};

// Function to get the skill entity types from the state
export const getActionText = (action) => {
    const actionTextMap = {
        add: 'Skills added',
        update: 'Skills updated',
        delete: 'Skills deleted'
    };

    return actionTextMap[action] || 'Skills modified';
};
