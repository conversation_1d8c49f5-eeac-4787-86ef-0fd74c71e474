import { TABLE_NAMES } from '../constants';
import { NOTIFICATION_EVENTS, NOTIFICATIONS_HISTORY_ACTIONS, NOTIFICATIONS_BOOKING_ALIAS, DEFAULT_NOTIFICATION_PAGESIZE, DUMMY_GUID, NEW_NOTIFICATION_TABS_KEY, NOTIFICATION_TABS_KEY } from '../constants/notificationsPageConsts';
import { formatDate, getDisplayShortDateFormat, parseToUtcDate, startOfDayAtMidnight, parseUtcToLocalDate } from '../utils/dateUtils';
import { populateStringTemplates } from '../utils/translationUtils';
import { CREATE_FNAS_PER_TABLENAME, EDIT_FNAS_PER_TABLENAME, ROLEREQUEST_WORKFLOW_ACCESS_TYPES } from '../constants/tablesConsts';
import { ENTITY_WINDOW_OPERATIONS } from '../constants/entityWindowConsts';
import { ENTITY_ACCESS_TYPES } from '../constants/entityAccessConsts';
import { sortBy } from 'lodash';
import { onChangeNotificationReadUnreadStatus, deleteNotification, createBookingAction } from '../actions/notificationsPageActions';
import { getNotificationByIdSelector } from '../selectors/notificationsPageSelectors';
import { ENTITY_ACTION_KEYS } from '../constants/entityAccessConsts';
import { DISPLAY_DATE_TIME_FORMATS, FEATURE_FLAGS } from '../constants/globalConsts';
import { REMAP_ENTITY_TITLES } from '../constants/adminSettingConsts';
import { BOOKING_TYPES } from '../constants/plannerConsts';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';

const {
    BOOKING_ASSIGN_TYPE,
    BOOKING_UPDATE_TYPE,
    BOOKING_DELETE_TYPE,
    ROLE_REQUEST_CREATE_TYPE,
    ROLE_REQUEST_REJECT_TYPE,
    ROLE_REQUEST_LIVE_TYPE,
    BOOKING_POST,
    BOOKING_PATCH,
    BOOKING_DELETE,
    RESOURCE_SKILL_EXPIRY,
    RESOURCE_MANAGER_SKILL_EXPIRY,
    RESOURCE_SKILL_RECOMMENDATION
} = NOTIFICATION_EVENTS;

const { CREATE_BOOKING, MARK_AS_READ, MARK_AS_UNREAD, DELETE_NOTIFICATION } = NOTIFICATIONS_HISTORY_ACTIONS;

const findData = (param1, param2, list) => {
    return list.find(item => item[param1] === param2);
};

export const getFixedNumber = (number) => {
    const numberToFix = !number ? 0 : number ;

    return Number.isInteger(numberToFix) ? numberToFix : numberToFix.toFixed(2);
};

const getNotificationDescription = (notificationDescription = {}) => {
    if (notificationDescription.bookingseries_guid) {
        const bookingSeries = JSON.parse(notificationDescription.bookingseries_booking);

        return {
            ...notificationDescription,
            loading: bookingSeries.booking_loading || null,
            fixedtime: bookingSeries.booking_fixedtime || 0,
            hoursperday: bookingSeries.booking_hours_per_day || null,
            time: bookingSeries.booking_time || null
        };
    }

    return { ...notificationDescription };
};

const getTimeAllocationValue = (event, notificationDescription = {}, notificationsTimeAllocationTypes = [], staticMessages) => {

    const notificationDescriptionValues = getNotificationDescription(notificationDescription);

    const { fixedtime = 0 } = notificationDescriptionValues;

    //here this approach is temp, as server has both old structure and new structure. Eventually will remove old implementation
    const oldNotificationPayloadStructure = isOldNotificationPayloadStructure(event);
    const timeAllocationData = findData('key', fixedtime, notificationsTimeAllocationTypes);
    const timeAllocaitonDataKey = oldNotificationPayloadStructure ? timeAllocationData['name'] : timeAllocationData['name'].toLowerCase();
    const timeAllocationFixedTime = getFixedNumber(notificationDescriptionValues[timeAllocaitonDataKey]);
    const timeAllocationDataSuffix = staticMessages[timeAllocationData['suffix']];
    let timeAllocation = '';
    switch (timeAllocationData.key) {
        case 0:
        case 3: {
            timeAllocation = `(${timeAllocationFixedTime}${timeAllocationDataSuffix})`;
            break;
        }
        case 1: {
            const hoursPerDayData = findData('key', 2, notificationsTimeAllocationTypes);
            const hoursePerDayKey = oldNotificationPayloadStructure ? hoursPerDayData['name'] : hoursPerDayData['name'].toLowerCase();
            const hoursValue = notificationDescriptionValues[hoursePerDayKey];
            const hours = getFixedNumber(hoursValue);
            const hoursPerDaySuffix = staticMessages[hoursPerDayData['suffix']];
            timeAllocation = `(${timeAllocationFixedTime} ${timeAllocationDataSuffix} (${hours} ${hoursPerDaySuffix}))`;
            break;
        }
        case 2: {
            const timeData = findData('key', 1, notificationsTimeAllocationTypes);
            const timeDataKey = oldNotificationPayloadStructure ? timeData['name'] : timeData['name'].toLowerCase();
            const timeValue = notificationDescriptionValues[timeDataKey];
            const time = getFixedNumber(timeValue);
            const timeSectionSuffix = staticMessages[timeData['suffix']];
            timeAllocation = `(${timeAllocationFixedTime} ${timeAllocationDataSuffix} (${time} ${timeSectionSuffix}))`;
            break;
        }
        case 4: {
            const hoursPerDayData = findData('key', 2, notificationsTimeAllocationTypes);
            const hoursePerDayKey = oldNotificationPayloadStructure ? hoursPerDayData['name'] : hoursPerDayData['name'].toLowerCase();
            const hoursValue = notificationDescriptionValues[hoursePerDayKey];
            const hours = getFixedNumber(hoursValue);
            const hoursPerDaySuffix = staticMessages[hoursPerDayData['suffix']];
            timeAllocation = `(${hours} ${hoursPerDaySuffix} (${timeAllocationFixedTime} ${timeAllocationDataSuffix}))`;
            break;
        }
        default:
            break;
    }

    return timeAllocation;
};

//here this approach is temp, as server has both old structure and new structure. Eventually will remove old implementation

export const getNotificationEventMessage = (event, staticMessages, notificationsTimeAllocationTypes, unconfirmedBookingsData) => {
    const { notificationDescription = {}, notificationPreferenceKey = '' } = event;
    const timeAllocation = getTimeAllocationValue(event, notificationDescription, notificationsTimeAllocationTypes, staticMessages);
    let OldPlaceholderValues = {}, newPlaceholderValues = {};
    const oldNotificationStructure = isOldNotificationPayloadStructure(event);
    const isBookingSeriesNotification = isBookingSeriesNotificationStructure(event);
    const skillExpiryNotification = isSkillExpiryNotification(notificationPreferenceKey);
    const { bookingtype = '' } = notificationDescription;

    if (oldNotificationStructure && !isBookingSeriesNotification) {
        let { jobname = '', startdate = '', enddate = '', resourcename = '', editorname = '' } = notificationDescription;
        OldPlaceholderValues = {
            jobName: jobname,
            timeAllocation,
            resourceName: resourcename,
            editorName: editorname,
            startDate: formatDate(startdate, getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR)),
            endDate: formatDate(startOfDayAtMidnight(enddate), getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR))
        };
    } else if (isBookingSeriesNotification) {
        let { jobname = '', bookingseries_booking = {}, editorname = '', bookingseries_until = '', bookingseries_recurrence = '', bookingseries_recurrency = '' } = notificationDescription;
        const bookingSeries = JSON.parse(bookingseries_booking);

        newPlaceholderValues = {
            jobName: jobname,
            timeAllocation,
            editorName: editorname,
            startDate: formatDate(bookingSeries.booking_start, getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR)),
            endDate: formatDate(startOfDayAtMidnight(bookingSeries.booking_end), getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR)),
            untilDate: formatDate(bookingseries_until, getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR)),
            interval: `${bookingseries_recurrence} ${bookingseries_recurrency}`
        };
    } else if (skillExpiryNotification) {
        let { expiryDay = 0, resourceDescription = '' } = notificationDescription;
        newPlaceholderValues = {
            expiryDay,
            resourceName:resourceDescription
        };
    } else {
        let { jobname = '', startdate = '', enddate = '', resource_description = '', editorname = '' } = notificationDescription;
        newPlaceholderValues = {
            jobName: jobname,
            timeAllocation,
            resourceName: resource_description,
            editorName: editorname,
            startDate: formatDate(parseUtcToLocalDate(parseToUtcDate(startdate)), getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR)),
            endDate: formatDate(startOfDayAtMidnight(parseToUtcDate(enddate)), getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR))
        };
    }
    const placeholderValues = oldNotificationStructure && !isBookingSeriesNotification ? OldPlaceholderValues : newPlaceholderValues;

    const { bookingAssignedLabel, bookingUpdateLabel, bookingDeleteLabel, roleRequestCreateLabel, roleRequestRejectLabel,
        roleRequestLiveLabel, repeatBookingAssignedLabel, repeatBookingUpdateLabel, repeatBookingDeleteLabel,
        resourceSkillExpiryLabel, resourceManagerSkillExpiryLabel, resourceSkillRecommendationLabel } = populateStringTemplates(staticMessages, placeholderValues);

    let result = '';
    switch (notificationPreferenceKey.toLowerCase()) {
        case BOOKING_POST.toLowerCase(): {
            if (notificationDescription.bookingseries_guid) {
                result = repeatBookingAssignedLabel;
            } else {
                result = bookingAssignedLabel;
            }
            break;
        }
        case BOOKING_PATCH.toLowerCase(): {
            if (unconfirmedBookingsData === false) {
                if (bookingtype === BOOKING_TYPES.UNCONFIRMED) {
                    result = bookingDeleteLabel;
                } else if (bookingtype === BOOKING_TYPES.PLANNED) {
                    result = bookingAssignedLabel;
                }
            } else {
                result = notificationDescription.bookingseries_guid
                    ? repeatBookingUpdateLabel
                    : bookingUpdateLabel;
            }
            break;
        }
        case BOOKING_DELETE.toLowerCase(): {
            if (notificationDescription.bookingseries_guid) {
                result = repeatBookingDeleteLabel;
            } else {
                result = bookingDeleteLabel;
            }
            break;
        }
        case ROLE_REQUEST_CREATE_TYPE.toLowerCase(): {
            result = roleRequestCreateLabel;
            break;
        }
        case ROLE_REQUEST_REJECT_TYPE.toLowerCase(): {
            result = roleRequestRejectLabel;
            break;
        }
        case ROLE_REQUEST_LIVE_TYPE.toLowerCase(): {
            result = roleRequestLiveLabel;
            break;
        }
        case RESOURCE_SKILL_EXPIRY.toLowerCase() : {
            result = resourceSkillExpiryLabel;
            break;
        }
        case RESOURCE_MANAGER_SKILL_EXPIRY.toLowerCase() : {
            result = resourceManagerSkillExpiryLabel;
            break;
        }
        case RESOURCE_SKILL_RECOMMENDATION.toLowerCase() : {
            result = resourceSkillRecommendationLabel;
            break;
        }
        default:
            break;
    }

    return result;
};

/**
 * Returns Details about the event message
 *
 * @param {Object} event Notification Event
 * @param {Object} staticMessages Static message Object
 * @returns {Array} Array with strings with event details
 */
export const getNotificationEventMessageDetails = (event, staticMessages) => {
    const { notificationDescription = {} } = event;
    const { skills = [] } = notificationDescription;

    const skillExpiryDetails = skills.map(skill => {
        const { skillExpiryLabel } = populateStringTemplates(staticMessages, {
            skillName: skill.skillName,
            entityType: skill.entityType.toLowerCase(),
            expiryDate:formatDate(skill.expiryDate, getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR)) });

        return skillExpiryLabel;
    });

    if (skillExpiryDetails.length > 2) {
        // Only Show 1 skill in detail if it has more than 2
        const { extraSkillExpiryLabel } = populateStringTemplates(staticMessages, { count: skillExpiryDetails.length - 1 });

        return [skillExpiryDetails[0], extraSkillExpiryLabel];
    }

    return skillExpiryDetails;
};

export const getMappedNotificationsTabs = (notificationsTabs = []) => {

    let mappedNotificationsTabs = notificationsTabs.map(item => {
        return {
            tabName: item.tabName,
            tabDefaultName: item.tabName
        };
    });

    return mappedNotificationsTabs;
};

export const getAllNotificationsTabs = (state) => {
    const hasManagerRequest = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state);

    return hasManagerRequest ? NEW_NOTIFICATION_TABS_KEY : NOTIFICATION_TABS_KEY;
};

export const isValidNotificationsTabExists = (tab = '', notificationStaticMessagesTabs) => {
    const notificationTabs = getNotificationsTabs(notificationStaticMessagesTabs) || [];

    return notificationTabs.find(item => {
        return item.tabName.toLowerCase() === tab.toLowerCase();
    });
};

export const getNotificationsTabs = (notificationStaticMessagesTabs) => {
    const tabs = [];

    Object.keys(notificationStaticMessagesTabs).forEach((item, index) => {
        const label = notificationStaticMessagesTabs[item];
        tabs.push({
            key:index,
            tabName: label,
            tabDefaultName: label
        });
    });

    return tabs;
};


export const shouldShowNotificationItemFilter = (notificationPreferenceKey, isMarkedRead, item, notificationId = '', contextualMenuProps) => {
    const { notificationHistory = {}, userEntityAccessWrapper } = contextualMenuProps;
    const { notificationsHistoryData = [] } = notificationHistory;
    const notification = notificationsHistoryData.find(notification => notification.guid === notificationId) || {};
    const entityId = getEntityIdFromNotificationDescription(notification);
    const { notificationDescription = {} } = notification;
    const { entityName = '' } = notificationDescription;
    let hasAccess = false;

    if (entityName === TABLE_NAMES.BOOKING) {
        hasAccess = userEntityAccessWrapper(TABLE_NAMES.BOOKING, [entityId], ENTITY_ACCESS_TYPES.EDIT, CREATE_FNAS_PER_TABLENAME[TABLE_NAMES.BOOKING]);
    } else {
        hasAccess = userEntityAccessWrapper(TABLE_NAMES.ROLEREQUEST, [entityId], ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[TABLE_NAMES.ROLEREQUEST], ENTITY_ACTION_KEYS.MAKE_LIVE, ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MAKE_LIVE);
    }

    let showItem = false;
    switch (item.onClickActionType) {
        case CREATE_BOOKING: {
            if (notificationPreferenceKey.toLowerCase() === ROLE_REQUEST_CREATE_TYPE.toLowerCase() && hasAccess) {
                showItem = true;
            }
            break;
        }
        case MARK_AS_READ: {
            showItem = !isMarkedRead;
            break;
        }
        case MARK_AS_UNREAD: {
            showItem = isMarkedRead;
            break;
        }
        case DELETE_NOTIFICATION: {
            showItem = true;
            break;
        }
        default: {
            showItem = false;
            break;
        }
    }

    return showItem;
};

const notificationHistoryListActionCreators = {
    [MARK_AS_READ]: (action, dispatch, state) => {
        const { entitiesId } = action;
        dispatch(onChangeNotificationReadUnreadStatus(entitiesId[0], true));
    },
    [MARK_AS_UNREAD]: (action, dispatch, state) => {
        const { entitiesId } = action;
        dispatch(onChangeNotificationReadUnreadStatus(entitiesId[0], false));
    },
    [DELETE_NOTIFICATION]: (action, dispatch) => {
        const { entitiesId = [] } = action;
        dispatch(deleteNotification(entitiesId));
    },
    [CREATE_BOOKING]: (action, dispatch, state) => {
        const { entitiesId } = action;
        const notification = getNotificationByIdSelector(state)(entitiesId[0]);
        const entityId = getEntityIdFromNotificationDescription(notification);
        dispatch(createBookingAction(entityId));
    }
};

export const getEntityIdFromNotificationDescription = (notification) => {
    const { notificationDescription = {} } = notification;
    const { entityName = '' } = notificationDescription;
    const remapEntityObject = Object.keys(REMAP_ENTITY_TITLES).find(key => key.toLowerCase() === entityName.toLowerCase());
    const entityId = remapEntityObject ? notification.notificationDescription[`${REMAP_ENTITY_TITLES[remapEntityObject].toLowerCase()}_guid`] : DUMMY_GUID;

    return entityId;
};

export const getNotificationListContextItems = (staticMessages) => {
    return [
        {
            label: staticMessages.markAsReadLabel,
            type: 'MenuItem',
            onClickActionType: MARK_AS_READ,
            options: {
                isEntityDependant: false,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'default'
            }
        },
        {
            label: staticMessages.markAsUnreadLabel,
            type: 'MenuItem',
            onClickActionType: MARK_AS_UNREAD,
            options: {
                isEntityDependant: false,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'default'
            }
        },
        {
            label: staticMessages.deleteLabel,
            type: 'MenuItem',
            onClickActionType: DELETE_NOTIFICATION,
            options: {
                isEntityDependant: false,
                singularForm: true,
                capitalized: false,
                labelTemplate: 'default'
            }
        },
        {
            label: staticMessages.makeLiveLabel,
            type: 'MenuItem',
            onClickActionType: CREATE_BOOKING,
            workflowAccessType: ROLEREQUEST_WORKFLOW_ACCESS_TYPES.CAN_MAKE_LIVE,
            entityAccess: ENTITY_ACCESS_TYPES.EDIT,
            options: {
                tableName: TABLE_NAMES.BOOKING
            }
        }
    ];
};

export const getNotificationListItemContextMenuProps = (item, index, contextualMenuProps) => {
    const { notificationId, notificationPreferenceKey, isMarkedRead } = item;
    const { notificationActionsStaticMessages, moreOptionsButtonLabel = 'More options' } = contextualMenuProps;

    return {
        actionProps: {
            entitiesId: [notificationId]
        },
        config: {
            items: getNotificationListContextItems(notificationActionsStaticMessages),
            key: `member_contextual_menu_${notificationId}`,
            icon: 'ellipsis',
            type: 'Menu',
            visible: true,
            ariaLabel: moreOptionsButtonLabel
        },
        visible: false,
        shouldShowItem: (item) => shouldShowNotificationItemFilter(notificationPreferenceKey, isMarkedRead, item, notificationId, contextualMenuProps),
        getItemDisabled: ()=> false,
        setVisibility: ()=> {},
        getPopupContainer: trigger => trigger.parentNode
    };
};

export const notificationHistoryListActionCreationFunc = (action, dispatch, state) => {
    if (notificationHistoryListActionCreators[action.actionType]) {
        notificationHistoryListActionCreators[action.actionType](action, dispatch, state);
    }
};

export const getEntityWindowOperationForNotification = (notificationPreferenceKey, userEntityAccessWrapper, tableName, entityId) => {
    let operation = ENTITY_WINDOW_OPERATIONS.READ;
    const userHasEntityAccess = userEntityAccessWrapper(tableName, [entityId], ENTITY_ACCESS_TYPES.EDIT, EDIT_FNAS_PER_TABLENAME[tableName]);

    if (userHasEntityAccess) {
        switch (notificationPreferenceKey.toLowerCase()) {
            case BOOKING_ASSIGN_TYPE.toLowerCase():
            case BOOKING_UPDATE_TYPE.toLowerCase():
            case BOOKING_POST.toLowerCase():
            case BOOKING_PATCH.toLowerCase():
            case ROLE_REQUEST_CREATE_TYPE.toLowerCase():
            case RESOURCE_MANAGER_SKILL_EXPIRY.toLowerCase():
            case RESOURCE_SKILL_EXPIRY.toLowerCase():
            case RESOURCE_SKILL_RECOMMENDATION.toLowerCase(): {
                operation = ENTITY_WINDOW_OPERATIONS.EDIT;
                break;
            }

            case ROLE_REQUEST_REJECT_TYPE.toLowerCase():
            case ROLE_REQUEST_LIVE_TYPE.toLowerCase(): {
                operation = ENTITY_WINDOW_OPERATIONS.READ;
                break;
            }
            default: {
                operation = null;
            }
        }
    }

    return operation;
};

export const getEntityWindowCollectionAliasForNotification = (tableName) => {
    switch (tableName) {
        case TABLE_NAMES.ROLEREQUEST:
            return TABLE_NAMES.ROLEREQUEST;
        case TABLE_NAMES.RESOURCE:
            return TABLE_NAMES.RESOURCE;
        default:
            NOTIFICATIONS_BOOKING_ALIAS;
    }
};

export const getSortedNotificationsByDate = (notifications = []) => {
    return sortBy(notifications, (notification) => {
        return parseToUtcDate(notification.notificationDate);
    }).reverse();
};

export const getLastNotificationInTheList = (notificationHistoryData = []) =>{
    return notificationHistoryData.length > 0 ? notificationHistoryData[notificationHistoryData.length - 1] : null;
};

export const convertObjectToQuryParams = (paramsObject = {}) => {
    const convertedParams = [];

    Object.keys(paramsObject).forEach(param => {
        if (paramsObject[param]) {
            convertedParams.push(`${param}=${paramsObject[param]}`);
        }
    });

    return convertedParams.join('&');
};

export const getNotificationQueryParams = (existingNotifications) => {
    const params = {
        fromId: '',
        pageSize: DEFAULT_NOTIFICATION_PAGESIZE
    };
    const lastNotification = getLastNotificationInTheList(existingNotifications);

    if (lastNotification) {
        params.fromId = lastNotification.guid;
    }

    return convertObjectToQuryParams(params);
};

export const updateRowCount = (rowCount, offset) => {
    return rowCount + offset;
};


export const isOldNotificationPayloadStructure = (notificationItem = {}) => {

    const { notificationDescription = '' } = notificationItem;

    return 'EntityGuid' in notificationDescription;
};

export const isBookingSeriesNotificationStructure = (notificationItem = {}) => {

    const { notificationDescription = '' } = notificationItem;

    return 'bookingseries_guid' in notificationDescription;
};

/**
 * Checks if the event is of type skill expiry notification
 *
 * @param {string} [notificationPreferenceKey=''] Event Key type
 * @returns {boolean} returns true if is skill expiry notification
 */
export const isSkillExpiryNotification = (notificationPreferenceKey = '') => {

    return notificationPreferenceKey.toLowerCase() === RESOURCE_SKILL_EXPIRY.toLowerCase()
    || notificationPreferenceKey.toLowerCase() === RESOURCE_MANAGER_SKILL_EXPIRY.toLowerCase();
};

export const getNotificationEntityName = (entityName = '') => {
    let actualEntityName = '';
    Object.keys(REMAP_ENTITY_TITLES).forEach(entity => {
        if (entity.toLowerCase() === entityName.toLowerCase()) {
            actualEntityName = REMAP_ENTITY_TITLES[entity].toLowerCase();
        }
    });

    return actualEntityName;
};

export function getFirstBookingInTheSeries(bookings) {
    if (!bookings || bookings.length === 0) return null;

    const sortedBookings = bookings.sort((a, b) =>
        new Date(a.booking_start) - new Date(b.booking_start));

    return sortedBookings[0];
}