import { map, mergeMap, filter, shareReplay, switchMap } from 'rxjs/operators';
import { empty, merge, of, isObservable, from, EMPTY } from 'rxjs';
import { ERROR_STATUS } from '../../constants';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';
import { jobHoursBudgetField } from '../../state/entityWindow/fieldsConfig';

export function getApiCallEpicConfig(
    action,
    apiName,
    apiHandler,
    successActionHandler,
    errorActionHandler,
    validationActionHandler,
    mapAction = (state$, action) => action,
    responseDataHandler = response => response,
    isBatchRequest
) {
    return {
        action,
        apiName,
        apiHandler,
        successActionHandler,
        validationActionHandler,
        errorActionHandler,
        mapAction,
        responseDataHandler,
        isBatchRequest
    }
}

const getServerErrors = (data) => data.filter(({ status }) => status === ERROR_STATUS);

const hasErrorStatus = (data) => {
    return data && data.status === ERROR_STATUS;
}

export function createAPICallEpic(alias, config) {
    return () => {
        const {
            action,
            apiName,
            apiHandler,
            validationActionHandler,
            successActionHandler,
            responseDataHandler = response => response,
            errorActionHandler,
            mapAction,
            isBatchRequest
        } = config;
        const actionName = alias ? `${action}_${alias}` : action;

        return (action$, state$, { apis }) => {
            const apiCallPreparations = action$
                .ofType(actionName)
                .pipe(
                    switchMap((action) => {
                        let result = action;
                        if(mapAction){
                            result = mapAction(state$, action);
                        }
                        return isObservable(result) ? result : of(result);
                    }
                    ),
                    filter(({payload}) => {
                        const isEnabledBudgetHoursAndRevenue = getFeatureFlagSelector(FEATURE_FLAGS.BUDGET_HOURS_AND_REVENUE)(state$.value);

                        if (!isEnabledBudgetHoursAndRevenue) {
                            if (payload && payload.tableData) {
                                delete payload.tableData[jobHoursBudgetField.name];
                            }
                        }

                        if(validationActionHandler){
                            return validationActionHandler(payload);
                        }
                        return true;
                    })
                );
 
            const apiCall = apiCallPreparations.pipe(
                filter(action => !action.errorsFound),
                mergeMap(({payload}) => apiHandler(apis[apiName], payload),
                (action, data) => {
                    if (actionName === 'DIGEST_CREATE_WORKSPACE') {
                        console.log('createAPICallEpic:', {action, data});
                    }
                    return {payload: action.payload, data}
                }),
                shareReplay(1)
            );
 
            const apiCallAborted = apiCallPreparations.pipe(
                filter(action => action.errorsFound)
            );
 
            const apiCallSuccess = apiCall.pipe(
                filter(({data}) => {
                    let result = !hasErrorStatus(data);
 
                    if (Array.isArray(data)) {
                        const serverErrors = getServerErrors(data);
                        result = isBatchRequest ? serverErrors.length < data.length : serverErrors.length === 0;
                    }
 
                    return result;
                }),
                switchMap(({payload, data}) => {
                    let successActions = [];

                    if (successActionHandler) {
                        let successHandlerResult = successActionHandler(alias, payload, responseDataHandler(data, payload, state$.value));

                        if (Array.isArray(successHandlerResult)) {
                            successHandlerResult.forEach(successAction => successActions.push(successAction));
                        } else {
                            successActions.push(successHandlerResult);
                        }
                    }

                    return from(successActions);
                })
            );
 
            const apiCallError = apiCall.pipe(
                filter(({data}) => {
                    let result = hasErrorStatus(data);
 
                    if (Array.isArray(data)) {
                        result = getServerErrors(data).length > 0;
                    }
 
                    return result;
                }),
                map(({payload, data}) => {
                    let handler = empty();
 
                    if(errorActionHandler){
                        handler = errorActionHandler(alias, payload, data);
                    }
 
                    return handler;
                })
            );
 
            return merge(
                merge(
                    apiCallSuccess,
                    apiCallError
                ),
                apiCallAborted
            );
        }
    }
}