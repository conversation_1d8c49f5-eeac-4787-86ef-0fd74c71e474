import { combineEpics } from 'redux-observable';
import { empty, EMPTY, forkJoin, of } from 'rxjs';
import { format } from 'date-fns';
import uuidv4 from 'uuid/v4';
import * as actionTypes from '../actions/actionTypes';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { createAPICallEpic, getApiCallEpicConfig } from './epicGenerators/apiCallEpicGenerator';
import { generateDefaultNewPlanName } from '../utils/planNameGenerationUtils';
import { combineAPIEpics } from './middlewareExtensions';
import { filter, map, switchMap } from 'rxjs/operators';
import { getCurrentDate } from '../utils/dateUtils';
import { getActiveListViewSelector, getPageFiltersSelector, getWorkspaceViewTypeSelector } from '../selectors/listPageSelectors';
import { JOBS_PAGE_ALIAS, TABLE_NAMES } from '../constants';
import { RESOURCES_PAGE_ALIAS } from '../constants/resourcesPageConsts';
import { WORKSPACE_VIEW_TYPE_FIELDS } from '../constants/fieldConsts';
import {
    digestCreateListPageWorkspaceSuccess,
    updateWorkspaceViewType,
    deleteListPageWorkspaceSuccess,
    renameListPageWorkspaceSuccess,
    moveListPageWorkspaceSuccess,
    copyListPageWorkspaceSuccess
} from '../actions/listPageActions';
import { getPageState } from '../selectors/pagesSelectors';
import { URL_PARAMS, FEATURE_FLAGS } from '../constants/globalConsts';
import { updatePageParams } from '../actions/pageStateActions';
import { replaceUrl } from '../actions/navigateActions';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { commandBarPopulateWorkspacesSection, commandBarRepopulateWorkspacesSection } from '../actions/commandBarActions';

const workspacesApiName = 'workspaces';
const tableDataApiName = 'tableData';

const {
    WORKSPACE_SURROGATE_ID,
    WORKSPACE_NAME
} = URL_PARAMS;

const buildPageParams = (workspaceGuid, workspaceSurrogateId, workspaceName) => ({
    workspaceGuid,
    [WORKSPACE_SURROGATE_ID]: workspaceSurrogateId,
    [WORKSPACE_NAME]: workspaceName
});

export const ensureWorkspaceNameSet$ = (api, payload) => {
    const { workspace_description, privatePlans, newPlanLabel } = payload;

    const workspaceName = !workspace_description || workspace_description === ''
        ? generateDefaultNewPlanName(privatePlans, newPlanLabel)
        : workspace_description;

    return of(workspaceName);
};

const handleLoadWorkspacesEpic = (action$, state$, { apis }) => {
    return action$.ofType(
        actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES
    ).pipe(
        switchMap((action) => {
            const { payload } = action;
            const selection = {
                fields: [
                    { fieldName: WORKSPACE_VIEW_TYPE_FIELDS.GUID },
                    { fieldName: WORKSPACE_VIEW_TYPE_FIELDS.DESCRIPTION }
                ],
                filter: {
                    filterGroupOperator: 'And',
                    filterLines: []
                }
            };

            return apis[tableDataApiName].getFilterTableAccessData$(TABLE_NAMES.WORKSPACE_VIEW_TYPE, selection)
                .pipe(map(result => {
                    return { result, payload };
                }));
        }),
        switchMap(({ result, payload }) => {
            if (!result || result.length <= 0) {
                return EMPTY;
            }

            let wsViewType = result.find(viewType => viewType[WORKSPACE_VIEW_TYPE_FIELDS.DESCRIPTION] === 'Lists');

            if (!wsViewType) {
                // Show error creating workspace
                return EMPTY;
            }

            return forkJoin(
                apis[workspacesApiName].getWorkspaces$(payload.groupByType, wsViewType[WORKSPACE_VIEW_TYPE_FIELDS.GUID]),
                apis[workspacesApiName].getMostRecentlyUsed$(payload.getWorkspaceDetail || false)
            ).pipe(map(result => {
                return { result, wsViewType };
            }));
        }),
        switchMap(({ result, wsViewType }) => {
            console.log('workspaces result:', result);

            return of(
                updateWorkspaceViewType(wsViewType),
                {
                    type: actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL,
                    payload: {
                        data: result
                    }
                }
            );
        })
    );
};

const handleListPageLoadWorkspacesSuccessEpic = (action$, state$) => {
    return action$.ofType(
        actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL
    ).pipe(
        map(() => {
            const workspaces = state$.value.listPage.workspaces;
            const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state$.value);

            return commandBarPopulateWorkspacesSection(workspaces, listPageAndBulkUpdateFeatureFlag);
        })
    );
};

const handleListPageWorkspaceChangeEpic = (action$, state$) => {
    return action$.ofType(
        actionTypes.LIST_PAGE_ACTIONS.RENAME_WORKSPACE_SUCCESS,
        actionTypes.LIST_PAGE_ACTIONS.MOVE_WORKSPACE_SUCCESS,
        actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE_SUCCESS,
        actionTypes.LIST_PAGE_ACTIONS.COPY_WORKSPACE_SUCCESS
    ).pipe(
        map(() => {
            const workspaces = state$.value.listPage.workspaces;
            const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state$.value);

            return commandBarRepopulateWorkspacesSection(workspaces, listPageAndBulkUpdateFeatureFlag);
        })
    );
};

// const renameWorkspaceDataRequest$ = (api, payload) => {
//     const { workspaceGuid, workspaceData } = payload;

//     return api.updateWorkspace$(workspaceGuid, workspaceData);
// };

// const deleteWorkspaceDataRequest$ = (api, payload) => {
//     const { workspaceGuid } = payload;

//     return api.deleteWorkspace$(workspaceGuid);
// };

export const handleListPageWorkspaceRenameSuccessEpic = (action$, state$) => {
    return action$.ofType(actionTypes.LIST_PAGE_ACTIONS.RENAME_WORKSPACE_SUCCESS).pipe(
        switchMap(({ payload }) => {
            const {
                workspaceGuid,
                workspaceData: {
                    workspace_description: workspaceName
                }
            } = payload;

            const {
                workspaceGuid: currentWorkspaceGuid,
                workspaceSurrogateId: currentWorkspaceSurrogateId
            } = getPageState(state$.value, JOBS_PAGE_ALIAS).params || {};

            const pageParams = buildPageParams(currentWorkspaceGuid, currentWorkspaceSurrogateId, workspaceName);

            return workspaceGuid === currentWorkspaceGuid
                ? of(updatePageParams(JOBS_PAGE_ALIAS, pageParams), replaceUrl(pageParams))
                : empty();
        })
    );
};


const renameWorkspaceDataRequest$ = (api, payload) => {
    const { workspaceGuid, workspaceData } = payload;

    return api.updateWorkspace$(workspaceGuid, workspaceData);
};

const deleteWorkspaceDataRequest$ = (api, payload) => {
    const { workspaceGuid } = payload;

    return api.deleteWorkspace$(workspaceGuid);
};

const moveWorkspaceDataRequest$ = (api, payload) => {
    const { workspaceGuid, workspaceData } = payload;

    return api.updateWorkspace$(workspaceGuid, workspaceData);
};

const copyWorkspaceDataRequest$ = (api, payload) => {
    const { workspaceGuid, workspaceData } = payload;

    return api.copyWorkspace$(workspaceGuid, workspaceData);
};

const createListPageRenameWorkspaceEpic = (successActionHandler = renameListPageWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LIST_PAGE_ACTIONS.RENAME_WORKSPACE, workspacesApiName, renameWorkspaceDataRequest$, successActionHandler, null)
    );

const createListPageDeleteWorkspaceEpic = (successActionHandler = deleteListPageWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE, workspacesApiName, deleteWorkspaceDataRequest$, successActionHandler, null)
    );

const createListPageMoveWorkspaceEpic = (successActionHandler = moveListPageWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LIST_PAGE_ACTIONS.MOVE_WORKSPACE, workspacesApiName, moveWorkspaceDataRequest$, successActionHandler, null)
    );

const createListPageCopyWorkspaceEpic = (successActionHandler = copyListPageWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LIST_PAGE_ACTIONS.COPY_WORKSPACE, workspacesApiName, copyWorkspaceDataRequest$, successActionHandler, null)
    );

// export const listPageRenameWorkspaceEpic = createListPageRenameWorkspaceEpic();
// export const listPageDeleteWorkspaceEpic = createListPageDeleteWorkspaceEpic();

const handleDigestCreateWorkspaceSuccessEpic = (action$, state$, { apis }) => {
    return action$.ofType(
        actionTypes.LIST_PAGE_ACTIONS.CREATE_WORKSPACE_SUCCESSFUL
    ).pipe(
        switchMap(({ payload }) => {
            const state = state$.value;
            const wsViewType = getWorkspaceViewTypeSelector(state);

            if (!wsViewType) {
                // Show error creating workspace
                return EMPTY;
            }

            const { data, wsDescription, wsAccessType, wsEditRights } = payload;
            const newWorkspaceDescription = data ? data : wsDescription;
            const activeListView = getActiveListViewSelector(state);
            const currentUserGuid = getApplicationUserId(state);
            const getPageFilters = getPageFiltersSelector(state);

            console.log('activeListView:', activeListView);

            const newWorkspaceStructure = {
                workspace_description: newWorkspaceDescription,
                workspace_accesstype: wsAccessType,
                workspace_editrights: wsEditRights,
                workspace_colourtheme_guid: null,
                workspace_colour_field_name: null,
                workspace_change_date: format(getCurrentDate(), 'YYYY-MM-DDTHH:mm:ss.SSS'),
                workspace_author_guid: currentUserGuid,
                workspace_change_author_guid: currentUserGuid,
                workspace_custom_colour_field: [],
                workspace_wsviewtype_guid: wsViewType[WORKSPACE_VIEW_TYPE_FIELDS.GUID]
            };

            const workSpaceSettings = {
                activeListView,
                views: {
                    [JOBS_PAGE_ALIAS]: {
                        filters: getPageFilters(JOBS_PAGE_ALIAS, TABLE_NAMES.JOB)
                    },
                    [RESOURCES_PAGE_ALIAS]: {
                        filters: getPageFilters(RESOURCES_PAGE_ALIAS, TABLE_NAMES.RESOURCE)
                    },
                }
            };

            newWorkspaceStructure['workspace_settings'] = JSON.stringify(workSpaceSettings);

            console.log('handleDigestCreateWorkspaceSuccessEpic:', {newWorkspaceStructure, workSpaceSettings});

            return apis[workspacesApiName].insertWorkspace$(newWorkspaceStructure);
        }),
        switchMap((result) => {
            console.log('workspace created:', result);

            // After creating a workspace, reload workspaces to refresh the dropdown
            return of({
                type: actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES,
                payload: {
                    groupByType: false
                }
            });
        })
    );
};

export const digestCreateWorkspaceEpic = (successActionHandler = digestCreateListPageWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LIST_PAGE_ACTIONS.CREATE_WORKSPACE, workspacesApiName, ensureWorkspaceNameSet$, successActionHandler, null)
    );

export const listPageRenameWorkspaceEpic = createListPageRenameWorkspaceEpic();
export const listPageDeleteWorkspaceEpic = createListPageDeleteWorkspaceEpic();
export const listPageMoveWorkspaceEpic = createListPageMoveWorkspaceEpic();
export const listPageCopyWorkspaceEpic = createListPageCopyWorkspaceEpic();

export default combineEpics(
    combineAPIEpics(
        digestCreateWorkspaceEpic(digestCreateListPageWorkspaceSuccess)(),
        listPageRenameWorkspaceEpic,
        listPageDeleteWorkspaceEpic,
        listPageMoveWorkspaceEpic,
        listPageCopyWorkspaceEpic
    ),
    handleLoadWorkspacesEpic,
    handleListPageLoadWorkspacesSuccessEpic,
    handleListPageWorkspaceChangeEpic,
    handleDigestCreateWorkspaceSuccessEpic,
    handleListPageWorkspaceRenameSuccessEpic
);