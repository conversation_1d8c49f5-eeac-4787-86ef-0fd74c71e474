import { ofType } from 'redux-observable';
import { mergeMap, map, catchError, withLatestFrom } from 'rxjs/operators';
import { of } from 'rxjs';
import { FETCH_OPTIONS, fetchOptionsSuccess, fetchOptionsError } from '../actions/configurableSelectActions';
import { Configurable_Select_API_Config } from '../api/configurableSelect.api';
import RxPhoenixHttp from '../api/RxPhoenixHttp/RxPhoenixHttp';

const http = new RxPhoenixHttp();

/**
 * Fetches and transforms options from the API
 * @param {string} searchTerm - Term to filter results
 * @param {string} sourceId - Identifier for the data source
 * @param {string} methodtype Defines the type of the request
 * @param {object} payload Defines the payload
 * @returns {Promise<Array<import('../types/transformedOptions.jsdoc').TransformedOption>>} Transformed options
 * @throws {Error} When API config is missing or request fails
 */
const fetchOptionsFromAPI = (searchTerm, sourceId, methodType, payload) => {
    const config = Configurable_Select_API_Config[sourceId];
    if (!config) {
        return of([]);
    }

    // Get the different properties based on the sourceId.
    const { endpoint, valueKey, labelKey, transformResponse } = config;

    // Return response based on the config property
    // Return object with label, value and originalItem property
    const mapResponse = (rawData = []) => {
        const data = transformResponse(rawData);

        // Object with value and label based on the config property and api response
        return data.map(item => ({
            value: item[valueKey],
            label: item[labelKey],
            originalItem: item
        }));
    };

    /**
     * Handles the error of API call
     * @param {string} type Type of the request
     * @param {HttpErrorResponse} error Error returned during the API call.
     */
    const handleError = (type, error) => {
        console.error(`${type} API Error:`, error);

        return of([]);
    };

    // If the method type is 'POST'
    if (methodType === 'POST') {
        return http.post(endpoint, payload).pipe(
            map(res => mapResponse(res.serverResponse)),
            catchError(err => handleError('POST', err))
        );
    } else {
        const queryString = searchTerm ? `search=${encodeURIComponent(searchTerm)}` : '';

        return http.get(endpoint, queryString).pipe(
            map(res => {
                if (!res.success) throw new Error(res.message);

                return mapResponse(res.serverResponse);
            }),
            catchError(err => handleError('GET', err))
        );
    }
};

/**
 * Redux-Observable epic for handling options fetching
 * @param {Observable} action$ - Stream of Redux actions
 * @param {Observable} state$ - Stream of Redux state
 * @returns {Observable} Stream of result actions
 */
export const fetchOptionsEpic = (action$, state$) =>
    action$.pipe(
        ofType(FETCH_OPTIONS),
        withLatestFrom(state$),
        mergeMap(([action, state]) => {
            const { sourceId, searchTerm, isTypeahead, methodType, payload } = action.payload;
            const sourceState = state.configurableSelect.sources[sourceId];

            if (!isTypeahead && sourceState?.fullData?.length > 0) {
                return of(fetchOptionsSuccess(sourceState.fullData, sourceId, isTypeahead));
            }

            return fetchOptionsFromAPI(searchTerm, sourceId, methodType, payload).pipe(
                map(response => fetchOptionsSuccess(response, sourceId, isTypeahead)),
                catchError(error => of(fetchOptionsError(error, sourceId)))
            );
        })
    );
