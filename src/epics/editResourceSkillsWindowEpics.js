import { combineEpics } from 'redux-observable';
import { map, switchMap, mergeMap } from 'rxjs/operators';
import { of, concat } from 'rxjs';
import { ERROR_STATUS } from '../constants';
import {
    editResourceSkillsWindowBuildSections,
    editResourceSkillsWindowLoadAutoCompleteSkillsSuccess,
    editResourceSkillsWindowLoadAutoCompleteSkillsError,
    editResourceSkillsWindowUpdateSkillsSuccess,
    editResourceSkillsWindowClose
} from '../actions/editResourceSkillsWindowActions';
import { getIgnoredResourceSkillsSelector, mergeResourceSkillsSelector } from '../selectors/resourceSkillsSelectors';
import { getGroupedSkills } from '../selectors/editSkillsWindowSelectors';
import {
    recommendationSkillsWindowLoadSuccess, recommendationSkillsWindowLoadError, populateAuthorizeResourceSkills,
    loadAuthorizeResourceSkillsFailure, resourceSkillsApprovalsPendingSuccess, resourceSkillsApprovalsPendingError,
    resourceSkillsApprovalsHistorySuccess, resourceSkillsApprovalsHistoryError
} from '../actions/resourceSkillsActions';
import { EDIT_RESOURCE_SKILLS_WINDOW, RECOMMENDATION_SKILLS, RESOURCE_SKILLS, RESOURCE_SKILLS_APPROVALS } from '../actions/actionTypes';
import { loadResourceSkills } from '../actions/resourceSkillsActions';
import { responseFollowUp } from './talentProfileEpics';
import { createAPICallEpic, getApiCallEpicConfig } from './epicGenerators/apiCallEpicGenerator';
import { ignoreErrorPipe, updateSkillsPipe } from '../api/pipes.api';
import { loadTalentProfileAuditSection } from '../actions/talentProfileActions';
import { TABLE_NAMES } from '../constants';
import { getToasterMessageConfig } from '../utils/plannerDataUtils';
import { setToasterConfig } from '../actions/adminSettings/adminSettingActions';
import { getTranslationsSelector } from '../selectors/internationalizationSelectors';

const resourceSkillsApiKey = 'resourceSkills';

const buildWindowSectionsEpic = (action$, state$) =>
    action$
        .ofType(EDIT_RESOURCE_SKILLS_WINDOW.OPEN)
        .pipe(map(({ payload: { entityId } }) => editResourceSkillsWindowBuildSections(getGroupedSkills(state$.value, entityId))));

export const getAutoCompleteSkillEpic = (api, { sectionId, searchTerm, maxResults, filterByResourcePermission }) =>
    api.withPipe(ignoreErrorPipe).getAutoCompleteSkills$(sectionId, searchTerm, maxResults, filterByResourcePermission);

const loadAutoCompleteSkillsEpic = createAPICallEpic(
    null,
    getApiCallEpicConfig(
        EDIT_RESOURCE_SKILLS_WINDOW.AUTOCOMPLETE.SEARCH_SKILLS,
        resourceSkillsApiKey,
        getAutoCompleteSkillEpic,
        editResourceSkillsWindowLoadAutoCompleteSkillsSuccess,
        editResourceSkillsWindowLoadAutoCompleteSkillsError
    )
)();

const loadRecommendationSkillsEpic = (action$, state$, { apis }) =>
    action$
        .ofType(RECOMMENDATION_SKILLS.LOAD_RECOMMENDATION_SKILLS)
        .pipe(
            switchMap(({ payload: { entityId } }) => {

                return apis[resourceSkillsApiKey]
                    .getResourceSkillsRecommendations$(entityId)
                    .pipe(
                        map(response => {
                            if (response.status === ERROR_STATUS) {
                                return recommendationSkillsWindowLoadError(entityId);
                            }

                            return recommendationSkillsWindowLoadSuccess(entityId, response);
                        })
                    );
            })
        );

const loadResourceSkillsApprovalsPendingEpic = (action$, state$, { apis }) =>
    action$
        .ofType(RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_PENDING)
        .pipe(
            switchMap(({ payload: { entityId } }) => {
                return apis[resourceSkillsApiKey]
                    .getResourceSkillsApprovalsPending$(entityId)
                    .pipe(
                        map(response => {
                            if (response.status === ERROR_STATUS) {
                                return resourceSkillsApprovalsPendingError(entityId);
                            }

                            return resourceSkillsApprovalsPendingSuccess(entityId, response);
                        })
                    );
            })
        );

/**
 * The epic is used to load the skill approval history.
 * @param {Observable} action$ Payload that contains action types.
 * @param {Observable} state$ Payload that contains redux state.
 * @param {{ apis: Object; }} param0 Its an array of function endpoints
 * @returns {Observable} Returns observable result.
 */
const loadResourceSkillsApprovalsHistoryEpic = (action$, state$, { apis }) =>
    action$
        .ofType(RESOURCE_SKILLS_APPROVALS.LOAD_APPROVALS_HISTORY)
        .pipe(
            switchMap(({ payload: { entityId, sortBy, pageSize, pageNumber } }) => {
                // Represent count from where the next set of record should be taken.
                const from = (pageNumber - 1) * pageSize;

                return apis[resourceSkillsApiKey]
                    .getResourceSkillsApprovalsHistory$(entityId, from, pageSize, sortBy)
                    .pipe(
                        map(response => {
                            if (response.status === ERROR_STATUS) {
                                return resourceSkillsApprovalsHistoryError(entityId);
                            }

                            return resourceSkillsApprovalsHistorySuccess(entityId, response, response.count, pageNumber);
                        })
                    );
            })
        );

const updateResourceSkillsEpic = (action$, state$, { apis }) =>
    action$
        .ofType(EDIT_RESOURCE_SKILLS_WINDOW.UPDATE.SKILLS)
        .pipe(
            switchMap(
                ({ payload: { entityId } }) => {
                    const skills = mergeResourceSkillsSelector(state$.value)(entityId);
                    const ignoredSkills = getIgnoredResourceSkillsSelector(state$.value)(entityId).map(
                        skillId => ({ id: skillId, isIgnored: true })
                    );

                    return apis[resourceSkillsApiKey].withPipe(updateSkillsPipe).updateResourceSkills$(entityId, [...skills, ...ignoredSkills]);
                },
                (action, result) => ([action, result])
            ),
            mergeMap(
                ([{ payload: { entityId } }, result]) => responseFollowUp(
                    result,
                    concat(
                        of(editResourceSkillsWindowUpdateSkillsSuccess()),
                        of(loadResourceSkills(entityId)),
                        of(loadTalentProfileAuditSection(TABLE_NAMES.RESOURCESKILL))
                    )
                )
            )
        );

/**
* The epic is used to load the resource skills allowed
* @param {*} action$
* @param {*} state$
* @returns {Observable<import('../types/authorizeResourceSkillDTO.jsdoc').AuthorizeResourceSkillDTO[]>}
*/
export const loadAuthorizedResourceSkills = (action$, state$, { apis }) => {
    return action$.ofType(RESOURCE_SKILLS.LOAD.AUTHORIZE_SKILLS).pipe(
        switchMap(() => {
            return apis[resourceSkillsApiKey].getAuthorizeResourceSkills$().pipe(
                map((response) => {
                    if (response.status === ERROR_STATUS) {
                        return loadAuthorizeResourceSkillsFailure();
                    }

                    return populateAuthorizeResourceSkills(response);
                })
            );
        })
    );
};

/**
* Epic to handle resending the approval request for resource skills.
* @param {*} action$
* @param {*} state$
* @returns On success, it closes the resource skills window and shows a success toaster message.
*/
export const resendApprovalRequest = (actions$, state$, { apis }) => {
    return actions$.ofType(EDIT_RESOURCE_SKILLS_WINDOW.RESEND_APPROVAL_REQUEST).pipe(
        switchMap(({ payload: { resourceId } }) => {
            return apis[resourceSkillsApiKey].resendApprovalRequest$(resourceId).pipe(
                mergeMap((response) => {
                        const translatedMessages = getTranslationsSelector(state$.value, { sectionName: 'skills' });
                        const customType = {
                            customMessage: translatedMessages.approvalRequestSent
                        };
                        const toasterConfig = getToasterMessageConfig(state$.value, null, null, customType, 1, null);
                        return concat( of(editResourceSkillsWindowClose()),
                            of(setToasterConfig(toasterConfig)))
                        
                })
            )
        })
    )
}

export default combineEpics(
    buildWindowSectionsEpic,
    loadAutoCompleteSkillsEpic,
    loadRecommendationSkillsEpic,
    loadResourceSkillsApprovalsPendingEpic,
    loadResourceSkillsApprovalsHistoryEpic,
    updateResourceSkillsEpic,
    loadAuthorizedResourceSkills,
    resendApprovalRequest
);
