import { combineEpics } from 'redux-observable';
import { switchMap, delay } from 'rxjs/operators';
import * as actionTypes from '../../actions/actionTypes';
import { PAGE_ACTIONS } from '../../actions/navigateActions';
import { setPageState } from '../../actions/pageStateActions';
import { of, concat, empty } from 'rxjs';
import { getApplicationUserId, userDataLoaded, getApplicationUserName, getApplicationUserSurrogateId, getApplicationUserSurrogateIdSelector } from '../../selectors/applicationUserSelectors';
import { JOBS_PAGE_ALIAS } from '../../constants/jobsPageConsts';
import { PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';
import { PLANNER_PAGE_ALIAS, TABLE_NAMES } from '../../constants';
import { getPagedData, getPageState } from '../../selectors/pagesSelectors';
import { parsePageParams, processUrlParams } from '../../utils/pageParamsUtils';
import {
    getJobsPageLoadStrategy, getJobsPageStrategyImplementingActions, getPlannerPageLoadStrategy, getPlannerPageStrategyImplementingActions, getProfilePageLoadStrategy,
    getProfilePageStrategyImplementingActions, commonPageStateChanged, plannerPageStateChanged, profilePageStateChanged, getRoleInboxLoadStrategy, getRoleInboxPageStrategyImplementingActions, getTimesheetsLoadStrategy, getTimesheetsPageStrategyImplementingActions,
    getNotificationsPageStrategyImplementingActions, getNotificationsLoadStrategy, getMarketplacePageStategyImplementatingActions, getMarketplaceLoadStategy, getTableViewLoadStategy, getTableViewPageStategyImplementatingActions, getRoleInboxPageConsequentLoadStrategyActions,
    getResourcesPageLoadStrategy,
    getResourcesPageStrategyImplementingActions
} from './pageLoadStrategiesUtils';
import { FEATURE_FLAGS, URL_PARAMS } from '../../constants/globalConsts';
import { getEntityStructure } from '../../selectors/entityStructureSelectors';
import { ROLE_INBOX_PAGE_ALIAS } from '../../constants/roleInboxPageConsts';
import { TIMESHEETS_PAGE_ALIAS } from '../../constants/timeSheetsPageConsts';
import { NEW_NOTIFICATION_TABS_KEY, NOTIFICATIONS_PAGE_ALIAS, NOTIFICATION_TABS_KEY } from '../../constants/notificationsPageConsts';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { getPersistItemPropForUser } from '../../localStorage';
import { MARKETPLACE_PAGE_ALIAS } from '../../constants/marketplacePageConsts';
import { TABLE_VIEW_PAGE_ALIAS } from '../../constants/tableViewPageConsts';
import { commandBarPopulateWorkspacesSection } from '../../actions/listPageActions';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { RESOURCES_PAGE_ALIAS } from '../../constants/resourcesPageConsts';

const PA_TYPES = actionTypes.PAGE_ACTIONS;
const {
    RESOURCE_SURROGATE_ID,
    RESOURCE_NAME
} = URL_PARAMS;

const getJobsUrlParams = (location, getAppUserSurrogateId) => {
    let params = parsePageParams(location, JOBS_PAGE_ALIAS);

    if (!params.pagesize) {
        const surrogateId = getAppUserSurrogateId();
        const options = getPersistItemPropForUser('persist:jobsPagePersist', 'pageState', surrogateId);
        params.pagesize = options.pagesize;
    }

    return processUrlParams(params);
};

export const openPlannerPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[PLANNER_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { location } = payload;
                const previousState = getPageState(state$.value, PLANNER_PAGE_ALIAS);
                const urlParams = parsePageParams(location, PLANNER_PAGE_ALIAS);
                const currentParams = processUrlParams(urlParams);
                const currentPageState = { params: currentParams };

                let openChain = [];

                if (plannerPageStateChanged(currentPageState, previousState)) {
                    const loadStrategy = getPlannerPageLoadStrategy(previousState, currentPageState);

                    openChain.push(
                        of(setPageState(PLANNER_PAGE_ALIAS, currentPageState)),
                        of(PAGE_ACTIONS.LOAD[PLANNER_PAGE_ALIAS](loadStrategy, previousState))
                    );
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadPlannerPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.LOAD[PLANNER_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const plannerPageState = getPageState(state$.value, PLANNER_PAGE_ALIAS);
                const { loadStrategy, previousPageState = {} } = payload;

                const actions = getPlannerPageStrategyImplementingActions(loadStrategy, plannerPageState.params, previousPageState.params);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openJobsPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[JOBS_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { location } = payload;
                const previousState = getPageState(state$.value, JOBS_PAGE_ALIAS);
                const getApplicationUserSurrogateId = getApplicationUserSurrogateIdSelector(state$.value);
                const currentParams = getJobsUrlParams(location, getApplicationUserSurrogateId);
                const currentPageState = {
                    params: currentParams
                };
                let openChain = [];
                const isListPageAndBulkUpdateEnabled = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state$.value);

                if (commonPageStateChanged(currentPageState, previousState)) {
                    const pagedData = getPagedData(state$.value, JOBS_PAGE_ALIAS)[TABLE_NAMES.JOB];
                    const loadStrategy = getJobsPageLoadStrategy(currentParams, { pagedData });

                    if (isListPageAndBulkUpdateEnabled) {
                        openChain.push(of(commandBarPopulateWorkspacesSection()));
                    }

                    openChain.push(
                        of(setPageState(JOBS_PAGE_ALIAS, currentPageState)),
                        of(PAGE_ACTIONS.LOAD[JOBS_PAGE_ALIAS](loadStrategy))
                    );
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadJobsPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.LOAD[JOBS_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const jobsPageState = getPageState(state$.value, JOBS_PAGE_ALIAS);
                const { loadStrategy } = payload;
                const { value: state } = state$;
                const entityStructure = getEntityStructure(state);
                const { displayFields } = state.jobsPage;
                const actions = getJobsPageStrategyImplementingActions(loadStrategy, jobsPageState.params, entityStructure, displayFields);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openResourcesPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[RESOURCES_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { location } = payload;
                const previousState = getPageState(state$.value, RESOURCES_PAGE_ALIAS);
                const params = parsePageParams(location, RESOURCES_PAGE_ALIAS);
                const currentParams = processUrlParams(params);
                const currentPageState = {
                    params: currentParams
                };
                let openChain = [];

                if (commonPageStateChanged(currentPageState, previousState)) {
                    const pagedData = getPagedData(state$.value, RESOURCES_PAGE_ALIAS)[TABLE_NAMES.RESOURCE];
                    const loadStrategy = getResourcesPageLoadStrategy(currentParams, { pagedData });

                    openChain.push(
                        of(setPageState(RESOURCES_PAGE_ALIAS, currentPageState)),
                        of(PAGE_ACTIONS.LOAD[RESOURCES_PAGE_ALIAS](loadStrategy))
                    );
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadResourcesPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.LOAD[RESOURCES_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const resourcesPageState = getPageState(state$.value, RESOURCES_PAGE_ALIAS);
                const { loadStrategy } = payload;
                const { value: state } = state$;
                const entityStructure = getEntityStructure(state);
                const { displayFields } = state.resourcesPage;
                const actions = getResourcesPageStrategyImplementingActions(loadStrategy, resourcesPageState.params, entityStructure, displayFields);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openProfilePageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[PROFILE_PAGE_ALIAS])
        .pipe(
            delay(100),
            switchMap((action) => {

                if (!userDataLoaded(state$.value)) {
                    //Try again until the logged user data has been loaded;
                    return of(action);
                }

                const { payload: { location } } = action;
                const params = parsePageParams(location, PROFILE_PAGE_ALIAS);
                const currentParams = processUrlParams(params);
                let currentPageState = {
                    params: currentParams
                };

                const openChain = [];

                if (!params[RESOURCE_SURROGATE_ID]) {
                    currentPageState = {
                        params: {
                            [RESOURCE_SURROGATE_ID]: getApplicationUserSurrogateId(state$.value),
                            [RESOURCE_NAME]: getApplicationUserName(state$.value),
                            resourceId: getApplicationUserId(state$.value)
                        }
                    };
                }

                const previousPageState = getPageState(state$.value, PROFILE_PAGE_ALIAS);

                if (profilePageStateChanged(currentPageState, previousPageState)) {
                    const loadStrategy = getProfilePageLoadStrategy();

                    openChain.push(of(setPageState(PROFILE_PAGE_ALIAS, currentPageState)));
                    openChain.push(of(PAGE_ACTIONS.LOAD[PROFILE_PAGE_ALIAS](loadStrategy)));
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadProfilePageEpic$ = (action$) => {
    return action$
        .ofType(PA_TYPES.LOAD[PROFILE_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { loadStrategy } = payload;
                const actions = getProfilePageStrategyImplementingActions(loadStrategy);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openRoleInboxPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[ROLE_INBOX_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { location } = payload;
                const previousState = getPageState(state$.value, ROLE_INBOX_PAGE_ALIAS);
                const params = parsePageParams(location, ROLE_INBOX_PAGE_ALIAS);
                const currentParams = processUrlParams(params);
                const currentPageState = {
                    params: currentParams
                };
                let openChain = [];

                if (commonPageStateChanged(currentPageState, previousState)) {
                    const pagedData = getPagedData(state$.value, ROLE_INBOX_PAGE_ALIAS)[TABLE_NAMES.ROLEREQUEST];
                    const loadStrategy = getRoleInboxLoadStrategy(currentParams, { pagedData });

                    openChain.push(
                        of(setPageState(ROLE_INBOX_PAGE_ALIAS, currentPageState)),
                        of(PAGE_ACTIONS.LOAD[ROLE_INBOX_PAGE_ALIAS](loadStrategy))
                    );
                } else {
                    const loadStrategyActions = getRoleInboxPageConsequentLoadStrategyActions();
                    loadStrategyActions.forEach(action => openChain.push(of(action)));
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadRoleInboxPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.LOAD[ROLE_INBOX_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { loadStrategy } = payload;
                const actions = getRoleInboxPageStrategyImplementingActions(loadStrategy, state$.value);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openTimesheetsPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[TIMESHEETS_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { location } = payload;
                const { value: state } = state$;
                const previousState = getPageState(state, TIMESHEETS_PAGE_ALIAS);
                const params = parsePageParams(location, TIMESHEETS_PAGE_ALIAS);
                const currentParams = processUrlParams(params);
                const currentPageState = {
                    params: currentParams
                };
                let openChain = [];

                if (commonPageStateChanged(currentPageState, previousState)) {
                    const loadStrategy = getTimesheetsLoadStrategy();
                    openChain.push(of(PAGE_ACTIONS.LOAD[TIMESHEETS_PAGE_ALIAS](loadStrategy)));
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadTimesheetsPageEpic$ = (action$) => {
    return action$
        .ofType(PA_TYPES.LOAD[TIMESHEETS_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { loadStrategy } = payload;
                const actions = getTimesheetsPageStrategyImplementingActions(loadStrategy);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openNotificationsPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[NOTIFICATIONS_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { location } = payload;
                const previousState = getPageState(state$.value, NOTIFICATIONS_PAGE_ALIAS);
                const params = parsePageParams(location, NOTIFICATIONS_PAGE_ALIAS);
                const { tab } = params;
                const hasManagerRequest = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state$.value);
                const notificationKey = hasManagerRequest ? NEW_NOTIFICATION_TABS_KEY : NOTIFICATION_TABS_KEY;
                const translationConfig = { sectionName: NOTIFICATIONS_PAGE_ALIAS, idsArray: [notificationKey] };
                const { notificationHistoryLabel } = getTranslationsSelector(state$.value, translationConfig)[notificationKey];
                const defaultTab = tab || notificationHistoryLabel;
                const defaultParams = { ...params, tab: defaultTab };
                const currentParams = processUrlParams(defaultParams);
                const currentPageState = {
                    params: currentParams
                };
                let openChain = [];
                if (commonPageStateChanged(currentPageState, previousState)) {
                    const loadStrategy = getNotificationsLoadStrategy();

                    openChain.push(
                        of(setPageState(NOTIFICATIONS_PAGE_ALIAS, currentPageState)),
                        of(PAGE_ACTIONS.LOAD[NOTIFICATIONS_PAGE_ALIAS](loadStrategy))
                    );
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadNotificationsPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.LOAD[NOTIFICATIONS_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const notificationsPageState = getPageState(state$.value, NOTIFICATIONS_PAGE_ALIAS);
                const { loadStrategy } = payload;
                const actions = getNotificationsPageStrategyImplementingActions(loadStrategy, notificationsPageState.params);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openMarketplacePageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[MARKETPLACE_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { value: state } = state$;
                const { location } = payload;
                const previousState = getPageState(state, MARKETPLACE_PAGE_ALIAS);
                const params = parsePageParams(location, MARKETPLACE_PAGE_ALIAS);
                const currentParams = processUrlParams(params);
                const currentPageState = {
                    params: currentParams
                };
                let openChain = [];

                if (commonPageStateChanged(currentPageState, previousState)) {
                    const pagedData = getPagedData(state, MARKETPLACE_PAGE_ALIAS)[TABLE_NAMES.ROLEREQUEST];
                    const loadStrategy = getMarketplaceLoadStategy(currentParams, { pagedData });

                    openChain.push(
                        of(setPageState(MARKETPLACE_PAGE_ALIAS, currentPageState)),
                        of(PAGE_ACTIONS.LOAD[MARKETPLACE_PAGE_ALIAS](loadStrategy))
                    );
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadMarketplacePageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.LOAD[MARKETPLACE_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { value: state } = state$;
                const marketplacePageState = getPageState(state, MARKETPLACE_PAGE_ALIAS);
                const { loadStrategy } = payload;
                const entityStructure = getEntityStructure(state);
                const actions = getMarketplacePageStategyImplementatingActions(loadStrategy, marketplacePageState.params, entityStructure);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export const openTableViewPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.OPEN[TABLE_VIEW_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { value: state } = state$;
                const { location } = payload;
                const previousState = getPageState(state, TABLE_VIEW_PAGE_ALIAS);
                const params = parsePageParams(location, TABLE_VIEW_PAGE_ALIAS);
                const currentParams = processUrlParams(params);
                const currentPageState = {
                    params: currentParams
                };
                let openChain = [];

                if (commonPageStateChanged(currentPageState, previousState)) {
                    //TO DO: incorporate here once reducer is in place
                    const pagedData = getPagedData(state, TABLE_VIEW_PAGE_ALIAS)[TABLE_NAMES.BOOKING];
                    const loadStrategy = getTableViewLoadStategy(currentParams, { pagedData });

                    openChain.push(
                        of(setPageState(TABLE_VIEW_PAGE_ALIAS, currentPageState)),
                        of(PAGE_ACTIONS.LOAD[TABLE_VIEW_PAGE_ALIAS](loadStrategy))
                    );
                }

                return 0 < openChain.length ? concat(...openChain) : empty();
            })
        );
};

export const loadTableViewPageEpic$ = (action$, state$) => {
    return action$
        .ofType(PA_TYPES.LOAD[TABLE_VIEW_PAGE_ALIAS])
        .pipe(
            switchMap(({ payload }) => {
                const { value: state } = state$;
                const tableViewPageState = getPageState(state, TABLE_VIEW_PAGE_ALIAS);
                const { loadStrategy } = payload;
                const actions = getTableViewPageStategyImplementatingActions(loadStrategy, tableViewPageState.params, state);

                return 0 < actions.length ? of(...actions) : empty();
            })
        );
};

export default combineEpics(
    openPlannerPageEpic$,
    loadPlannerPageEpic$,
    openJobsPageEpic$,
    loadJobsPageEpic$,
    openResourcesPageEpic$,
    loadResourcesPageEpic$,
    openProfilePageEpic$,
    loadProfilePageEpic$,
    openRoleInboxPageEpic$,
    loadRoleInboxPageEpic$,
    openTimesheetsPageEpic$,
    loadTimesheetsPageEpic$,
    openNotificationsPageEpic$,
    loadNotificationsPageEpic$,
    openMarketplacePageEpic$,
    loadMarketplacePageEpic$,
    openTableViewPageEpic$,
    loadTableViewPageEpic$
);