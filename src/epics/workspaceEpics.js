import { concat, EMPTY, empty, forkJoin, from, of } from 'rxjs';
import { concatMap, map, switchMap } from 'rxjs/operators';
import { combineEpics } from 'redux-observable';
import { PLANNERPAGE_FILTER_ALIAS, PLANNER_MASTER_REC_ALIAS, PLANNER_PAGE_ALIAS, PLANNER_PAGE_TABLE_TO_GROUP_ALIAS, PLANNER_SUB_REC_ALIAS } from '../../src/constants/plannerConsts';
import { deleteWorkspaceSuccess, getMostRecentlyUsedWorkspaces, dropMostRecentlyUsedWorkspace, digestLoadWorkspaces } from '../actions/workspaceActions';
import * as wsActions from '../actions/workspaceActions';
import * as tableDataActions from '../actions/tableDataActions';
import { commandBarPopulatePlansSection, commandBarRepopulatePlansSection } from '../actions/commandBarActions';
import * as actionTypes from '../actions/actionTypes';
import { loadPlannerData, loadColourSchemes } from '../actions/plannerDataActions';
import * as wss from '../selectors/workspaceSelectors';
import { WORKSPACE_ACCESS_TYPES } from '../constants';
import { getApiCallEpicConfig, createAPICallEpic } from './epicGenerators/apiCallEpicGenerator';
import { reloadPlannerData } from '../actions/plannerDataActions';
import { manageWorkspacesAddEditMode } from '../actions/managePlansSectionActions';
import { getAddGrouppedTableDataModelActions, getSaveWorkspaceSettings } from '../utils/workspaceUtils';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { getEntityStructure } from '../selectors/entityStructureSelectors';
import { format } from 'date-fns';
import uuidv4 from 'uuid/v4';
import { addPlannerFieldOptionsModel } from '../actions/workspaceSettingsActions';
import { createPlannerPageFieldOptionsModel } from '../state/plannerPageFieldOptions';
import { verifyFilterSettings, filterGroupsConfigAddCustomFields, getOldFilterHasHiddenFilters } from '../utils/filtersUtils';
import { generateCopiedPlanName, generateDefaultNewPlanName } from '../utils/planNameGenerationUtils';
import { plannerPageFilterGroupsConfig, tableViewPageFilterGroupsConfig } from '../state/filterPane/pagesFilterGroupsConfig';
import { getCustomFieldInfosByTableName, getFieldInfoSelector } from '../selectors/tableStructureSelectors';
import { combineAPIEpics } from './middlewareExtensions';
import { updatePageParams } from '../actions/pageStateActions';
import { pushUrl, replaceUrl } from '../actions/navigateActions';
import { omit } from '../utils/commonUtils';
import { getPageState } from '../selectors/pagesSelectors';
import { replaceBrowserHistoryUrl } from '../history';
import { ERROR_PAGES } from '../pages/pages';
import { FEATURE_FLAGS, LICENSE_KEYS_ADMIN_SETTINGS, URL_PARAMS } from '../constants/globalConsts';
import { getSkillsFilterProps } from '../connectedComponents/connectedFilters/selectors';
import { TABLE_VIEW_PAGE_ALIAS, TABLE_VIEW_PAGE_FILTER_ALIAS } from '../constants/tableViewPageConsts';
import { getPageLicenseSize } from '../selectors/commonSelectors';
import { getCurrentDate } from '../utils/dateUtils';
import { AdvancedFilterValidator } from '../utils/advancedFiltersValidator';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';

const {
    WORKSPACE_SURROGATE_ID,
    WORKSPACE_NAME
} = URL_PARAMS;

const workspacesApiName = 'workspaces';

const buildPageParams = (workspaceGuid, workspaceSurrogateId, workspaceName) => ({
    workspaceGuid,
    [WORKSPACE_SURROGATE_ID]: workspaceSurrogateId,
    [WORKSPACE_NAME]: workspaceName
});

export const loadWorkspaceRequest$ = (api, payload) => {
    const { workspaceGuid } = payload;

    return api.getWorkspace$(workspaceGuid);
};

export const loadWorkspacesRequest$ = (api, payload) => {
    return forkJoin(
        api.getWorkspaces$(payload.groupByType),
        api.getMostRecentlyUsed$(payload.getWorkspaceDetail)
    );
};

export const digestLoadWorkspacesRequest$ = (api, payload) => {
    const { addDefaultMostRecentlyUsed, loadDefaultWorkspace, defaultWorkspaceGuid } = payload;

    let addDefaultMostRecentlyUsed$ = of(null);
    let getMostRecentlyUsed$ = of(null);
    if (addDefaultMostRecentlyUsed) {
        addDefaultMostRecentlyUsed$ = api.addMostRecentlyUsed$(defaultWorkspaceGuid);
        getMostRecentlyUsed$ = api.getMostRecentlyUsed$(false);
    }

    let loadDefaultWorkspace$ = of(null);
    if (loadDefaultWorkspace) {
        loadDefaultWorkspace$ = api.getWorkspace$(defaultWorkspaceGuid);
    }

    return addDefaultMostRecentlyUsed$.pipe(
        concatMap(addDefaultMostRecentlyUsed => getMostRecentlyUsed$.pipe(
            map(updatedMostRecentlyUsed => ({
                addDefaultMostRecentlyUsed, updatedMostRecentlyUsed
            }))
        )),
        switchMap(addDefaultMostRecentlyUsedResult => loadDefaultWorkspace$.pipe(
            map(loadDefaultWorkspaceResult => ({
                addDefaultMostRecentlyUsedResult, loadDefaultWorkspaceResult
            }))
        ))
    );
};

export const getMostRecentlyUsedWorkspaces$ = (api, payload) => {
    const getWorkspaceDetail = !!payload.getWorkspaceDetail;

    return api.getMostRecentlyUsed$(getWorkspaceDetail);
};

export const addMostRecentlyUsedWorkspace$ = (api, payload) => {
    return api.addMostRecentlyUsed$(payload.workspaceGuid);
};

export const deleteWorkspaceDataRequest$ = (api, payload) => {
    const { workspaceGuid } = payload;

    return api.deleteWorkspace$(workspaceGuid);
};

export const patchWorkspaceDataRequest$ = (api, payload) => {
    const { workspaceGuid, workspaceData } = payload;

    return api.patchWorkspace$(workspaceGuid, workspaceData);
};

export const insertWorkspaceDataRequest$ = (api, payload) => {
    const { data } = payload;

    return api.insertWorkspace$(data);
};

export const ensureWorkspaceNameSet$ = (api, payload) => {
    const { workspace_description, privatePlans, newPlanLabel } = payload;

    const workspaceName = !workspace_description || workspace_description === ''
        ? generateDefaultNewPlanName(privatePlans, newPlanLabel)
        : workspace_description;

    return of(workspaceName);
};

export const getCopyWorkspaceUniqueName$ = (api, payload) => {
    const { originalWorkspaceName } = payload;

    return of(generateCopiedPlanName(originalWorkspaceName));
};

const getInitFilterSettings = (filtersGuid, masterRecTableName, subRecTableName) => {
    return {
        guid: filtersGuid,
        title: 'Planner page filters',
        selectedView: masterRecTableName,
        views: {
            [masterRecTableName]: {
                guid: masterRecTableName,
                selection: {},
                hidden: false
            },
            [subRecTableName]: {
                guid: subRecTableName,
                selection: {},
                hidden: false
            }
        }
    };
};

const getCommonWorkspaceActions = (wsSettings, entityStructure, pageSize) => {
    const {
        masterRecTableName,
        subRecTableName,
        pagedMasterRecPlannerDataGuid,
        subRecPlannerDataGuid,
        detailsPaneGuid,
        plannerDataGuid
    } = wsSettings;

    return [
        wsActions.addPagedDataModel(PLANNER_MASTER_REC_ALIAS, pagedMasterRecPlannerDataGuid, masterRecTableName, pageSize),
        ...getAddGrouppedTableDataModelActions(wsSettings),

        tableDataActions.addTableDataModel(PLANNER_SUB_REC_ALIAS, subRecPlannerDataGuid, subRecTableName),
        wsActions.addDetailsPaneModel(detailsPaneGuid, entityStructure),
        wsActions.addPlannerDataModel(plannerDataGuid)
    ];
};

export const getFilterSettingActions = (wsSettings, customFields, workspaceGuids, currentWorkspaceIndex, getFieldInfo, getSkillData) => {
    let pageFilterGroupsConfig = plannerPageFilterGroupsConfig;
    const filterGroupsConfig = filterGroupsConfigAddCustomFields(customFields, pageFilterGroupsConfig);
    let filterSettings = {};
    const result = [];

    const {
        filterState,
        filterSettings: workspaceFitlerSettings,
        filtersGuid,
        masterRecTableName,
        subRecTableName
    } = wsSettings;

    const isOldFilterStructure = !!workspaceFitlerSettings;
    const isNewFilterStructure = !!filterState;

    if (isNewFilterStructure) {
        const advancedFilterValidator = new AdvancedFilterValidator(filterGroupsConfig, getFieldInfo, getSkillData);
        let validFilterState = advancedFilterValidator.getValidAdvancedFilterState(filterState);
        if (filtersGuid && (!validFilterState || !validFilterState.views || Object.keys(validFilterState.views).length <= 0)) {
            validFilterState = {
                guid: filtersGuid,
                title: '',
                selectedView: 'resource',
                views: {}
            };
        }
        const hasHiddenFilters = advancedFilterValidator.getHasHiddenFilters(validFilterState, filterState);
        result.push(wsActions.setFilterState(validFilterState, PLANNERPAGE_FILTER_ALIAS, hasHiddenFilters));
        result.push(wsActions.deleteWokspaceFilterSettings(workspaceGuids[currentWorkspaceIndex]));
    } else if (isOldFilterStructure) {
        filterSettings = verifyFilterSettings(workspaceFitlerSettings, filterGroupsConfig, getFieldInfo, getSkillData);
        const hasHiddenFilters = getOldFilterHasHiddenFilters(workspaceFitlerSettings, filterSettings);
        result.push(wsActions.deleteWokspaceFilterSettings(workspaceGuids[currentWorkspaceIndex]));
        result.push(wsActions.addMultipleViewsFiltersModel(filterSettings, filterGroupsConfig, PLANNERPAGE_FILTER_ALIAS, hasHiddenFilters));
    } else {
        filterSettings = getInitFilterSettings(filtersGuid , masterRecTableName, subRecTableName);
        result.push(wsActions.addMultipleViewsFiltersModel(filterSettings, filterGroupsConfig, PLANNERPAGE_FILTER_ALIAS));
    }

    return result;
};

export const getWorkspaceGuidModelDataActions = (workspaceGuids, workspaces, entityStructure, customFields = {}, getFieldInfo, getSkillData, pageSize) => {
    let result = [];

    let wsSettings;
    for (let i = 0; i < workspaceGuids.length; i++) {
        wsSettings = wss.getWorkspaceSettings(workspaces, workspaceGuids[i]);
        result.push(...getFilterSettingActions(wsSettings, customFields, workspaceGuids, i, getFieldInfo, getSkillData));
        result.push(...getCommonWorkspaceActions(wsSettings, entityStructure, pageSize));
    }

    result.push(
        wsActions.filtersModelsLoadedSuccess(PLANNERPAGE_FILTER_ALIAS),
        wsActions.tableDataModelsLoadedSuccess(),
        wsActions.pagedDataModelsLoadedSuccess(PLANNER_MASTER_REC_ALIAS),
        wsActions.detailsPaneModelsLoadedSuccess(),
        wsActions.plannerDataModelsLoadedSuccess(),
        wsActions.populateSkillsValues(PLANNER_PAGE_ALIAS)
    );

    Object.values(PLANNER_PAGE_TABLE_TO_GROUP_ALIAS).forEach(groupDataAlias =>
        result.push(wsActions.grouppedTableDataModelsLoadedSuccess(groupDataAlias)));

    return result;
};

export const handleTableViewPageDataFiltersEpic = (action$, state$) => {
    return action$.ofType(actionTypes.FILTERS_ACTIONS.HANDLE_TABLE_VIEW_PAGE_DATA_FILTERS).pipe(
        concatMap(() => {
            const state = state$.value;
            const workspaces = wss.getWorkspacesSettings(state[TABLE_VIEW_PAGE_ALIAS]);
            const workspacesSettingsStructure = wss.getWorkspacesSettingsStructure(workspaces);
            const workspaceGuids = Object.keys(workspacesSettingsStructure.map);
            const getFieldInfo = getFieldInfoSelector(state);
            let wsSettings;
            const result = [];

            for (let i = 0; i < workspaceGuids.length; i++) {
                wsSettings = wss.getWorkspaceSettings(workspaces, workspaceGuids[i]);
                let pageFilterGroupsConfig = tableViewPageFilterGroupsConfig;
                const customFields = getCustomFieldInfosByTableName(state);
                const filterGroupsConfig = filterGroupsConfigAddCustomFields(customFields, pageFilterGroupsConfig);
                let filterSettings = {};

                if (wsSettings.filterSettings) {
                    const skillsFilterProps = getSkillsFilterProps(state);
                    filterSettings = verifyFilterSettings(wsSettings.filterSettings, filterGroupsConfig, getFieldInfo, skillsFilterProps.getSkillData);
                } else {
                    filterSettings = getInitFilterSettings(wsSettings['filtersGuid'] , wsSettings['masterRecTableName'], wsSettings['subRecTableName']);
                }

                result.push(
                    wsActions.addMultipleViewsFiltersModel(filterSettings, filterGroupsConfig, TABLE_VIEW_PAGE_FILTER_ALIAS)
                );
            }

            result.push(
                wsActions.filtersModelsLoadedSuccess(TABLE_VIEW_PAGE_FILTER_ALIAS),
                wsActions.populateSkillsValues(TABLE_VIEW_PAGE_ALIAS)
            );

            return from(result);
        })
    );
};

const shouldSelectWorkspace = (workspaces, selectedWorkspaceGuid, mostRecentlyUsedWorkspaceGuid) => {
    return selectedWorkspaceGuid && wss.getWorkspaceStructure(workspaces, selectedWorkspaceGuid) && selectedWorkspaceGuid !== mostRecentlyUsedWorkspaceGuid;
};

export const handleLoadWorkspacesSuccessEpic = (action$, state$) => {
    return action$.ofType(actionTypes.LOAD_WORKSPACES_SUCCESSFUL).pipe(
        map((action) => {
            const { payload } = action;
            const workspaces = state$.value.plannerPage.workspaces;
            const mostRecentlyUsedWorkspacesGuids = wss.getMostRecentlyUsedWorkspaces(workspaces);

            const defaultWorkspaceStructure = wss.getDefaultWorkspaceStructure(workspaces);
            const defaultWorkspaceSettings = wss.getWorkspaceSettings(workspaces, defaultWorkspaceStructure.workspace_guid);

            const addDefaultMostRecentlyUsed = mostRecentlyUsedWorkspacesGuids.length === 0;
            const loadDefaultWorkspace = !defaultWorkspaceSettings;

            if (!addDefaultMostRecentlyUsed && !loadDefaultWorkspace) {
                return wsActions.validateSelectedWorkspaceOnInitLoad(payload);
            }

            return digestLoadWorkspaces(payload, addDefaultMostRecentlyUsed, loadDefaultWorkspace, defaultWorkspaceStructure.workspace_guid);
        })
    );
};

export const validateSelectedWorkspaceOnInitLoad = (action$, state$) => {
    return action$.ofType(actionTypes.VALIDATE_SELECTED_WORKSPACE_ON_INIT_LOAD, actionTypes.DIGEST_LOAD_WORKSPACES_SUCCESSFUL).pipe(
        switchMap(({ payload }) => {
            const { value: state } = state$;
            const { selectedWorkspaceSurrogateId } = payload;
            const workspaceToSelect = wss.getInitWorkspaceToSelect(state, selectedWorkspaceSurrogateId);

            if (workspaceToSelect) {
                const {
                    workspace_guid: workspaceGuid,
                    workspace_surrogate_id: workspaceSurrogateId,
                    workspace_description: workspaceDescription
                } = workspaceToSelect;

                return of(wsActions.loadWorkspacesSuccessSetup({ workspaceGuid, workspaceSurrogateId, workspaceDescription }));
            }

            replaceBrowserHistoryUrl(ERROR_PAGES.NOT_FOUND.navigationLink);

            return empty();
        })
    );
};

const createDigestLoadWorkspacesSuccess = () => {
    return (action$, state$) => {
        return action$.ofType(actionTypes.LOAD_WORKSPACES_SUCCESSFUL_SETUP).pipe(
            concatMap(({ payload }) => {
                const { value: state } = state$;

                const workspaces = wss.getWorkspacesSettings(state.plannerPage);
                const currWSSettings = wss.getSelectedWorkspaceSettings(workspaces);
                const mostRecentlyUsedWorkspacesGuids = wss.getMostRecentlyUsedWorkspaces(workspaces);
                const workspacesSettingsStructure = wss.getWorkspacesSettingsStructure(workspaces);
                const entityStructure = getEntityStructure(state);
                const customFields = getCustomFieldInfosByTableName(state);
                const getFieldInfo = getFieldInfoSelector(state);
                const skillsFilterProps = getSkillsFilterProps(state);
                const pageSize = getPageLicenseSize(state)(LICENSE_KEYS_ADMIN_SETTINGS.licensePlannerPageSize);
                const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

                const guidModelActions = getWorkspaceGuidModelDataActions(Object.keys(workspacesSettingsStructure.map), workspaces, entityStructure, customFields, getFieldInfo, skillsFilterProps.getSkillData, pageSize);

                let actions = [
                    commandBarPopulatePlansSection(workspaces, listPageAndBulkUpdateFeatureFlag),
                    ...guidModelActions,
                    loadPlannerData(currWSSettings),
                    loadColourSchemes()
                ];

                const { workspaceSurrogateId, workspaceGuid, workspaceDescription } = payload;

                if (shouldSelectWorkspace(workspaces, workspaceGuid, mostRecentlyUsedWorkspacesGuids[0])) {
                    actions.push(wsActions.digestSelectWorkspace(workspaceGuid, listPageAndBulkUpdateFeatureFlag));
                }

                const pageParams = buildPageParams(workspaceGuid, workspaceSurrogateId, workspaceDescription);

                actions.push(
                    updatePageParams(PLANNER_PAGE_ALIAS, pageParams),
                    replaceUrl(pageParams)
                );

                return from(actions);
            })
        );
    };
};

export const handleDigestSelectWorkspaceEpic = (action$, state$) => {
    return action$.ofType(actionTypes.DIGEST_SELECT_WORKSPACE).pipe(
        switchMap((action) => {
            const state = state$.value || {};
            const workspaces = state$.value.plannerPage.workspaces;
            const { workspaceGuid } = action.payload;
            const selectedWorkspaceStructure = wss.getWorkspaceStructure(workspaces, workspaceGuid);
            const selectedWorkspaceSettings = wss.getWorkspaceSettings(workspaces, workspaceGuid);
            const { workspace_guid: selectedWorkspaceGuid } = selectedWorkspaceStructure;
            const { filterState } = getSaveWorkspaceSettings(state[PLANNER_PAGE_ALIAS], workspaces.selected, true);

            let chain = [];

            if (!selectedWorkspaceSettings || wss.workspaceSettingsChanged(workspaces, selectedWorkspaceGuid)) {
                const selectWorkspace = true;

                chain = [
                    ...chain,
                    of(wsActions.loadWorkspace(selectedWorkspaceGuid, null, selectWorkspace))
                ];
            } else {
                chain = [
                    ...chain,
                    of(wsActions.selectWorkspace(workspaceGuid))
                ];
            }

            chain.push(of(wsActions.resetFilterToSavedPlan(PLANNERPAGE_FILTER_ALIAS, filterState.guid, filterState)));

            return concat(...chain);
        })
    );
};

export const handleLoadWorkspaceSuccessEpic = (action$, state$) => {
    return action$.ofType(actionTypes.LOAD_WORKSPACE_SUCCESSFUL, actionTypes.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL).pipe(
        concatMap((action) => {
            const state = state$.value;
            const selectWorkspace = action.payload.selectWorkspace;
            const workspaces = wss.getWorkspacesSettings(state.plannerPage);
            const entityStructure = getEntityStructure(state);
            const customFields = getCustomFieldInfosByTableName(state);
            const getFieldInfo = getFieldInfoSelector(state);
            const skillsFilterProps = getSkillsFilterProps(state);
            const newWorkspaceSettings = wss.getSaveAsNewPlanWorkspaceSelector(state.plannerPage.workspaces);
            const pageSize = getPageLicenseSize(state)(LICENSE_KEYS_ADMIN_SETTINGS.licensePlannerPageSize);
            const { workspaceColourthemeGuid = null, workspaceCustomColourTheme = [] } = newWorkspaceSettings;
            const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

            //build guid model data for the particular loaded workspace
            const guidModelActions = getWorkspaceGuidModelDataActions([action.payload.workspaceGuid], workspaces, entityStructure, customFields, getFieldInfo, skillsFilterProps.getSkillData, pageSize);

            let dispatchActions = [...guidModelActions];

            if (selectWorkspace) {
                dispatchActions.push(wsActions.selectWorkspace(action.payload.workspaceGuid, PLANNER_PAGE_ALIAS));
            } else {
                dispatchActions.push(commandBarRepopulatePlansSection(workspaces, listPageAndBulkUpdateFeatureFlag));
            }

            if (action.type === actionTypes.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL) {
                const { payload } = action;
                const { newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, originalWorkspaceName, doCreate } = payload;
                dispatchActions.push(wsActions.digestCopyWorkspace(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, workspaceColourthemeGuid, workspaceCustomColourTheme));
            }

            return from(dispatchActions);
        })
    );
};

const handleSelectWorkspaceEpic = (action$, state$) => {
    return action$.ofType(actionTypes.SELECT_WORKSPACE).pipe(
        concatMap((action) => {
            const { payload } = action;
            const { workspaceGuid } = payload;
            const workspaces = state$.value.plannerPage.workspaces;
            const selectedWorkspaceStructure = wss.getWorkspaceStructure(workspaces, workspaceGuid);
            const selectedWorkspaceSettings = wss.getWorkspaceSettings(workspaces, workspaceGuid);
            const selectedWSPlannerData = state$.value.plannerPage.plannerData[selectedWorkspaceSettings.plannerDataGuid];

            const {
                workspace_guid: selectedWorkspaceGuid,
                workspace_surrogate_id: selectedWorkspaceSurrogateId,
                workspace_description: selectedWorkspaceName
            } = selectedWorkspaceStructure;

            let actions = [
                wsActions.addMostRecentlyUsedWorkspace(selectedWorkspaceGuid, selectedWorkspaceStructure)
            ];

            if (!selectedWSPlannerData.loaded || selectedWSPlannerData.isDirty) {
                actions.push(reloadPlannerData());
            }

            const getFieldInfo = getFieldInfoSelector(state$.value);
            const loadedFields = wss.getWorkspaceLoadedFields(selectedWorkspaceSettings, getFieldInfo);

            actions.push(
                addPlannerFieldOptionsModel(
                    workspaceGuid,
                    createPlannerPageFieldOptionsModel(loadedFields)
                ),
                pushUrl(
                    buildPageParams(selectedWorkspaceGuid, selectedWorkspaceSurrogateId, selectedWorkspaceName),
                    PLANNER_PAGE_ALIAS
                )
            );

            return from(actions);
        })
    );
};

const handleAddMostRecentlyUsedWorkspaceSuccessEpic = (action$, state$) => {
    return action$.ofType(actionTypes.ADD_MOST_RECENTLY_USED_WORKSPACE_SUCCESSFUL).pipe(
        concatMap((action) => {
            const workspaces = state$.value.plannerPage.workspaces;
            const { cachedMostRecentWorkspaces } = workspaces;

            const getWorkspaceDetail = false;
            let actions = [
                getMostRecentlyUsedWorkspaces(getWorkspaceDetail)
            ];

            const droppedWorkspaceGuid = action.payload.data[0];

            if (droppedWorkspaceGuid && cachedMostRecentWorkspaces && cachedMostRecentWorkspaces.length > 0) {
                let workspaceToDrop = null;
                let defaultWorkspace = null;

                for (const cachedWorkspace of cachedMostRecentWorkspaces) {
                    if (cachedWorkspace.workspace_guid === droppedWorkspaceGuid) {
                        workspaceToDrop = cachedWorkspace;
                    }
                    if (cachedWorkspace.workspace_accesstype.toLowerCase() === WORKSPACE_ACCESS_TYPES.DEFAULT.toLowerCase()) {
                        defaultWorkspace = cachedWorkspace;
                    }
                }

                if (workspaceToDrop) {
                    const workspaceToDropGuid = workspaceToDrop.workspace_guid || '';
                    const defaultWorkspaceGuid = defaultWorkspace && defaultWorkspace.workspace_guid ? defaultWorkspace.workspace_guid : '';

                    if (workspaceToDropGuid !== defaultWorkspaceGuid && workspaceToDrop.workspace_accesstype !== WORKSPACE_ACCESS_TYPES.PUBLIC) {
                        actions.push(dropMostRecentlyUsedWorkspace(workspaceToDropGuid, workspaceToDrop.workspace_settings));
                    }
                }
            }

            return from(actions);
        })
    );
};

const generateWSSettingsGuidDataKeyValuePairs = (uuid) => {
    //TO BE CHANGED AS PART OF NEW WSS Structure
    return {
        filtersGuid: `filters_${uuid}`,
        plannerDataGuid: `planner_data_${uuid}`,
        pagedMasterRecPlannerDataGuid: `pagedMasterRecPlannerData_${uuid}`,
        subRecPlannerDataGuid: `subRecPlannerData_${uuid}`,
        detailsPaneGuid: `detailsPane_${uuid}`,
        barGroupsGuids: {
            bookingGroupsGuid: `bookingGroups_${uuid}`,
            roleGroupsGuid: `rolesGroups_${uuid}`
        }
    };
};

const handleDigestCreateWorkspaceSuccessEpic = (action$, state$) => {
    return action$.ofType(
        actionTypes.DIGEST_CREATE_WORKSPACE_SUCCESSFUL,
        actionTypes.DIGEST_COPY_WORKSPACE_SUCCESSFUL,
        actionTypes.DIGEST_SET_CREATE_WORKSPACE_CHANGE,
        actionTypes.DIGEST_SAVE_AS_NEW_PLAN
    ).pipe(
        map((action) => {
            const { payload } = action;
            const { data, wsDescription, wsAccessType, wsEditRights, newWorkspaceTemplateGuid, doCreate, selectCreatedWorkspace, workspaceColourthemeGuid = null, workspaceCustomColourTheme = [] } = payload;
            const newWorkspaceDescription = data ? data : wsDescription;

            const workspaces = state$.value.plannerPage.workspaces;

            const {
                workspace_guid: templateWorkspaceGuid,
                ...templateWorkspaceStructureWithoutGuid
            } = wss.getWorkspaceStructure(workspaces, newWorkspaceTemplateGuid);

            const currentUserGuid = getApplicationUserId(state$.value);
            const newWorkspaceStructure = {
                ...omit(templateWorkspaceStructureWithoutGuid, ['workspace_surrogate_id']),
                workspace_description: newWorkspaceDescription,
                workspace_accesstype: wsAccessType,
                workspace_editrights: wsEditRights,
                workspace_colourtheme_guid: workspaceColourthemeGuid,
                workspace_colour_field_name: null,
                workspace_change_date: format(getCurrentDate(), 'YYYY-MM-DDTHH:mm:ss.SSS'),
                workspace_author_guid: currentUserGuid,
                workspace_change_author_guid: currentUserGuid,
                workspace_custom_colour_field: workspaceCustomColourTheme
            };

            const newWSUUID = uuidv4();

            const copyTemplateFilterSelections = true;

            let templateWorkspaceSettings = getSaveWorkspaceSettings(state$.value.plannerPage, templateWorkspaceGuid, copyTemplateFilterSelections);

            templateWorkspaceSettings.filterState.guid = `filters_${newWSUUID}`;

            const workspaceSettingsData = {
                workspace_settings: JSON.stringify({
                    ...templateWorkspaceSettings,
                    ...generateWSSettingsGuidDataKeyValuePairs(newWSUUID)
                })
            };

            return wsActions.setCreateWorkspaceChange(newWSUUID, newWorkspaceStructure, workspaceSettingsData, doCreate, selectCreatedWorkspace);
        })
    );
};

const handleSetCreateWorkspaceChangeEpic = (action$) => {
    return action$.ofType(actionTypes.SET_CREATE_WORKSPACE_CHANGE).pipe(
        switchMap(({ payload }) => {
            const {
                newWSUUID: workspaceGuid,
                selectCreatedWorkspace,
                doCreate
            } = payload;

            return doCreate
                ? of(wsActions.createWorkspace(workspaceGuid, selectCreatedWorkspace))
                : of(manageWorkspacesAddEditMode(workspaceGuid, 'create'));
        })
    );
};

const handleCreateWorkspaceEpic = (action$, state$) => {
    return action$.ofType(actionTypes.CREATE_WORKSPACE).pipe(
        concatMap((action) => {
            const { payload } = action;
            const { uuid, selectCreatedWorkspace } = payload;

            const workspaces = state$.value.plannerPage.workspaces;

            const newWorkspaceStructure = wss.getWorkspaceStructureFromChangesCollection(workspaces, 'inserts', uuid);
            const newWorkspaceStructureUpdates = wss.getWorkspaceStructureFromChangesCollection(workspaces, 'updates', uuid);

            const newWorkspaceSettings = wss.getWorkspaceSettingsFromChangesCollection(workspaces, 'inserts', uuid);

            const newWorkspace = { ...newWorkspaceStructure, ...newWorkspaceStructureUpdates, ...newWorkspaceSettings };

            return from([
                wsActions.doCreateWorkspace(newWorkspace, uuid, selectCreatedWorkspace),
                wsActions.removeCreateWorkspaceChange(uuid)
            ]);
        })
    );
};

const handleCreateWorkspaceSuccessEpic = (action$) => {
    return action$.ofType(actionTypes.CREATE_WORKSPACE_SUCCESSFUL).pipe(
        map((action) => {
            const { payload } = action;
            const { data : createdWorkspaceGuid, selectCreatedWorkspace, createdWorkspaceChangeUUID } = payload;

            return wsActions.loadWorkspace(createdWorkspaceGuid, createdWorkspaceChangeUUID, selectCreatedWorkspace);
        })
    );
};

const handleWorkspaceChangeEpic = (action$, state$) => {
    return action$.ofType(
        actionTypes.RENAME_WORKSPACE_SUCCESS,
        actionTypes.MOVE_WORKSPACE_SUCCESS,
        actionTypes.GET_MOST_RECENTLY_USED_WORKSPACES_SUCCESSFUL,
        actionTypes.DELETE_WORKSPACE_SUCCESS
    ).pipe(
        map(() => {
            const workspaces = state$.value.plannerPage.workspaces;
            const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state$.value);

            return commandBarRepopulatePlansSection(workspaces, listPageAndBulkUpdateFeatureFlag);
        })
    );
};

export const handleWorkspaceRenameSuccessEpic = (action$, state$) => {
    return action$.ofType(actionTypes.RENAME_WORKSPACE_SUCCESS).pipe(
        switchMap(({ payload }) => {
            const {
                workspaceGuid,
                workspaceData: {
                    workspace_description: workspaceName
                }
            } = payload;

            const {
                workspaceGuid: currentWorkspaceGuid,
                workspaceSurrogateId: currentWorkspaceSurrogateId
            } = getPageState(state$.value, PLANNER_PAGE_ALIAS).params || {};

            const pageParams = buildPageParams(currentWorkspaceGuid, currentWorkspaceSurrogateId, workspaceName);

            return workspaceGuid === currentWorkspaceGuid
                ? of(updatePageParams(PLANNER_PAGE_ALIAS, pageParams), replaceUrl(pageParams))
                : empty();
        })
    );
};

export const handleRepopulatedPlansSectionEpic = (action$, state$) => {
    return action$.ofType(actionTypes.COMMAND_BAR_PLANS_SECTION.REPOPULATE).pipe(
        switchMap(() => {
            const { value: state } = state$;

            const { workspaceGuid: currentWorkspaceGuid } = getPageState(state, PLANNER_PAGE_ALIAS).params || {};

            const {
                workspace_guid: selectedWorkspaceGuid,
                workspace_surrogate_id: selectedWorkspaceSurrogateId,
                workspace_description: selectedWorkspaceName
            } = wss.getSelectedWorkspace(state.plannerPage.workspaces);

            const pageParams = buildPageParams(selectedWorkspaceGuid, selectedWorkspaceSurrogateId, selectedWorkspaceName);

            return currentWorkspaceGuid !== selectedWorkspaceGuid
                ? of(updatePageParams(PLANNER_PAGE_ALIAS, pageParams), replaceUrl(pageParams))
                : empty();
        })
    );
};

const createDeleteWorkspaceEpic = (successActionHandler = deleteWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.DELETE_WORKSPACE, workspacesApiName, deleteWorkspaceDataRequest$, successActionHandler, null)
    );

export const createLoadWorkspacesEpic = (successActionHandler = wsActions.loadWorkspacesSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LOAD_WORKSPACES, workspacesApiName, loadWorkspacesRequest$, successActionHandler, null)
    );

export const createAddMostRecentlyUsedWorkspaceEpic = (successActionHandler = wsActions.addMostRecentlyUsedWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.ADD_MOST_RECENTLY_USED_WORKSPACE, workspacesApiName, addMostRecentlyUsedWorkspace$, successActionHandler, null)
    );

export const createLoadMostRecentlyUsedWorkspacesEpic = (successActionHandler = wsActions.getMostRecentlyUsedWorkspacesSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.GET_MOST_RECENTLY_USED_WORKSPACES, workspacesApiName, getMostRecentlyUsedWorkspaces$, successActionHandler, null)
    );

export const createLoadWorkspaceEpic = (successActionHandler = wsActions.loadWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LOAD_WORKSPACE, workspacesApiName, loadWorkspaceRequest$, successActionHandler, null)
    );

export const createDropMostRecentlyUsedWorkspaceEpic = (successActionHandler = wsActions.dropMostRecentlyUsedWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.DROP_MOST_RECENTLY_USED_WORKSPACE, workspacesApiName, patchWorkspaceDataRequest$, successActionHandler, null)
    );

export const digestCreateWorkspaceEpic = (successActionHandler = wsActions.digestCreateWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.DIGEST_CREATE_WORKSPACE, workspacesApiName, ensureWorkspaceNameSet$, successActionHandler, null)
    );

export const createWorkspaceEpic = (successActionHandler = wsActions.createWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.DO_CREATE_WORKSPACE, workspacesApiName, insertWorkspaceDataRequest$, successActionHandler, null)
    );

const createRenameWorkspaceEpic = (successActionHandler = wsActions.renameWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.RENAME_WORKSPACE, workspacesApiName, patchWorkspaceDataRequest$, successActionHandler, successActionHandler)
    );

const createLoadDefaultWorkspaceEpic = (successActionHandler = wsActions.loadWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LOAD_DEFAULT_WORKSPACE, workspacesApiName, loadWorkspaceRequest$, successActionHandler, successActionHandler)
    );

export const digestCopyWorkspaceEpic = (successActionHandler = wsActions.digestCopyWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.DIGEST_COPY_WORKSPACE, workspacesApiName, getCopyWorkspaceUniqueName$, successActionHandler, null)
    );

const createMoveWorkspaceEpic = (successActionHandler = wsActions.moveWorkspaceSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.MOVE_WORKSPACE, workspacesApiName, patchWorkspaceDataRequest$, successActionHandler, successActionHandler)
    );

const saveWorkspaceSettingsEpic = (successActionHandler = wsActions.saveWorkspaceSettingsSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.SAVE_WORKSPACE_SETTINGS, workspacesApiName, patchWorkspaceDataRequest$, successActionHandler, successActionHandler)
    );

const loadCopyWorkspaceTemplateEpic = (successActionHandler = wsActions.loadCopyWorkspaceTemplateSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.LOAD_COPY_WORKSPACE_TEMPLATE, workspacesApiName, loadWorkspaceRequest$, successActionHandler, successActionHandler)
    );

const digestLoadWorkspacesEpic = (successActionHandler = wsActions.digestLoadWorkspacesSuccess) =>
    createAPICallEpic(
        null,
        getApiCallEpicConfig(actionTypes.DIGEST_LOAD_WORKSPACES, workspacesApiName, digestLoadWorkspacesRequest$, successActionHandler, successActionHandler)
    );

export default function () {
    return combineEpics(
        combineAPIEpics(
            createDeleteWorkspaceEpic(deleteWorkspaceSuccess)(),
            createLoadWorkspacesEpic(wsActions.loadWorkspacesSuccess)(),
            createAddMostRecentlyUsedWorkspaceEpic(wsActions.addMostRecentlyUsedWorkspaceSuccess)(),
            createLoadMostRecentlyUsedWorkspacesEpic(wsActions.getMostRecentlyUsedWorkspacesSuccess)(),
            createLoadWorkspaceEpic(wsActions.loadWorkspaceSuccess)(),
            createDropMostRecentlyUsedWorkspaceEpic(wsActions.dropMostRecentlyUsedWorkspaceSuccess)(),
            digestCreateWorkspaceEpic(wsActions.digestCreateWorkspaceSuccess)(),
            createWorkspaceEpic(wsActions.createWorkspaceSuccess)(),
            createRenameWorkspaceEpic(wsActions.renameWorkspaceSuccess)(),
            createLoadDefaultWorkspaceEpic(wsActions.loadWorkspaceSuccess)(),
            digestCopyWorkspaceEpic(wsActions.digestCopyWorkspaceSuccess)(),
            createMoveWorkspaceEpic(wsActions.moveWorkspaceSuccess)(),
            saveWorkspaceSettingsEpic(wsActions.saveWorkspaceSettingsSuccess)(),
            loadCopyWorkspaceTemplateEpic(wsActions.loadCopyWorkspaceTemplateSuccess)(),
            digestLoadWorkspacesEpic(wsActions.digestLoadWorkspacesSuccess)()
        ),
        handleLoadWorkspacesSuccessEpic,
        createDigestLoadWorkspacesSuccess(),
        validateSelectedWorkspaceOnInitLoad,
        handleSelectWorkspaceEpic,
        handleDigestSelectWorkspaceEpic,
        handleLoadWorkspaceSuccessEpic,
        handleAddMostRecentlyUsedWorkspaceSuccessEpic,
        handleSetCreateWorkspaceChangeEpic,
        handleDigestCreateWorkspaceSuccessEpic,
        handleWorkspaceChangeEpic,
        handleCreateWorkspaceSuccessEpic,
        handleCreateWorkspaceEpic,
        handleWorkspaceRenameSuccessEpic,
        handleRepopulatedPlansSectionEpic,
        handleTableViewPageDataFiltersEpic
    );
}