import { of, concat, from, empty } from 'rxjs';
import { switchMap, mergeMap, concatMap, map, catchError } from 'rxjs/operators';
import { ENTITY_WINDOW, NOTIFICATION_DATA_ACTIONS, NOTIFICATION_SETTINGS_ACTIONS } from '../actions/actionTypes';
import { isArray } from 'lodash';
import { replaceUrl } from '../actions/navigateActions';
import { URL_PARAMS, TABLE_NAMES, MAP_ENTITY_TITLES, FEATURE_FLAGS } from '../constants/globalConsts';
import { NOTIFICATIONS_PAGE_ALIAS, NOTIFICATIONS_BOOKING_ALIAS } from '../constants/notificationsPageConsts';
import { updatePageParams } from '../actions/pageStateActions';
import { loadUserEntityAccess } from '../actions/userEntityAccessActions';
import {
    loadNotificationHistory,
    loadNotificationHistorySuccess,
    loadRealTimeNotificationHistorySuccess,
    onChangeNotificationReadUnreadStatusSuccess,
    markAllNotificationReadSuccess,
    deleteNotificationSuccess,
    loadNotificationHistoryError,
    deleteEntityFromNotificationEWSuccess,
    updateNotificationsEntitiesForTableData,
    loadNotificationHistoryUnreadCountSuccess,
    loadNotificationHistoryUnreadCount
} from '../actions/notificationsPageActions';
import { getNotificationsEntityIds, getStaticMessagesBySubSectionSelector } from '../selectors/notificationsPageSelectors';
import { combineEpics } from 'redux-observable';
import { ENTITY_WINDOW_MODULES, SUCCESS_STATUS } from '../constants';
import { createEntityWindowTableDataChangeInterceptorEpic } from './epicGenerators/entityWindowInterceptors';
import { entityWindowClose, entityWindowSetFormErrorMessages } from '../actions/entityWindowActions';
import { patchPagedDataSuccess } from '../actions/pagedDataActions';
import { DATA_GRID_TABLE_DATAS_SUFFIX } from '../constants/dataGridConsts';
import { loadMoreTableDataSuccess } from '../actions/tableDataActions';
import { ERROR_STATUS } from '../constants/apiConsts';
import { getAllNotificationsTabs, getFirstBookingInTheSeries, getNotificationQueryParams, isValidNotificationsTabExists } from '../utils/notificationsPageUtils';
import { ENTITY_WINDOW_OPERATIONS } from '../constants/entityWindowConsts';
import { loadEntitiesRequest$, getLoadEntitiesQuerySelection } from './entityWindowEpics';
import { getLinkedTableData, getTableDatasLoadedActions } from '../utils/linkedDataUtils';
import { getFieldInfo, getTableStructure } from '../selectors/tableStructureSelectors';
import { NOTIFICATION_HISTORY } from '../constants/notificationsPageConsts';
import { USER_FOUND } from 'redux-oidc';
import { BOOKING_GUID, BOOKING_RESOURCE_GUID, BOOKING_SERIES_GUID, BOOKING_START } from '../constants/fieldConsts';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { loadSkillPreferences, populateResourceSkills, recommendationSkillsWindowLoad } from '../actions/resourceSkillsActions';
import { editResourceSkillsChangeTab, editResourceSkillsWindowOpen } from '../actions/editResourceSkillsWindowActions';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';

const { TAB } = URL_PARAMS;
const {
    LOAD_NOTIFICATION_HISTORY,
    NOTIFICATIONS_LOAD_DATA,
    NEW_NOTIFICATION_MESSAGE,
    CHANGE_NOTIFICATION_READ_UNREAD_STATUS,
    MARK_ALL_NOTIFICATION_READ,
    DELETE_NOTIFICATIONS,
    UPDATE_NOTIFICATIONS_ENTITIES_TABLE_DATA,
    LOAD_NOTIFICATIONS_UNREAD_DATA_COUNT } = NOTIFICATION_DATA_ACTIONS;

const API_NAME = 'notifications';
const resourceSkillsApiKey = 'resourceSkills';

const buildNotificationsParams = (tab) => {

    return {
        [TAB]: tab
    };
};

const getAppUrlAction = (actionType) => {
    switch (actionType) {
        case `${NOTIFICATIONS_LOAD_DATA}`:
            return replaceUrl;
    }
};

const loadUserEntityActions = (data) => {
    const actions = [];
    const bookingIds = getNotificationsEntityIds(data)(MAP_ENTITY_TITLES.Booking);
    const roleIds = getNotificationsEntityIds(data)(MAP_ENTITY_TITLES.rolerequest);

    bookingIds.length > 0 && actions.push(loadUserEntityAccess(bookingIds, TABLE_NAMES.BOOKING));
    roleIds.length > 0 && actions.push(loadUserEntityAccess(roleIds, TABLE_NAMES.ROLEREQUEST));
    actions.push(loadNotificationHistoryUnreadCount({ isLoading: true }));

    return actions;
};

export const notificationPageEpic$ = (action$, state$) => {

    return action$
        .ofType(
            `${NOTIFICATIONS_LOAD_DATA}`
        ).pipe(
            switchMap(
                (action) => {
                    const { params = {} } = action.payload || {};
                    const { tab } = params;
                    const actions = [];
                    const notificationStaticMessagesTabs = getStaticMessagesBySubSectionSelector(state$.value, getAllNotificationsTabs(state$.value)) || {};
                    const { notificationHistoryLabel = NOTIFICATION_HISTORY } = notificationStaticMessagesTabs;
                    const validTab = isValidNotificationsTabExists(tab, notificationStaticMessagesTabs);
                    const navigatedDataTab = validTab ? validTab.tabName : notificationHistoryLabel;
                    const urlAction = getAppUrlAction(action.type);
                    const queryParams = getNotificationQueryParams([]);
                    actions.push(
                        of(urlAction(buildNotificationsParams(navigatedDataTab))),
                        of(loadNotificationHistory({ queryParams, isLoading: true }))
                    );

                    if (validTab) {
                        actions.push(of(updatePageParams(NOTIFICATIONS_PAGE_ALIAS, { tab: navigatedDataTab })));
                    }

                    return concat(...actions);
                }
            )
        );
};

export const loadNotificationHistoryData$ = (action$, state$, { apis }) => {
    const actionOfInterest = [LOAD_NOTIFICATION_HISTORY];

    const loadNotificationHistory$ = action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap((action) => {
                const { payload = {} } = action;
                const { queryParams = '' } = payload;

                return apis[API_NAME].getNotificationHistory$(queryParams);
            },
            (action, result) => {
                return [result, action];
            })
        );

    const loadNotificationHistorySuccess$ = loadNotificationHistory$.pipe(
        switchMap(
            ([result]) => {
                const actions = [];

                if (result && result.notificationDetails) {
                    const data = result.notificationDetails || [];

                    data.forEach(notificationItem => {
                        const { notificationDescription = '' } = notificationItem;
                        if (typeof(notificationDescription) === 'string') {
                            notificationItem.notificationDescription = JSON.parse(notificationDescription);
                        }
                    });

                    actions.push(...loadUserEntityActions(data));
                    actions.push(loadNotificationHistorySuccess(result));
                    actions.push(updateNotificationsEntitiesForTableData(data));
                } else {
                    actions.push(loadNotificationHistoryError());
                }

                return of(...actions);
            }
        )
    );

    return loadNotificationHistorySuccess$;
};

export const loadRealTimeNotification$ = (action$) => {
    return action$.ofType(NEW_NOTIFICATION_MESSAGE).pipe(
        switchMap((action) => {
            const { payload = {} } = action;
            const actions = [];
            const convertedObj = JSON.parse(payload);
            const data = isArray(convertedObj) ? convertedObj : [convertedObj];
            data.forEach(notificationItem => {
                const { notificationDescription = '' } = notificationItem;
                if (typeof(notificationDescription) === 'string') {
                    notificationItem.notificationDescription = JSON.parse(notificationDescription);
                }
            });

            actions.push(...loadUserEntityActions(data));
            actions.push(loadRealTimeNotificationHistorySuccess(data));
            actions.push(updateNotificationsEntitiesForTableData(data));

            return of(...actions);
        })
    );
};

export const notificationReadUnreadStatus$ = (action$, state$, { apis }) => {
    const actionOfInterest = [CHANGE_NOTIFICATION_READ_UNREAD_STATUS];

    const changeNotificationReadUnreadStatus$ = action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap((action) => {
                const { payload = {} } = action;

                return apis[API_NAME].updateNotificationReadUnreadStatus$([payload]);
            },
            (action, result) => {
                return [result, action];
            })
        );

    return changeNotificationReadUnreadStatus$.pipe(
        switchMap(
            ([result, action]) => {

                if (result == undefined || result.status === ERROR_STATUS) {
                    return empty();
                }
                const { payload = {} } = action;
                const actions = [];
                actions.push(onChangeNotificationReadUnreadStatusSuccess(payload));
                actions.push(loadNotificationHistoryUnreadCount({ isLoading: true }));

                return of(...actions);
            }
        )
    );
};

export const setAllNotificationRead$ = (action$, state$, { apis }) => {
    const actionOfInterest = [MARK_ALL_NOTIFICATION_READ];

    const markAllNotificationRead$ = action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap((action) => {
                const { payload } = action;

                return apis[API_NAME].batchUpdateNotificationMarkAllRead$({ notificationPreferenceKeys:payload });
            },
            (result) => {
                return [result];
            })
        );

    return markAllNotificationRead$.pipe(
        switchMap(
            ([result]) => {
                if (result == undefined || result.status === ERROR_STATUS) {
                    return empty();
                }
                const actions = [];
                actions.push(markAllNotificationReadSuccess());

                return of(...actions);
            }
        )
    );
};


/**
 * Load resource data required for displaying recommendation skills
 * Loads resource skills, skill preferences, recommendations skills and opens tab in recommendation mode
 *
 * @param {Object} action$ contains the action type and payload (entityId)
 * @param {Object} state$ contains global redux state value
 * @param {{ apis: any; }} apis contains list of apis
 * @returns {Array} list of actions to load the recommendation modal
 */
export const loadRecommendationNotificationEpic$ = (action$, state$, { apis }) => {
    const actionOfInterest = [NOTIFICATION_DATA_ACTIONS.LOAD_RECOMMENDATION_NOTIFICATION];

    const getResourceSkills$ = action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap((action) => {
                const { payload = {} } = action;
                const { entityId } = payload;

                return apis[resourceSkillsApiKey].getResourceSkills$(entityId);
            }, (action, result) => {
                return [action, result];
            })
        );

    return getResourceSkills$.pipe(
        switchMap(
            ([action, result]) => {

                if (result == undefined || result.status === ERROR_STATUS) {
                    return empty();
                }
                const { payload = {} } = action;
                const { entityId } = payload;
                const actions = [];
                actions.push(populateResourceSkills(null, { entityId }, result));
                actions.push(loadSkillPreferences());
                actions.push(recommendationSkillsWindowLoad(entityId));
                actions.push(editResourceSkillsWindowOpen(entityId));
                actions.push(editResourceSkillsChangeTab('2'));

                return of(...actions);
            }
        )
    );
};

//update

export const notificaitonsPageEntityWindowRoleUpdateSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUEST,
    ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL),
        patchPagedDataSuccess(`${NOTIFICATIONS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`, { tableDataGuid: TABLE_NAMES.ROLEREQUEST }, true)
    ]
)();

const notificationsPageEntityWindowRoleUpdateErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUEST,
    ENTITY_WINDOW.SUBMIT_UPDATE_PAGED_DATA_ERROR,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL)
    ]
)();

export const notificationsPageEntityWindowBookingUpdateSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    NOTIFICATIONS_BOOKING_ALIAS,
    ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL)
    ]
)();

export const notificationsPageEntityWindowBookingUpdateErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    NOTIFICATIONS_BOOKING_ALIAS,
    ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    [
        entityWindowSetFormErrorMessages(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL),
        entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL)
    ]
)();

// Delete
export const notificationsPageDeleteEntitySuccessInterceptorEpic = (action$) => {
    const actionsOfInterest = [
        `${ENTITY_WINDOW.SUBMIT_DELETE_GROUPPED_TABLE_DATA_SUCCESS}_${NOTIFICATIONS_BOOKING_ALIAS}`,
        `${ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_SUCCESS}_${TABLE_NAMES.ROLEREQUEST}`
    ];

    return action$
        .ofType(...actionsOfInterest)
        .pipe(
            switchMap((action) => {
                const actions = [
                    entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL),
                    deleteEntityFromNotificationEWSuccess(action.payload)
                ];

                return from(actions);
            })
        );
};

export const notificationsPageDeleteRoleErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.ROLEREQUEST,
    ENTITY_WINDOW.SUBMIT_DELETE_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL)
    ]
)();


export const notificationsPageDeleteBookingErrorInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    NOTIFICATIONS_BOOKING_ALIAS,
    ENTITY_WINDOW.SUBMIT_DELETE_GROUPPED_TABLE_DATA_ERROR,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL)
    ]
)();

/**
 * Responsible for closing of recommendation modal on save
 */
export const notificationsPageResourceUpdateSuccessInterceptorEpic = createEntityWindowTableDataChangeInterceptorEpic(
    TABLE_NAMES.RESOURCE,
    ENTITY_WINDOW.SUBMIT_UPDATE_TABLE_DATA_SUCCESS,
    ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
    [
        entityWindowClose(ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL)
    ]
)();

export const deleteNotificationEpic$ = (action$, state$, { apis }) => {
    const actionOfInterest = [DELETE_NOTIFICATIONS];

    const deleteNotification$ = action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap((action) => {
                const { payload } = action;

                return apis[API_NAME].deleteNotifications$(payload);
            },
            (action, result) => {
                return [result, action];
            })
        );

    return deleteNotification$.pipe(
        switchMap(
            ([result, action]) => {
                const { payload = {} } = action;
                if (result.status !== SUCCESS_STATUS) {
                    return empty();
                }
                const actions = [];

                actions.push(deleteNotificationSuccess({ deletedGuid: payload }));
                actions.push(loadNotificationHistoryUnreadCount({ isLoading: true }));

                return of(...actions);
            }
        )
    );
};

export const updateNotificationsEntitiesForTableData$ = (action$, state$, { apis }) => {
    const actionOfInterest = [UPDATE_NOTIFICATIONS_ENTITIES_TABLE_DATA];

    const loadEntityFields$ = action$
        .ofType(...actionOfInterest).pipe(
            switchMap(
                ({ payload }) => {
                    const state = state$.value;
                    const tableName = TABLE_NAMES.ROLEREQUEST;
                    const roleIds = getNotificationsEntityIds(payload)(MAP_ENTITY_TITLES.rolerequest);

                    if (roleIds.length === 0) return empty();

                    const querySelection = getLoadEntitiesQuerySelection(tableName, ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL, roleIds, state, ENTITY_WINDOW_OPERATIONS.EDIT);

                    return loadEntitiesRequest$(tableName, querySelection, apis);
                },
                (action, respone) => [action, respone]
            )
        );

    return loadEntityFields$.pipe(

        mergeMap(([action, response]) => {
            let chain = [];
            const getFieldInfoWrapped = (tableName, fieldName) => getFieldInfo(getTableStructure(state$.value), tableName, fieldName);
            const linkedTableDatas = getLinkedTableData(response, TABLE_NAMES.ROLEREQUEST, getFieldInfoWrapped);
            const linkedTableDatasLoadedActions = getTableDatasLoadedActions(`${NOTIFICATIONS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`, linkedTableDatas);
            linkedTableDatasLoadedActions.forEach(action => chain.push(of(action)));

            chain.push(of(loadMoreTableDataSuccess(
                `${NOTIFICATIONS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`,
                {
                    tableDataGuid: TABLE_NAMES.ROLEREQUEST,
                    tableNames: [TABLE_NAMES.ROLEREQUEST]
                },
                response
            )));

            return concat(...chain);
        })

    );
};

export const loadNotificationDataUnreadCountEpic$ = (action$, state$, { apis }) => {
    const actionOfInterest = [LOAD_NOTIFICATIONS_UNREAD_DATA_COUNT];

    const loadUnreadDataCount$ = action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap((action) => {
                const { payload } = action;

                return apis[API_NAME].getNotificationDataUnreadCount$(payload);
            },
            (action, result) => {
                return [result, action];
            })
        );

    return loadUnreadDataCount$.pipe(
        switchMap(
            ([result]) => {

                const actions = [];

                actions.push(loadNotificationHistoryUnreadCountSuccess({ unReadCount: result.count }));

                return of(...actions);
            }
        )
    );
};

const updateNotificationEmailSettingsForUserEpic$ = (action$, state$, { apis }) => {
    const actionOfInterest = [USER_FOUND];

    return action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap(() => {
                return apis[API_NAME].updateNotificationsEmailSettingsForUser$();
            }),
            concatMap(() => empty())
        );
};

const loadFirstBookingInSeriesEpic$ = (action$, state$, { apis }) => {
    return action$
        .ofType(NOTIFICATION_DATA_ACTIONS.LOAD_FIRST_BOOKING_IN_THE_SERIES)
        .pipe(
            switchMap(({ payload: { bookingseries_guid, tableName, collectionAlias, operation } }) => {
                const userId = getApplicationUserId(state$.value);

                const selection = {
                    fields: [
                        { fieldName: BOOKING_GUID },
                        { fieldName: BOOKING_START },
                        { fieldName: BOOKING_RESOURCE_GUID }
                    ],
                    filter: {
                        filterGroupOperator: 'And',
                        filterLines: [
                            {
                                field: BOOKING_SERIES_GUID,
                                operator: 'Equals',
                                value: bookingseries_guid
                            },
                            {
                                field: BOOKING_RESOURCE_GUID,
                                operator: 'Equals',
                                value: userId
                            }
                        ],
                        subFilters: null
                    }
                };

                return apis['tableData'].getTableData$(TABLE_NAMES.BOOKING, selection).pipe(
                    map(result => ({
                        firstBookingInSeries: getFirstBookingInTheSeries(result),
                        tableName,
                        collectionAlias,
                        operation
                    })),
                    catchError(error => {
                        return of({ firstBookingInSeries: null, guid: bookingseries_guid });
                    })
                );
            }),
            mergeMap(({
                firstBookingInSeries,
                tableName,
                collectionAlias,
                operation
            }) => concat(
                of({
                    type: `${ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL}`,
                    payload: {
                        tableName,
                        collectionAlias,
                        entity: {},
                        operation,
                        entityId: firstBookingInSeries.booking_guid,
                        lazyLoadEntityData: true,
                        moduleName: ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
                        tableNames: [
                            tableName
                        ]
                    }
                })
            ))
        );
};

const loadUnconfirmedBookingsNotificationEpic$ = (action$, state$, { apis }) => {
    const actionOfInterest = [USER_FOUND, NOTIFICATION_SETTINGS_ACTIONS.UNCONFIRMED_BOOKINGS_NOTIFICATION_LOAD_DATA];

    return action$
        .ofType(...actionOfInterest)
        .pipe(
            switchMap(() => {
                const bookingTypeNotificationFeatureEnabled = getFeatureFlagSelector(FEATURE_FLAGS.BOOKING_TYPE_NOTIFICATION)(state$.value);

                return bookingTypeNotificationFeatureEnabled ?
                    apis[API_NAME].getUnconfirmedBookingsNotificationSettings$().pipe(
                        map(result => {
                            return result;
                        }),
                        catchError(() => {
                            return of({ isEnabled: null });
                        })
                    ) : of({ isEnabled: null });
            }),
            map((result) => ({
                type: `${NOTIFICATION_SETTINGS_ACTIONS.UNCONFIRMED_BOOKINGS_NOTIFICATION_LOAD_DATA}_${SUCCESS_STATUS}`,
                payload: result.isEnabled
            }))
        );
};

export default combineEpics(
    notificationPageEpic$,
    loadNotificationHistoryData$,
    loadRealTimeNotification$,
    notificationReadUnreadStatus$,
    setAllNotificationRead$,
    deleteNotificationEpic$,
    notificaitonsPageEntityWindowRoleUpdateSuccessInterceptorEpic,
    notificationsPageEntityWindowRoleUpdateErrorInterceptorEpic,
    notificationsPageDeleteEntitySuccessInterceptorEpic,
    notificationsPageDeleteRoleErrorInterceptorEpic,
    notificationsPageEntityWindowBookingUpdateSuccessInterceptorEpic,
    notificationsPageEntityWindowBookingUpdateErrorInterceptorEpic,
    notificationsPageDeleteBookingErrorInterceptorEpic,
    notificationsPageResourceUpdateSuccessInterceptorEpic,
    updateNotificationsEntitiesForTableData$,
    loadNotificationDataUnreadCountEpic$,
    updateNotificationEmailSettingsForUserEpic$,
    loadFirstBookingInSeriesEpic$,
    loadUnconfirmedBookingsNotificationEpic$,
    loadRecommendationNotificationEpic$
);