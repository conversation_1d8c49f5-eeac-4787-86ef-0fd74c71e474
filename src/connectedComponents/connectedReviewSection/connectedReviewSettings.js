import { connect } from 'react-redux';
import { getEntityWindowReviewSettingsSelector } from '../../selectors/entityWindowSelectors';
import ReviewSettings from '../../lib/reviewSection/reviewSettings';

/**
 * Builds a structured selection filter object used for querying or data extraction.
 * @param {string} params.prefix - A prefix to apply to all field names (e.g., table alias or namespace).
 * @param {Array} [params.select=[]] - An array of field selections, each with a `name` and optional `alias`.
 * @param {Object} [params.filters={}] - Key-value pairs specifying filters to apply.
 * @param {Array} [params.orderBy=[]] - A list of field names to order by, using ascending order.
 * @param {Array} [params.tables=[]] - Optional list of tables involved in the query.
 * @returns {Object} A structured object with the following properties:
 *   - fields: Array of field objects with prefixed names and aliases.
 *   - filter: Object defining filter conditions with 'And' logic.
 *   - order: Object specifying sorting order of the fields.
 *   - tables: The original table list, if provided.
 */
const buildSelectionFilter = ({
    prefix,
    select = [],
    filters = {},
    orderBy = [],
    tables = []
}) => {
    const withPrefix = name => `${prefix}_${name}`;

    return {
        fields: select.map(({ name, alias }) => ({
            fieldName: withPrefix(name),
            fieldAlias: alias
        })),
        filter: {
            filterGroupOperator: 'And',
            filterLines: Object.entries(filters).map(([name, value]) => ({
                field: withPrefix(name),
                operator: 'Equals',
                value
            }))
        },
        order: {
            orderFields: orderBy.map(name => ({
                field: withPrefix(name),
                order: 'Ascending'
            }))
        },
        tables
    };
};

const mapStateToProps = (state) => {
    const {
        headerTitle = '',
        saveChangesButtonLabel = '',
        cancelBtnLabel = '',
        resourceSkillsReviewLabel = '',
        reviewerOnThisJobLabel = '',
        eligibleForReviewLabel = '',
        reviewerOnThisJobCaptionLabel = '',
        pastBookingLabel = '',
        allBookedLabel = '',
        clearSkillsReviewsLabel = ''
    } = getEntityWindowReviewSettingsSelector(state);

    return {
        headerProps: {
            title: headerTitle
        },
        footerProps: {
            saveChangesButtonLabel,
            cancelBtnLabel
        },
        bodyProps: {
            resourceSkillsReviewLabel,
            reviewerOnThisJobLabel,
            eligibleForReviewLabel,
            reviewerOnThisJobCaptionLabel,
            pastBookingLabel,
            allBookedLabel,
            clearSkillsReviewsLabel
        }
    };
};

const mapDispatchToProps = (dispatch) => ({
    loadResource: () => {
        const payload = buildSelectionFilter({
            prefix: 'resource',
            select: [
                { name: 'description', alias: 'value' },
                { name: 'guid', alias: 'id' }
            ],
            filters: { userstatus: true },
            orderBy: ['description']
        });

        return payload;
    }
});


const mergeProps = (stateProps, dispatchProps, ownProps) => ({
    ...ownProps,
    ...stateProps,
    ...dispatchProps
});

const ConnectedReviewSettings = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(ReviewSettings);

export { ConnectedReviewSettings };
