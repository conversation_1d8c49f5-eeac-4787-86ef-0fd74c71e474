import { connect } from 'react-redux';
import ApprovalSkillsWindow from '../../lib/skillsComponents/editSkillsWindow/approvals/approvalSkillsWindow.js';
import { getSkillsStaticMessages } from '../../selectors/editSkillsWindowSelectors';
import { getTPResourceId, getManagerIdAndName } from '../../selectors/talentProfileSelectors';
import { getApplicationUserId } from '../../selectors/applicationUserSelectors.js';
import { PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';
import { resendApprovalRequestToManager } from '../../actions/editResourceSkillsWindowActions.js';
import { getPendingApprovalsSelector } from '../../selectors/resourceSkillsSelectors';
import { getSkillInfoSelector } from '../../selectors/skillStructureSelectors';
import { getResourceSkillApprovalHistorySelector } from '../../selectors/resourceSkillsSelectors.js';

const mapStateToProps = (state, ownProps) => {
    const { pageAlias = '' } = ownProps;
    const resourceId = pageAlias === PROFILE_PAGE_ALIAS ? getTPResourceId(state) : getApplicationUserId(state);
    const staticMessages = getSkillsStaticMessages(state);
    const { managerName } = getManagerIdAndName(state);
    const pendingSkillsForApproval = getPendingApprovalsSelector(state)(resourceId);
    const getSkillInfo = getSkillInfoSelector(state);
    const resourceSkillApprovalHistory = getResourceSkillApprovalHistorySelector(state)(resourceId);

    return {
        resourceId,
        managerName,
        staticMessages,
        getSkillInfo,
        approvalRequestsData: {
            history: resourceSkillApprovalHistory,
            pendingRequests: pendingSkillsForApproval
        }
    };
};

const mapDispatchToProps = (dispatch) => ({
    onResendRequest: (resourceId) => {
        dispatch(resendApprovalRequestToManager(resourceId));
    },
    onApproveAll: () => {
        console.log('onApproveAll called');
    },
    onRejectAll: () => {
        console.log('onRejectAll called');
    },
    onApproveSkill: (skillId) => {
        console.log(`onApproveSkill called for skill ID: ${skillId}`);
    },
    onRejectSkill: (skillId) => {
        console.log(`onRejectSkill called for skill ID: ${skillId}`);
    },
    onLevelChange: (skillId, level) => {
        console.log(`onLevelChange called for skill ID: ${skillId} with level: ${level}`);
    }
});

const mergeProps = (stateProps, dispatchProps, ownProps) => {
    return {
        ...ownProps,
        ...stateProps,
        ...dispatchProps
    };
};

const ConnectedSkillApprovalsTab = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(ApprovalSkillsWindow);

export { ConnectedSkillApprovalsTab };

