import { connect } from 'react-redux';
import { EditSkillsWindow } from '../../lib/skillsComponents/editSkillsWindow';
import mapStateToPropsLookup from './skillsLookupProps';
import { SAVE_BUTTON_LABEL, CANCEL_BUTTON_LABEL, HEADER_TITLE } from '../../constants/editSkillsWindowConsts';
import {
    editResourceSkillsWindowClose,
    editResourceSkillsWindowAddSkills,
    editResourceSkillsWindowLoadAutoCompleteSkills,
    editResourceSkillsWindowClearAutoCompleteSkills,
    editResourceSkillsWindowUpdateSkills,
    editResourceSkillsChangeTab
} from '../../actions/editResourceSkillsWindowActions';
import {
    uiSkillFieldChange, markSkillForDeletion, resourceSkillDiscardDelete, resourceSkillsDiscardChanges,
    onAcceptRecommendSkills,
    onIgnoreRecommendSkills,
    discardIgnoreRecommendationSkills,
    loadAuthorizeResourceSkills
} from '../../actions/resourceSkillsActions';
import { TALENT_PROFILE_MARK_DELETED_MESSAGE, TALENT_PROFILE_CANCEL_DELETION_MESSAGE, PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';
import { getTPResourceId, getManagerIdAndName, isAllowedToViewResourceSkillsApproval } from '../../selectors/talentProfileSelectors';
import {
    getUIResourceSkillSelector, getResourceSkillsCanSaveSelector, getSkillPreferenceSelector, getResourceSkillPreferenceSelector,
    checkSkillPendingApproval, isResourceSkillsApprovalsPending
} from '../../selectors/resourceSkillsSelectors';
import { getAriaLabelDeleteSkillButtonSelector, getEditResourceSkillsWindowSections, getSkillsStaticMessages, getRecommendationSectionData, getActionedRecommendedSkillIdsSelector, getAuthorizedResourceSkillsSelector } from '../../selectors/editSkillsWindowSelectors';
import { getSkillFieldInfoSelector, getSkillInfoSelector, getSkillFormConfigSelector, getHiddenSkillFieldIdSelector } from '../../selectors/skillStructureSelectors';
import { mapPropsToFields, debouncedFieldsChange } from '../connectedSkillFieldsForm.js';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors.js';
import { getSkillEntityTypes } from '../../selectors/adminSettingSelectors/skillConfigurationSelectors.js';
import { EDIT_RESOURCE_SKILLS_WINDOW } from '../../actions/actionTypes.js';
import { FEATURE_FLAGS } from '../../constants/globalConsts.js';
import { getApplicationUserId } from '../../selectors/applicationUserSelectors.js';
import { getResourceSkillApprovalPermission } from '../../selectors/talentProfileSelectors.js';

const mapStateToProps = (state, ownProps) => {
    const skillLabels = getSkillsStaticMessages(state);
    const getAriaLabelDeleteSkillButton = getAriaLabelDeleteSkillButtonSelector(state);
    const editSkillsWindowLabels = {
        header: {
            title: skillLabels.headerTitle || HEADER_TITLE
        },
        body: {
            config: {
                markDeletedMessage: skillLabels.markDeletedMessage || TALENT_PROFILE_MARK_DELETED_MESSAGE,
                cancelDeletionMessage: skillLabels.cancelDeletionMessage || TALENT_PROFILE_CANCEL_DELETION_MESSAGE
            },
            staticMessages: skillLabels
        },
        footer: {
            config: {
                saveButtonLabel: skillLabels.primarySaveButtonLabel || SAVE_BUTTON_LABEL,
                cancelButtonLabel: skillLabels.cancelButtonLabel || CANCEL_BUTTON_LABEL
            }
        }
    };

    const {
        editResourceSkillsWindow: {
            visible,
            activeTab
        }
    } = state;
    const { pageAlias = '' } = ownProps;
    const resourceId = pageAlias === PROFILE_PAGE_ALIAS ? getTPResourceId(state) : getApplicationUserId(state);
    const getFieldsConfig = getSkillFormConfigSelector(state);
    const getUISkill = getUIResourceSkillSelector(state);
    const getSkillFieldInfo = getSkillFieldInfoSelector(state);
    const getSkillInfo = getSkillInfoSelector(state);
    const getResourceSkillPreference = getResourceSkillPreferenceSelector(state);
    const skillSections = getEditResourceSkillsWindowSections(state)(resourceId);
    const recommendationSectionData = getRecommendationSectionData(state)(resourceId);
    const skillPreferences = getSkillPreferenceSelector(state)();
    const getActionedRecommendedSkillIds = getActionedRecommendedSkillIdsSelector(state);
    const recommendationsEnabled = getFeatureFlagSelector(FEATURE_FLAGS.RECOMMENDATIONS)(state);
    const skillsFilterCascaderEnabled = getFeatureFlagSelector(FEATURE_FLAGS.TALENT_PROFILE_PAGE_SKILLS_FILTER_CASCADER)(state);
    const skillTaxonomyValues = getAuthorizedResourceSkillsSelector(state);
    const skillApprovalFeatureFlagEnabled = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state);
    const resourceSkillApprovalPermission = getResourceSkillApprovalPermission(state);
    const skillApprovalFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state);
    const { managerName } = getManagerIdAndName(state);
    const isAllowedToViewSkills = isAllowedToViewResourceSkillsApproval(state);
    const skillApprovalEnabled = resourceSkillApprovalPermission && skillApprovalFeatureFlag && isAllowedToViewSkills && managerName !== null;

    return {
        visible,
        activeTab,
        recommendationsEnabled,
        entityId: resourceId,
        headerProps: {
            ...editSkillsWindowLabels.header,
            ...mapStateToPropsLookup(state),
            skillsData: skillTaxonomyValues,
            getSkillEntityTypes: getSkillEntityTypes(state),
            showButtons: true,
            selectedSkillsOptions: [],
            selectedValues: [],
            isDropdownChild: EDIT_RESOURCE_SKILLS_WINDOW.ADD.SKILLS
        },
        bodyProps: {
            ...editSkillsWindowLabels.body,
            skillSections: skillSections,
            recommendationSectionData: recommendationSectionData,
            entityId: resourceId,
            hiddenFieldIds: getHiddenSkillFieldIdSelector(state)(),
            mapPropsToFields,
            getFieldsConfig,
            getUISkill,
            getSkillFieldInfo,
            getSkillInfo,
            getAriaLabelDeleteSkillButton,
            skillPreferences: skillPreferences,
            getResourceSkillPreference,
            getActionedRecommendedSkillIds,
            skillApprovalFeatureFlagEnabled: skillApprovalFeatureFlagEnabled,
            resourceSkillApprovalPermission: resourceSkillApprovalPermission,
            skillApprovalEnabled: skillApprovalEnabled,
            isResourceSkillsApprovalsPendingLoaded: isResourceSkillsApprovalsPending(state, resourceId),
            getIsSkillPending: (resourceId, skillId) => checkSkillPendingApproval(state, resourceId, skillId)
        },
        footerProps: {
            ...editSkillsWindowLabels.footer,
            entityId: resourceId,
            saveButtonEnabled: getResourceSkillsCanSaveSelector(state)(resourceId)
        },
        skillsFilterCascaderEnabled,
        selectedSkillId: ownProps.selectedSkillId
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        getAutoCompleteSkills: (searchTerm, maxResults) => {
            dispatch(editResourceSkillsWindowLoadAutoCompleteSkills(searchTerm, maxResults, true));
        },
        onClearAutoComplete: () => {
            dispatch(editResourceSkillsWindowClearAutoCompleteSkills());
        },
        onAutoCompleteSkillsSelect: (entityId, skillIds, skillInfos) => {
            dispatch(editResourceSkillsWindowAddSkills(entityId, 'addSkillsSectionId', skillIds, skillInfos));
        },
        onAcceptRecommendSkills: (entityId, skillIds, skillInfos) => {
            dispatch(onAcceptRecommendSkills(entityId, 'addSkillsSectionId', skillIds, skillInfos));
        },
        onIgnoreRecommendSkills: (entityId, skillIds) => {
            dispatch(onIgnoreRecommendSkills(entityId, skillIds));
        },
        onRemoveSkill: (resourceId, skillId) => {
            dispatch(markSkillForDeletion(resourceId, skillId));
        },
        onRemoveSkillCancel: (resourceId, skillId) => {
            dispatch(resourceSkillDiscardDelete(resourceId, skillId));
        },
        onSave: (resourceId) => {
            dispatch(editResourceSkillsWindowUpdateSkills(resourceId));
            dispatch(editResourceSkillsWindowClose());
            dispatch(discardIgnoreRecommendationSkills(resourceId));
        },
        onCancel: (resourceId) => {
            dispatch(resourceSkillsDiscardChanges(resourceId));
            dispatch(editResourceSkillsWindowClose());
            dispatch(discardIgnoreRecommendationSkills(resourceId));
        },
        onTabChange: (tabIndex) => {
            dispatch(editResourceSkillsChangeTab(tabIndex));
        },
        onSkillFieldChange: (entityId, skillId, fieldInfo, fieldValue, skillInfo, errors) => {
            dispatch(uiSkillFieldChange(entityId, skillId, fieldInfo, fieldValue, skillInfo, errors));
        },
        onSkillsFieldsChange: debouncedFieldsChange,
        loadResourceSkillsAllowed: () => {
            dispatch(loadAuthorizeResourceSkills());
        }
    };
};

const mergeProps = (stateProps, dispatchProps, ownProps) => ({
    ...ownProps,
    ...stateProps,
    ...dispatchProps,
    headerProps: {
        ...stateProps.headerProps,
        onSelectionChange: (selectedData) => {
            const {
                entityId,
                bodyProps: { getSkillInfo }
            } = stateProps;

            const skillIds = selectedData.map(
                ({ skillsData }) => skillsData.id
            );
            const skillInfos = skillIds.map(getSkillInfo);

            dispatchProps.onAutoCompleteSkillsSelect(
                entityId,
                skillIds,
                skillInfos
            );
        }
    }
});

const ConnectedEditResourceSkillsWindow = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(EditSkillsWindow);

export { ConnectedEditResourceSkillsWindow };
