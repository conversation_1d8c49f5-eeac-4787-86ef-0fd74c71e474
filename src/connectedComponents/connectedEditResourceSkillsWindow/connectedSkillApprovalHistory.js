import { connect } from 'react-redux';
import { getSkillsStaticMessages } from '../../selectors/editSkillsWindowSelectors';
import { getTPResourceId } from '../../selectors/talentProfileSelectors';
import { getApplicationUserId } from '../../selectors/applicationUserSelectors.js';
import { PROFILE_PAGE_ALIAS } from '../../constants/talentProfileConsts';
import { getResourceSkillApprovalHistorySelector } from '../../selectors/resourceSkillsSelectors.js';
import { TIMELINE_BACK_TO_TOP, TIMELINE_DEFAULT_LOGS_COUNT, TIMELINE_SHOW_MORE, TIMELINE_SHOW_MORE_LOGS_COUNT, TIMELINE_SORT_LABEL } from '../../constants/auditTrailConsts.js';
import { SORT_ASCENDING, SORT_DESCENDING } from '../../constants/selectionConsts.js';
import { resourceSkillsApprovalsHistory } from '../../actions/resourceSkillsActions.js';
import SkillsApprovalHistoryTimeline from '../../lib/skillsComponents/editSkillsWindow/skillsApprovalHistoryTimeline.js';

const mapStateToProps = (state, ownProps) => {
    const { pageAlias = '' } = ownProps;
    const resourceId = pageAlias === PROFILE_PAGE_ALIAS ? getTPResourceId(state) : getApplicationUserId(state);
    const staticMessages = getSkillsStaticMessages(state);
    const resourceSkillApprovalHistory = getResourceSkillApprovalHistorySelector(state)(resourceId);

    return {
        resourceId,
        staticMessages,
        approvalRequestsData: {
            history: resourceSkillApprovalHistory,
            entityId: resourceId,
            config: {
                constants: {
                    defaultLogsCount: TIMELINE_DEFAULT_LOGS_COUNT,
                    showMoreLogsCount: TIMELINE_SHOW_MORE_LOGS_COUNT,
                    sortAscending: SORT_ASCENDING,
                    sortDescending: SORT_DESCENDING
                }
            },
            sort: {
                order: SORT_DESCENDING
            },
            staticLabels: {
                showMoreText: TIMELINE_SHOW_MORE,
                sortLabel: TIMELINE_SORT_LABEL,
                backToTopText: TIMELINE_BACK_TO_TOP
            }
        }
    };
};

const mapDispatchToProps = (dispatch) => ({
    loadSkillsApprovalsHistory: (resourceId, sortBy, pageSize, pageNumber) => {
        dispatch(resourceSkillsApprovalsHistory(resourceId, sortBy, pageSize, pageNumber));
    }
});

const mergeProps = (stateProps, dispatchProps, ownProps) => {
    return {
        ...ownProps,
        ...stateProps,
        ...dispatchProps
    };
};


const ConnectedSkillApprovalHistory = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(SkillsApprovalHistoryTimeline);

export { ConnectedSkillApprovalHistory };

