import * as actionTypes from '../../actions/actionTypes';
import { UPDATE_TABLE_VIEW_CELL_DATA } from '../../actions/tableViewActions/actions';
import {
    ExtendJobRangePromptLogic,
    DeleteBooking<PERSON>romptLogic,
    <PERSON>etePlanPromptLogic,
    MakePlanPrivate<PERSON>romptLogic,
    CreatePlanPromptLogic,
    RenamePlanPromptLogic,
    SaveChangesPromptLogic,
    <PERSON><PERSON>ultPromptLogic,
    DeleteClientPromptLogic,
    DeleteJobPromptLogic,
    DeleteCommentPromptLogic,
    DeleteRolePromptLogic,
    BatchedCR<PERSON>DErrorPromptLogic,
    DeleteBookingsPromptLogic,
    CantPasteBookingPrompt,
    SingleCRUDErrorPromptLogic,
    ProgressRolesErrorPromptLogic,
    DeleteRoleGroupPromptLogic,
    ProgressRolesExtendJobRangePromptLogic,
    RejectRolesErrorPromptLogic,
    DeleteRolerequestsPromptLogic,
    DeleteRoleDetailed<PERSON>romptLogic,
    RollForwardSingleCreateErrorPromptLogic,
    RollForwardBatchCreateErrorPrompt,
    MoveRolerequestTimeAllocationPromptLogic,
    RemoveRolerequestTimeAllocationPromptLogic,
    CreateAndRequestErrorPromptLogic,
    UpdateRolerequestStatusErrorPromptLogic,
    RemoveRolePublicationPrompt,
    DeleteRoleTemplatePromptLogic,
    RenameRoleTemplatePromptLogic,
    WithdrawRoleApplicationPromptLogic,
    SplitBarsPromptLogic,
    SetPasswordConfirmationPromptLogic,
    DeleteOperationLogPromptLogic,
    CreateListPagePlanPromptLogic
} from './promptsLogic';
import { PLANNER_BOOKING_GROUPS_ALIAS, ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS, PLANNER_ACTIONS, PLANNER_ROLEREQUESTS_ALIAS, PLANNER_PAGE_ALIAS, TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS, TABLEVIEW_CELL_GROUPS_ALIAS } from '../../constants/plannerConsts';
import { TABLE_NAMES } from '../../constants/globalConsts';
import { ENTITY_WINDOW_MODULES } from '../../constants';
import { ROLE_GROUP_LIST_PAGE_TABLE_DATA_ALIAS } from '../../constants/rolegroupListPageConsts';
import { ROLES_MODAL } from '../../constants/rolesConsts';
import { ROLE_INBOX_PAGE_ALIAS, ROLE_INBOX_PAGE_PAGED_DATA } from '../../constants/roleInboxPageConsts';
import { JOB_PAGE_PAGED_DATA, ROLE_GROUP_DETAILS_PAGE } from '../../constants/jobsPageConsts';
import { NOTIFICATIONS_BOOKING_ALIAS } from '../../constants/notificationsPageConsts';
import { EditCellCompoisitePromptLogic, EditTableViewCellErrorPromptLogic } from './tableViewPromptsLogic';
import { START_LONG_RUNNING_TASK_ERROR } from '../../reducers/longRunningTasksReducer';
import { LONG_RUNNING_TASK_TYPES } from '../../reducers/longRunningTasksReducer/state';
import { DuplicateJobErrorPromptLogic, DuplicateRoleGroupErrorPromptLogic } from './duplicatePromptsLogic';
import { OPEN_CONFIRMATION_SET_PASSWORD_DIALOG } from '../../components/adminSetting/serviceAccountManagement/epics/actions';
import { MANAGE_REPORTS_MODAL_ACTIONS } from '../../reducers/reportReducer/manageReportsModalActions';
import { ROLEGROUP_DUPLICATE_DIALOG_ACTIONS } from '../../reducers/roleGroupDuplicateReducer/actions';
import { UnsavedReportChangesPromptLogic } from './reportsPagePromptsLogic';

const { PROGRESS_ROLES_DATA_ALIAS, REJECT_ROLES_DATA_ALIAS, CREATE_AND_REQUEST_ROLES_DATA_ALIAS, UPDATE_STATUS_ALIAS } = ROLES_MODAL;

const defaultLogic = new DefaultPromptLogic();

const extendsJobRangePromptLogic = new ExtendJobRangePromptLogic();
const deleteBookingPromptLogic = new DeleteBookingPromptLogic();
const makePlanPrivatePromptLogic = new MakePlanPrivatePromptLogic();
const createPlanPromptLogic = new CreatePlanPromptLogic();
const createListPagePlanPromptLogic = new CreateListPagePlanPromptLogic();
const deletePlanPromptLogic = new DeletePlanPromptLogic();
const renamePlanPromptLogic = new RenamePlanPromptLogic();
const saveChangesPromptLogic = new SaveChangesPromptLogic();
const deleteClientPromptLogic = new DeleteClientPromptLogic();
const deleteJobPromptLogic = new DeleteJobPromptLogic();
const deleteCommentPromptLogic = new DeleteCommentPromptLogic();
const cantPasteBookingPrompt = new CantPasteBookingPrompt();
const batchedCRUDErrorPromptLogic = new BatchedCRUDErrorPromptLogic();
const singleCRUDErrorPromptLogic = new SingleCRUDErrorPromptLogic();
const deleteBookingsPromptLogic = new DeleteBookingsPromptLogic();
const deleteRolerequestsPromptLogic = new DeleteRolerequestsPromptLogic();
const moveRolerequestTimeAllocationPromptLogic = new MoveRolerequestTimeAllocationPromptLogic();
const removeRolerequestTimeAllocationPromptLogic = new RemoveRolerequestTimeAllocationPromptLogic();
const deleteRolePromptLogic = new DeleteRolePromptLogic();
const deleteOperationLogPromptLogic = new DeleteOperationLogPromptLogic();
const deleteRoleDetailedPromptLogic = new DeleteRoleDetailedPromptLogic();
const progressRolesErrorPromptLogic = new ProgressRolesErrorPromptLogic();
const createAndRequestErrorPromptLogic = CreateAndRequestErrorPromptLogic;
const deleteRoleGroupPromptLogic = new DeleteRoleGroupPromptLogic();
const progressRolesExtendJobPromptLogic = new ProgressRolesExtendJobRangePromptLogic();
const rejectRolesErrorPromptLogic = new RejectRolesErrorPromptLogic();
const rollForwardSingleCreatePromptLogic = new RollForwardSingleCreateErrorPromptLogic();
const rollForwardBatchCreatePromptLogic = new RollForwardBatchCreateErrorPrompt();
const updateRolerequestStatusErrorPromptLogic = new UpdateRolerequestStatusErrorPromptLogic();
const deleteRoleTemplatePromptLogic = new DeleteRoleTemplatePromptLogic();
const renameRoleTemplatePromptLogic = new RenameRoleTemplatePromptLogic();
const removeRolePublicationPromptLogic = new RemoveRolePublicationPrompt();
const withdrawRoleApplicationPromptLogic = new WithdrawRoleApplicationPromptLogic();
const editTableViewCellPromptLogic = new EditCellCompoisitePromptLogic();
const errorEditTableViewCellPromptLogic = new EditTableViewCellErrorPromptLogic();
const splitBarsPromptLogic = new SplitBarsPromptLogic();
const setPasswordConfirmationPromptLogic = new SetPasswordConfirmationPromptLogic();
const duplicateJobErrorPromptLogic = new DuplicateJobErrorPromptLogic();
const unsavedReportChangesPomptLogic = new UnsavedReportChangesPromptLogic();
const duplicateRoleGroupErrorPromptLogic = new DuplicateRoleGroupErrorPromptLogic();

const actionsPromptSetupMap = {
    aliased: {
        [`${actionTypes.PATCH_GROUPPED_TABLE_DATA}_${PLANNER_BOOKING_GROUPS_ALIAS}`]: extendsJobRangePromptLogic,
        [`${actionTypes.DELETE_GROUPPED_TABLE_DATA}_${PLANNER_BOOKING_GROUPS_ALIAS}`]: deleteBookingPromptLogic,
        [`${actionTypes.DELETE_GROUPPED_TABLE_DATA}_${PLANNER_ROLEREQUESTS_ALIAS}`]: deleteRoleDetailedPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE}_${PLANNER_BOOKING_GROUPS_ALIAS}`]: deleteBookingPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE}_${PLANNER_ROLEREQUESTS_ALIAS}`]: deleteRoleDetailedPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE}_${ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS}`]: deleteClientPromptLogic,
        [`${actionTypes.DELETE_TABLE_DATA}_${ENTITY_LOOKUP_WINDOW_CLIENT_ALIAS}`]: deleteClientPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE}_${TABLE_NAMES.JOB}`]: deleteJobPromptLogic,
        [`${actionTypes.DELETE_TABLE_DATA}_${TABLE_NAMES.JOB}`]: deleteJobPromptLogic,
        [`${actionTypes.DELETE_TABLE_DATA}_${JOB_PAGE_PAGED_DATA}`]: deleteJobPromptLogic,
        [`${actionTypes.BATCH_DELETE_GROUPED_TABLE_DATA}_${PLANNER_BOOKING_GROUPS_ALIAS}`]: deleteBookingsPromptLogic,
        [`${actionTypes.BATCH_DELETE_GROUPED_TABLE_DATA}_${PLANNER_ROLEREQUESTS_ALIAS}`]: deleteRolerequestsPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${PLANNER_BOOKING_GROUPS_ALIAS}`]: deleteBookingsPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${PLANNER_ROLEREQUESTS_ALIAS}`]: deleteRolerequestsPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMUT_BATCH_DELETE_ENTITIES}_${TABLE_NAMES.ROLEREQUEST}`]: deleteRolerequestsPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.DELETE_ENTITY}_${ENTITY_WINDOW_MODULES.ROLE_REQUEST_FORM}`]: deleteRolePromptLogic,
        [`${actionTypes.DELETE_TABLE_DATA}_${ROLE_GROUP_LIST_PAGE_TABLE_DATA_ALIAS}`]: deleteRoleGroupPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE}_${TABLE_NAMES.ROLEREQUEST}`]: deleteRoleDetailedPromptLogic,
        [`${actionTypes.DELETE_TABLE_DATA}_${ROLE_INBOX_PAGE_PAGED_DATA}`]: deleteRoleDetailedPromptLogic,
        [`${actionTypes.BATCH_DELETE_TABLE_DATA}_${ROLE_INBOX_PAGE_PAGED_DATA}`]: deleteRolerequestsPromptLogic,
        [`${actionTypes.ENTITY_WINDOW.SUBMIT_DELETE}_${NOTIFICATIONS_BOOKING_ALIAS}`]: deleteBookingPromptLogic,
        [`${actionTypes.MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${ROLE_GROUP_DETAILS_PAGE}`]: moveRolerequestTimeAllocationPromptLogic,
        [`${actionTypes.REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${ROLE_GROUP_DETAILS_PAGE}`]: removeRolerequestTimeAllocationPromptLogic,
        [`${actionTypes.MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${ROLE_INBOX_PAGE_ALIAS}`]: moveRolerequestTimeAllocationPromptLogic,
        [`${actionTypes.REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${ROLE_INBOX_PAGE_ALIAS}`]: removeRolerequestTimeAllocationPromptLogic,
        [`${actionTypes.MOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${PLANNER_PAGE_ALIAS}`]: moveRolerequestTimeAllocationPromptLogic,
        [`${actionTypes.REMOVE_PENDING_ROLEREQUEST_TIME_ALLOCATION}_${PLANNER_PAGE_ALIAS}`]: removeRolerequestTimeAllocationPromptLogic,
        [`${UPDATE_TABLE_VIEW_CELL_DATA}_${TABLEVIEW_PAGE_TABLE_TO_GROUP_ALIAS[TABLE_NAMES.BOOKINGBYWEEKVIEW]}`]: editTableViewCellPromptLogic,
        [`${actionTypes.UPDATE_CELL_DATA_ERROR}_${TABLEVIEW_CELL_GROUPS_ALIAS}`]: errorEditTableViewCellPromptLogic,
        [`${START_LONG_RUNNING_TASK_ERROR}_${LONG_RUNNING_TASK_TYPES.DUPLICATE_JOB}`]: duplicateJobErrorPromptLogic,
        [`${ROLEGROUP_DUPLICATE_DIALOG_ACTIONS.SINGLE_CREATE_ERROR}`]: duplicateRoleGroupErrorPromptLogic,
        [actionTypes.DELETE_ROLE_GROUP]: deleteRoleGroupPromptLogic,
        [actionTypes.OPERATION_LOG_DIALOG_ACTIONS.CANCEL_TASK]: deleteOperationLogPromptLogic
    },
    default: {
        [actionTypes.DELETE_JOB_PROMPT]: deleteJobPromptLogic,
        [actionTypes.BATCH_CRUD_ERROR_PROMPT]: batchedCRUDErrorPromptLogic,
        [actionTypes.SINGLE_CRUD_ERROR_PROMPT]: singleCRUDErrorPromptLogic,
        [actionTypes.DIGEST_CREATE_WORKSPACE] : createPlanPromptLogic,
        [actionTypes.LIST_PAGE_ACTIONS.CREATE_WORKSPACE] : createListPagePlanPromptLogic,
        [actionTypes.DELETE_WORKSPACE] : deletePlanPromptLogic,
        [actionTypes.RENAME_WORKSPACE] : renamePlanPromptLogic,
        [actionTypes.LIST_PAGE_ACTIONS.DIGEST_CREATE_WORKSPACE] : createPlanPromptLogic,
        [actionTypes.LIST_PAGE_ACTIONS.RENAME_WORKSPACE] : renamePlanPromptLogic,
        [actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE] : deletePlanPromptLogic,
        [actionTypes.LIST_PAGE_ACTIONS.SAVE_WORKSPACE_IF_ANY_CHANGES] : saveChangesPromptLogic,
        [actionTypes.LIST_PAGE_ACTIONS.MOVE_WORKSPACE] : makePlanPrivatePromptLogic,
        [actionTypes.SAVE_WORKSPACE_IF_ANY_CHANGES] : saveChangesPromptLogic,
        [actionTypes.MOVE_WORKSPACE] : makePlanPrivatePromptLogic,
        [actionTypes.COMMENTS.DELETE] : deleteCommentPromptLogic,
        [PLANNER_ACTIONS.CANT_PASTE_BAR_PROMPT] : cantPasteBookingPrompt,
        [actionTypes.DELETE_ROLE_PROMPT]: deleteRolePromptLogic,
        [`${actionTypes.ROLE_TRANSITION_DIALOG.ERROR}_${PROGRESS_ROLES_DATA_ALIAS}`]: progressRolesErrorPromptLogic,
        [`${actionTypes.ROLE_TRANSITION_DIALOG.ERROR}_${UPDATE_STATUS_ALIAS}`]: updateRolerequestStatusErrorPromptLogic,
        [`${actionTypes.ROLE_TRANSITION_DIALOG.ERROR}_${CREATE_AND_REQUEST_ROLES_DATA_ALIAS}`]: createAndRequestErrorPromptLogic,
        [`${actionTypes.ROLE_TRANSITION_DIALOG.PERFORM}_${PROGRESS_ROLES_DATA_ALIAS}`]: progressRolesExtendJobPromptLogic,
        [actionTypes.ROLE_GROUP_DETAILS_PAGE_ACTIONS.DELETE]: deleteRoleGroupPromptLogic,
        [`${actionTypes.ROLE_TRANSITION_DIALOG.ERROR}_${REJECT_ROLES_DATA_ALIAS}`]: rejectRolesErrorPromptLogic,
        [actionTypes.ROLL_FORWARD_SINGLE_CREATE_ERROR]: rollForwardSingleCreatePromptLogic,
        [actionTypes.ROLL_FORWARD_BATCH_CREATE_ERROR]: rollForwardBatchCreatePromptLogic,
        [actionTypes.DIGEST_SAVE_AS_NEW_PLAN] : createPlanPromptLogic,
        [actionTypes.REMOVE_ROLE_PUBLICATION]: removeRolePublicationPromptLogic,
        [actionTypes.DELETE_ROLE_TEMPLATE]: deleteRoleTemplatePromptLogic,
        [actionTypes.ENTITY_WINDOW.WITHDRAW_ROLE_APPLICATION]: withdrawRoleApplicationPromptLogic,
        [actionTypes.RENAME_ROLE_TEMPLATE]: renameRoleTemplatePromptLogic,
        [actionTypes.SPLIT_BARS_ERROR]: splitBarsPromptLogic,
        [OPEN_CONFIRMATION_SET_PASSWORD_DIALOG]: setPasswordConfirmationPromptLogic,
        [MANAGE_REPORTS_MODAL_ACTIONS.SET_VISIBILITY]: unsavedReportChangesPomptLogic,
        [MANAGE_REPORTS_MODAL_ACTIONS.SETUP]: unsavedReportChangesPomptLogic,
        [actionTypes.DELETE_ROLE_GROUP]: deleteRoleGroupPromptLogic
    }
};

const getPrompSetupObj = (actionType) => {
    //TODO Add custom logic to retrieve the default implementation if there is no aliased and aliased is passed if needed at a later poind
    const aliased = actionsPromptSetupMap.aliased[actionType];
    const def = actionsPromptSetupMap.default[actionType];
    return def !== undefined ? def.getSetup() : aliased !== undefined ? aliased.getSetup() : defaultLogic.getSetup();
};

export default getPrompSetupObj;