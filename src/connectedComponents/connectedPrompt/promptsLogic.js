import { patchPagedData } from '../../actions/pagedDataActions';
import { patchTableData } from '../../actions/tableDataActions';
import { getSelectedWorkspaceSettings } from '../../selectors/workspaceSelectors';
import { getTranslationsSelector } from '../../selectors/internationalizationSelectors';
import { getRange, insideRange } from '../../utils/rangeUtils';
import { PLANNER_MASTER_REC_ALIAS, PLANNER_PAGE_ALIAS, PLANNER_SUB_REC_ALIAS } from '../../constants/plannerConsts';
import { digestSelectWorkspace } from '../../actions/workspaceActions';
import { TABLE_NAMES } from '../../constants';
import * as managePlansSectionActions from '../../actions/managePlansSectionActions';
import { getJobEntry, prepareRange, getNewJobRange, getBarEntry, getJobGuid, getCreatePlanPromptAction } from '../../utils/promptUtils';
import connectedPrompts from './connectedPrompts';
import { getManagePendingTimeAllocationPromptMessages, getStaticPromptMessagesSelector } from '../../selectors/promptsSelectors';
import { stringReplacePlaceholders } from '../../utils/commonUtils';
import { getCurrentPlannerBarGroupsObject } from '../../selectors/plannerPageSelectors';
import { getEntityAlias as getEntityAliasESUtil } from '../../utils/entityStructureUtils';
import { getEntityInfoSelector } from '../../selectors/entityStructureSelectors';
import { getEntityAliasSelector } from '../../selectors/entityStructureSelectors';
import { performRoleTransition } from '../../actions/roleTransitionDialogsActions';
import { EDIT_ALL_ENTITY_ID } from '../../constants/entityWindowConsts';
import { ROLES_MODAL, ROLES_TRANSITION_ACTION } from '../../constants/rolesConsts';
import { getActionWithNewTableDataEntryGuids } from '../../utils/promptUtils';
import { buildCommonEntityAliasesPlaceholdersSelector, populateStringTemplates } from '../../utils/translationUtils';
import { loadRemoveRolePublicationPromptContext } from '../../actions/promptContextActions';
import { marketplaceWithdrawSubmit } from '../../actions/marketplacePageActions';
import { JOB_GUID } from '../../constants/fieldConsts';
import { isValidDate } from '../../utils/dateUtils';
import { setServiceAccountPassword } from '../../components/adminSetting/serviceAccountManagement/reducers/actions';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';

const { PROGRESS_ROLES_DATA_ALIAS } = ROLES_MODAL;

/* eslint-disable */
//This is to be used as an abstract class only
export class BasePromptLogic {
    constructor(contentComponent) {
        this.modal = null;
        this.contentComponent = contentComponent;
        this.cancelHandle = this.cancelHandle.bind(this);
    }

    shouldPrompt(state, payload) {
        throw "BasePromptLogic.shouldPrompt Not implemented";
    }

    getPromptModalProps(dispatch, state, action, modal) {
        throw "BasePromptLogic.getPromptModalProps Not implemented";
    }

    //Possible onDataInit
    onMount(){
        throw "BasePromptLogic.onMount Not implemented";
    }

    onUnmount(){
        throw "BasePromptLogic.onUnmount Not implemented";
    }

    setModal(modal) {
        this.modal = modal;
    }

    okHandle(...params) {
        throw "BasePromptLogic.okHandle Not implemented";
    }

    getLoadContextHandler() {
       return undefined; 
    }

    internalCancelHandle(...params) {
        // Do nothing
    }

    getButtonsLabelTranslation(state, subSectionName, defaultOkText = 'Ok', defaultCancelText = 'Cancel'){
        const translationIdsArray = [subSectionName];
        const translationSectionInfo = {sectionName: 'prompts', idsArray: translationIdsArray};
        const translations = getTranslationsSelector(state, translationSectionInfo)[subSectionName];
        const translatedOkText = translations ? translations.okText : null;
        const translatedCancelText = translations ? translations.cancelText : null;

        return {
            okText: translatedOkText ? translatedOkText : defaultOkText,
            cancelText: translatedCancelText ? translatedCancelText : defaultCancelText
        }
    }

    cancelHandle(...params) {
        this.internalCancelHandle(...params);
        this.closePrompt();
    }

    closePrompt() {
        null !== this.modal && this.modal.destroy();
    }

    getContentComponent() {
        return this.contentComponent;
    }

    getSetup() {
        return {
            shouldPrompt: this.shouldPrompt.bind(this),
            getPromptModalProps: this.getPromptModalProps.bind(this),
            setModal: this.setModal.bind(this),
            getContentComponent : this.getContentComponent.bind(this),
            getLoadContextHandler: this.getLoadContextHandler.bind(this)
        };
    }
}

class DefaultPromptLogic extends BasePromptLogic {
    shouldPrompt(state, payload) {
        return false;
    }

    getPromptModalProps(dispatch, state, action) {
        return {};
    }

    getSetup() {
        return {
            shouldPrompt: this.shouldPrompt.bind(this),
            getPromptModalProps: this.getPromptModalProps.bind(this)
        };
    }
}

/* eslint-enable */

class ExtendJobRangePromptLogic extends BasePromptLogic {
    constructor(contentComponent = connectedPrompts.ConnectedExtendJobDatesContent) {
        super(contentComponent);
        this.pageAlias = PLANNER_PAGE_ALIAS;
    }

    okHandle(dispatch, state, action) {
        const { payload } = action;
        this.dispatchJobRangeUpdate(dispatch, state, payload);
        dispatch(action);
        this.closePrompt();
    }

    isMasterRec(settings) {
        return settings.masterRecTableName === TABLE_NAMES.JOB;
    }

    getJobEntry(state, payload) {
        const page = state[this.pageAlias];
        const bookingEntry = getBarEntry(page, payload, TABLE_NAMES.BOOKING);
        const jobGuid = getJobGuid(bookingEntry, payload);

        return getJobEntry(page, jobGuid);
    }

    getNewJobEntry(state, payload) {
        const jobEntry = this.getJobEntry(state, payload);
        const jobRange = prepareRange(getRange(jobEntry, TABLE_NAMES.JOB));
        const bookingRange = prepareRange(getRange(payload.tableData, TABLE_NAMES.BOOKING));
        const newRange = getNewJobRange(jobRange, bookingRange);

        return {
            job_start: newRange.start,
            job_end: newRange.end
        };
    }

    dispatchUpdatePagedData(dispatch, jobId, newJobEntry, wsSettings) {
        dispatch(patchPagedData(PLANNER_MASTER_REC_ALIAS, wsSettings.pagedMasterRecPlannerDataGuid, TABLE_NAMES.JOB,jobId, newJobEntry));
    }

    dispatchUpdateTableData(dispatch, jobId, newJobEntry, wsSettings) {
        dispatch(patchTableData(PLANNER_SUB_REC_ALIAS, wsSettings.subRecPlannerDataGuid, TABLE_NAMES.JOB, jobId, newJobEntry));
    }

    //This might change when proper chain observables are introduced
    dispatchJobRangeUpdate(dispatch, state, payload) {
        const page = state[this.pageAlias];
        const jobEntry = this.getJobEntry(state, payload);
        const newEntry = this.getNewJobEntry(state, payload);

        const workspaceSettings = getSelectedWorkspaceSettings(page.workspaces);

        const jobId = jobEntry[JOB_GUID];
        let dispatchFunc = () => null;

        if (this.isMasterRec(workspaceSettings))
            dispatchFunc = this.dispatchUpdatePagedData;
        else
            dispatchFunc = this.dispatchUpdateTableData;

        dispatchFunc(dispatch, jobId, newEntry, workspaceSettings);
    }

    shouldPrompt(state, payload) {
        const page = state[this.pageAlias];
        const selectedBars = getCurrentPlannerBarGroupsObject(page)[TABLE_NAMES.BOOKING].currentEdits; //Temporary solution until intractions for roles are implemented
        let shouldPrompt = false;

        if (selectedBars.length <= 1) {
            const bookingEntry = getBarEntry(page, payload, TABLE_NAMES.BOOKING);
            const jobGuid = getJobGuid(bookingEntry, payload);
            const jobEntry = getJobEntry(page, jobGuid);
            const jobRange = prepareRange(getRange(jobEntry, TABLE_NAMES.JOB));
            const bookingRange = prepareRange(getRange(payload.tableData, TABLE_NAMES.BOOKING)); // refactor?

            const jobStartIsValid = isValidDate(jobRange.start);
            const jobEndIsValid = isValidDate(jobRange.end);

            if (jobStartIsValid && jobEndIsValid) {
                if (false === isValidDate(bookingRange.start)) bookingRange.start = getRange(bookingEntry, TABLE_NAMES.BOOKING).start;

                if (false === isValidDate(bookingRange.end)) bookingRange.end = getRange(bookingEntry, TABLE_NAMES.BOOKING).end;

                shouldPrompt = !insideRange(jobRange, bookingRange, true);
            }
        }

        return shouldPrompt;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const defaultOkText = 'Yes, amend job dates';
        const defaultCancelText = 'No, cancel move';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'extendJobRangePrompt', defaultOkText, defaultCancelText);

        return {
            onOk: onOk,
            onCancel: onCancel,
            icon: null,
            cancelText,
            okText
        };
    }
}

class DeleteBookingPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteBookingContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const bookingAlias = getEntityAliasSelector(state, TABLE_NAMES.BOOKING, { singularForm: true, capitalized: false });
        const defaultOkText = 'Yes, delete the';
        const defaultCancelText = 'No, keep the';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'deleteBookingPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: `${cancelText} ${bookingAlias}`,
            okText: `${okText} ${bookingAlias}`,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class DeleteBookingsPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteBookingsContent);
    }

    okHandle(dispatch, state, action, modalContext) {
        const { tableDataEntryGuids } = modalContext;

        dispatch(getActionWithNewTableDataEntryGuids(action, tableDataEntryGuids));
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext = {}) {
        const onOk = () => this.okHandle(dispatch, state, action, modalContext);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        const { okText, cancelText } = getStaticPromptMessagesSelector(state, 'deleteMultipleBookingsPrompt');

        const bookingsCount = (modalContext.tableDataEntryGuids || []).length;
        const bookingsPluralUpper = getEntityAliasSelector(state, TABLE_NAMES.BOOKING, { singularForm: false, capitalized: true, fallbackValue: 'Bookings' });
        const bookingsSingularUpper = getEntityAliasSelector(state, TABLE_NAMES.BOOKING, { singularForm: true, capitalized: true, fallbackValue: 'Booking' });

        const placeholderValues = {
            bookingsAliasUpper: bookingsCount > 1 ? bookingsPluralUpper : bookingsSingularUpper
        };

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: stringReplacePlaceholders(cancelText, placeholderValues),
            okText: stringReplacePlaceholders(okText, placeholderValues),
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class DeleteRolerequestsPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteRolerequestsContent);
    }

    okHandle(dispatch, state, action, modalContext) {
        const { tableDataEntryGuids } = modalContext;

        dispatch(getActionWithNewTableDataEntryGuids(action, tableDataEntryGuids));
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext = {}) {
        const onOk = () => this.okHandle(dispatch, state, action, modalContext);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        const { okText, cancelText } = getStaticPromptMessagesSelector(state, 'deleteMultipleRolerequestsPrompt');

        const rolerequestsCount = (modalContext.tableDataEntryGuids || []).length;
        const rolerequestssPluralUpper = getEntityAliasSelector(state, TABLE_NAMES.ROLEREQUEST, { singularForm: false, capitalized: true, fallbackValue: 'Roles' });
        const rolerequestsSingularUpper = getEntityAliasSelector(state, TABLE_NAMES.ROLEREQUEST, { singularForm: true, capitalized: true, fallbackValue: 'Role' });

        const placeholderValues = {
            rolerequestsAliasUpper: rolerequestsCount > 1 ? rolerequestssPluralUpper : rolerequestsSingularUpper
        };

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: stringReplacePlaceholders(cancelText, placeholderValues),
            okText: stringReplacePlaceholders(okText, placeholderValues),
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class MoveRolerequestTimeAllocationPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedMovePendingTimeAllocationContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext = {}) {
        const { description, fixedTimeValue } = modalContext;
        const onOk = () => this.okHandle(dispatch, state, action, modalContext);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        const { okText, cancelText } = getManagePendingTimeAllocationPromptMessages(state, 'moveRolerequestTimeAllocationPrompt')(description, fixedTimeValue);

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class RemoveRolerequestTimeAllocationPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedRemovePendingTimeAllocationContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext = {}) {
        const { description, fixedTimeValue } = modalContext;
        const onOk = () => this.okHandle(dispatch, state, action, modalContext);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        const { okText, cancelText } = getManagePendingTimeAllocationPromptMessages(state, 'removeRolerequestTimeAllocationPrompt')(description, fixedTimeValue);

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class MakePlanPrivatePromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedMakePlanPrivateContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

        let defaultOkText;
        let defaultCancelText;

        if (listPageAndBulkUpdateFeatureFlag) {
            defaultOkText = 'Make workspace private';
            defaultCancelText = 'Keep workspace public';
        } else {
            defaultOkText = 'Make plan private';
            defaultCancelText = 'Keep plan public';
        }

        const { okText, cancelText } = this.getButtonsLabelTranslation(state, listPageAndBulkUpdateFeatureFlag ? 'makeWorkspacePrivatePrompt' : 'makePlanPrivatePrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText
        };
    }
}
class DeletePlanPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeletePlanContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

        let defaultOkText;
        let defaultCancelText;

        if (listPageAndBulkUpdateFeatureFlag) {
            defaultCancelText = 'No, keep workspace';
            defaultOkText = 'Yes, delete workspace';
        } else {
            defaultCancelText = 'No, keep plan';
            defaultOkText = 'Yes, delete plan';
        }

        const { okText, cancelText } = this.getButtonsLabelTranslation(state, listPageAndBulkUpdateFeatureFlag ? 'deleteWorkspacePrompt' : 'deletePlanPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class DeleteRoleTemplatePromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteRoleTemplateContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const defaultOkText = 'Yes, delete template';
        const defaultCancelText = 'No, keep template';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'deleteRoleTemplatePrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class DeleteClientPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteClientContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }


    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const defaultOkText = 'Yes, delete client';
        const defaultCancelText = 'No, keep client';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'deleteClientPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class DeleteJobPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteJobContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const jobAlias = getEntityAliasSelector(state, TABLE_NAMES.JOB, { singularForm: true, capitalized: false });
        const defaultOkText = 'Delete';
        const defaultCancelText = 'Keep';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'deleteJobPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: `${cancelText} ${jobAlias}`,
            okText: `${okText} ${jobAlias}`,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { disabled: true, className: this.okButtonDisabledClass }
        };
    }
}

class DeleteRolePromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteRoleContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(state, payload) {
        const { index, moduleName } = payload;
        const { batchIds, windows } = state.entityWindow.window[moduleName];

        const batchIndex = windows[EDIT_ALL_ENTITY_ID] && batchIds.length > 1 ? index - 1 : index;
        const id = batchIds[batchIndex];

        return !windows[id].newEntity;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const roleAlias = getEntityAliasSelector(state, TABLE_NAMES.ROLEREQUEST, { singularForm: true, capitalized: false });
        const defaultOkText = 'Yes, delete the ';
        const defaultCancelText = 'No, keep the ';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'deleteRolePrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: `${cancelText} ${roleAlias}`,
            okText: `${okText} ${roleAlias}`,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class DeleteRoleDetailedPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteRoleDetailedContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const roleAlias = getEntityAliasSelector(state, TABLE_NAMES.ROLEREQUEST, { singularForm: true, capitalized: false, fallbackValue: 'role' });
        const defaultOkText = 'Yes, delete the ';
        const defaultCancelText = 'No, keep the ';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'deleteRolePrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: `${cancelText} ${roleAlias}`,
            okText: `${okText} ${roleAlias}`,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class RemoveRolePublicationPrompt extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedRemoveRolePublicationContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getLoadContextHandler() {
        return loadRemoveRolePublicationPromptContext;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        const translationStrings = getStaticPromptMessagesSelector(state, 'removeRolePublicationPrompt');
        const commonEntityAliases = buildCommonEntityAliasesPlaceholdersSelector(state);
        const { okText, cancelText } = populateStringTemplates(translationStrings, commonEntityAliases);

        const defaultOkText = 'Delete role';
        const defaultCancelText = 'Keep role';

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: cancelText || defaultCancelText,
            okText: okText || defaultOkText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { disabled: true, className: 'ant-btn-danger' }
        };
    }
}

class CantPasteBookingPrompt extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedCantPasteBarContent);
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const footer = null;
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        return {
            footer,
            onCancel
        };
    }
}

class CreatePlanPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedCreatePlanContent);
    }

    okHandle(dispatch, state, action, planName, selectedPlanType, selectedEditRights) {
        const dispatchedAction = getCreatePlanPromptAction(action, planName, selectedPlanType, selectedEditRights);
        console.log('plans-okHandle:', {dispatch, state, action, planName, selectedPlanType, selectedEditRights, dispatchedAction})
        dispatch(dispatchedAction);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const { planName, selectedPlanType, selectedEditRights } = modalContext;
        const onOk = () => this.okHandle(dispatch, state, action, planName, selectedPlanType, selectedEditRights);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const cancelButtonProps = { className: 'ant-btn-tertiary' };
        const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
        let translationKey;
        if (listPageAndBulkUpdateFeatureFlag) {
            translationKey = 'createOrSaveAsNewWorkspacePrompt';
        } else {
            translationKey = 'createOrSaveAsNewPlanPrompt';
        }
        const staticMessages = getStaticPromptMessagesSelector(state, translationKey);
        const { okText, cancelText } = staticMessages;

        return {
            onOk,
            onCancel,
            icon: null,
            okText,
            cancelText,
            cancelButtonProps
        };
    }
}
class CreateListPagePlanPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedCreatePlanContent);
    }

    okHandle(dispatch, state, action, planName, selectedPlanType, selectedEditRights) {
        const dispatchedAction = getCreatePlanPromptAction(action, planName, selectedPlanType, selectedEditRights);
        console.log('list-okHandle:', {dispatch, state, action, planName, selectedPlanType, selectedEditRights, dispatchedAction})
        dispatch(dispatchedAction);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const { planName, selectedPlanType, selectedEditRights } = modalContext;
        const onOk = () => this.okHandle(dispatch, state, action, planName, selectedPlanType, selectedEditRights);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const cancelButtonProps = { className: 'ant-btn-tertiary' };
        let translationKey = 'createOrSaveAsNewWorkspacePrompt';

        const staticMessages = getStaticPromptMessagesSelector(state, translationKey);
        const { okText, cancelText } = staticMessages;

        return {
            onOk,
            onCancel,
            icon: null,
            okText,
            cancelText,
            cancelButtonProps
        };
    }
}

class RenamePlanPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedRenamePlanContent);
    }

    okHandle(dispatch, state, action) {
        const { workspaceGuid } = action.payload;
        dispatch(managePlansSectionActions.manageWorkspacesRemoveEditMode(workspaceGuid));
        dispatch(action);
        this.closePrompt();
    }

    internalCancelHandle(dispatch, state, action) {
        const { workspaceGuid } = action.payload;
        dispatch(managePlansSectionActions.manageWorkspacesRemoveEditMode(workspaceGuid));
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch,state, action);
        const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
        let defaultOkText;
        const defaultCancelText = 'Keep old name';

        if (listPageAndBulkUpdateFeatureFlag) {
            defaultOkText = 'Rename workspace';
        } else {
            defaultOkText = 'Rename plan';
        }

        const { okText, cancelText } = this.getButtonsLabelTranslation(state, listPageAndBulkUpdateFeatureFlag ? 'renameWorkspacePrompt' : 'renamePlanPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText
        };
    }
}

class RenameRoleTemplatePromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedRenameRoleTemplateContent);
    }

    okHandle(dispatch, _, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const defaultOkText = 'Rename template';
        const defaultCancelText = 'Keep old name';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'renameRoleTemplatePrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText
        };
    }
}

class SaveChangesPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedSaveChangesContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(digestSelectWorkspace(action.payload.selectWorkspaceGuid));
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle();
        const okButtonProps = { style: { float: 'left', marginRight: '10px' } };
        const cancelButtonProps = { className: 'ant-btn-tertiary' };
        const defaultOkText = 'Continue without saving';
        const defaultCancelText = 'Cancel';
        const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, listPageAndBulkUpdateFeatureFlag ? 'saveWorkspaceChangesPrompt' : 'saveChangesPrompt', defaultOkText, defaultCancelText);

        return {
            onCancel,
            onOk,
            icon: null,
            title: null,
            cancelText,
            okText,
            cancelButtonProps,
            okButtonProps
        };
    }
}

class SingleCRUDErrorPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedCommonSingleError);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        return {
            onOk,
            onCancel,
            icon: null,
            footer: null
        };
    }
}

class RollForwardSingleCreateErrorPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedRollForwardSingleError);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        return {
            onOk,
            onCancel,
            icon: null,
            footer: null
        };
    }
}

class BatchedCRUDErrorPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedCRUDErrorPrompt);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        return {
            onOk,
            onCancel,
            icon: null,
            footer: null
        };
    }
}

class RollForwardBatchCreateErrorPrompt extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedBatchRollForwardErorrPrompt);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        return {
            onOk,
            onCancel,
            icon: null,
            footer: null
        };
    }
}

class DeleteCommentPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteCommentContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const defaultOkText = 'Delete comment';
        const defaultCancelText = 'Keep comment';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'deleteCommentPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class ProgressRolesErrorPromptLogic extends BasePromptLogic {
    constructor(component = undefined) {
        super(component || connectedPrompts.ConnectedRolesTransitionErrorPrompt);
        this.promptTranslationKey = 'progressRolesWindow';
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getTitle(state, rolesCount) {
        const { title } = getStaticPromptMessagesSelector(state, this.promptTranslationKey);

        const getEntityInfo = getEntityInfoSelector(state);
        const entityInfo = getEntityInfo(TABLE_NAMES.ROLEREQUEST);
        const entitySingularLower = getEntityAliasESUtil(entityInfo, { singularForm: true, capitalized: false, fallbackValue: TABLE_NAMES.ROLEREQUEST });
        const entityPluralLower = getEntityAliasESUtil(entityInfo, { singularForm: false, capitalized: false, fallbackValue: TABLE_NAMES.ROLEREQUEST });

        const placeholderValues = {
            roleAlias: rolesCount > 1 ? entityPluralLower : entitySingularLower
        };

        return stringReplacePlaceholders(title, placeholderValues) || title;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        return {
            title: this.getTitle(state, modalContext.totalCount),
            footer: null,
            onCancel
        };
    }
}

class UpdateRolerequestStatusErrorPromptLogic extends ProgressRolesErrorPromptLogic {
    constructor(component = undefined) {
        super(component || connectedPrompts.ConnectedRolesTransitionErrorPrompt);
        this.promptTranslationKey = 'updateRolerequestStatusWindow';
    }
}

const CreateAndRequestErrorPromptLogic = new ProgressRolesErrorPromptLogic(connectedPrompts.ConnectedCreateAndRequestErrorPrompt);

class RejectRolesErrorPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedRolesTransitionErrorPrompt);
    }

    shouldPrompt() {
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const { errorDialogTitle } = getTranslationsSelector(state, { sectionName: 'rejectRolesWindow' }) || {};

        return {
            title: errorDialogTitle,
            footer: null,
            onCancel
        };
    }
}

class ProgressRolesExtendJobRangePromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedExtendJobRangePrompt);
        this.promptTranslationKey = 'extendJobRangeDetailsPagePrompt';
        this.defaultOkText = 'Yes, delete the';
        this.defaultCancelText = 'No, keep the';
    }

    shouldPrompt(state) {
        const { progressRolesWindow } = state;
        const { roleTransitionActionType } = progressRolesWindow;
        const { currentEdits, data, byId } = progressRolesWindow.rolesData;
        let result = false;

        currentEdits.forEach(element => {
            const id = byId[element];
            if (data[id].rolerequest_requires_job_extension == 1 && roleTransitionActionType === ROLES_TRANSITION_ACTION.ROLES_ACTION_MAKE_LIVE) {
                result = true;
            }
        });

        return result;
    }

    okHandle(dispatch, state, action) {
        dispatch(performRoleTransition(action.payload, PROGRESS_ROLES_DATA_ALIAS));
        this.closePrompt();
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const { promptTranslationKey, defaultOkText, defaultCancelText } = this;
        const { okText, cancelText, title } = this.getButtonsLabelTranslation(state, promptTranslationKey, defaultOkText, defaultCancelText);

        return {
            onOk : () => this.okHandle(dispatch, state, action),
            onCancel: () => this.cancelHandle(dispatch, state, action),
            title,
            okText,
            cancelText
        };
    }
}

class DeleteRoleGroupPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteRoleGroupContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);

        const translationKey = 'deleteRoleGroupPrompt';
        const translationsStrings = getStaticPromptMessagesSelector(state, translationKey);
        const roleGroupAliasSingular = getEntityAliasSelector(state, TABLE_NAMES.ROLEREQUESTGROUP, { singularForm: true, capitalized: false, fallbackValue: TABLE_NAMES.ROLEREQUESTGROUP });

        const placeholderValues = {
            roleGroupAliasSingular
        };
        let staticMessages = {};

        Object.keys(translationsStrings).forEach(key => {
            staticMessages = {
                ...staticMessages,
                [key]: stringReplacePlaceholders(translationsStrings[key], placeholderValues) || translationsStrings[key]
            };
        });
        const { confirmMessage, cancelMessage } = staticMessages;

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: cancelMessage,
            okText: confirmMessage,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { disabled: true, className: this.okButtonDisabledClass }
        };
    }
}

class DeleteOperationLogPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedDeleteOperationLogContent);
    }

    okHandle(dispatch, state, action) {
        dispatch(action);
        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action, modalContext) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const translationKey = 'deleteOperationLogPrompt';
        const translationsStrings = getStaticPromptMessagesSelector(state, translationKey);

        const { confirmMessage, cancelMessage } = translationsStrings;

        return {
            onOk,
            onCancel,
            icon: null,
            cancelText: cancelMessage,
            okText: confirmMessage,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { disabled: true, className: this.okButtonDisabledClass }
        };
    }
}

class PublishRoleErrorPrompt extends ProgressRolesErrorPromptLogic {
    constructor(component = undefined) {
        super(component || connectedPrompts.ConnectedRolesTransitionErrorPrompt);
        this.promptTranslationKey = 'publishRoleErrorPrompt';
    }
}
class WithdrawRoleApplicationPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedWithdrawRoleApplicationPrompt);
    }

    okHandle(dispatch, state, action) {
        dispatch(marketplaceWithdrawSubmit(action.payload));

        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const defaultOkText = 'Yes, withdraw my application';
        const defaultCancelText = 'No, keep my application';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'withdrawRoleApplicationPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-danger' }
        };
    }
}

class SplitBarsPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedSplitBarsErrorPrompt);
    }

    shouldPrompt() {
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const { cancelText } = this.getButtonsLabelTranslation(state, 'splitBookingsErrorPrompt');

        return {
            onCancel,
            icon: null,
            title: null,
            cancelText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { style: { display: 'none' } }
        };
    }
}

class EditRolePublicationErrorPrompt extends ProgressRolesErrorPromptLogic {
    constructor(component = undefined) {
        super(component || connectedPrompts.ConnectedRolesTransitionErrorPrompt);
        this.promptTranslationKey = 'editRolePublicationPrompt';
    }
}

class SetPasswordConfirmationPromptLogic extends BasePromptLogic {
    constructor() {
        super(connectedPrompts.ConnectedSetPasswordConfirmationPrompt);
    }

    okHandle(dispatch, state, action) {
        dispatch(setServiceAccountPassword(action.payload));

        this.closePrompt();
    }

    shouldPrompt(/*state, payload*/) {
        //We always want to show this prompt for now
        return true;
    }

    getPromptModalProps(dispatch, state, action) {
        const onOk = () => this.okHandle(dispatch, state, action);
        const onCancel = () => this.cancelHandle(dispatch, state, action);
        const defaultOkText = 'Yes, I\'m sure';
        const defaultCancelText = 'No, go back';
        const { okText, cancelText } = this.getButtonsLabelTranslation(state, 'setPasswordConfirmationPrompt', defaultOkText, defaultCancelText);

        return {
            onOk,
            onCancel,
            icon: null,
            title: null,
            cancelText,
            okText,
            cancelButtonProps: { className: 'ant-btn-secondary' },
            okButtonProps: { className: 'ant-btn-primary' },
            className: 'setPasswordConfirmationPrompt'
        };
    }
}

export {
    DefaultPromptLogic,
    ExtendJobRangePromptLogic,
    DeleteBookingPromptLogic,
    MakePlanPrivatePromptLogic,
    CreatePlanPromptLogic,
    CreateListPagePlanPromptLogic,
    DeletePlanPromptLogic,
    RenamePlanPromptLogic,
    SaveChangesPromptLogic,
    DeleteClientPromptLogic,
    DeleteJobPromptLogic,
    DeleteCommentPromptLogic,
    DeleteOperationLogPromptLogic,
    DeleteRolePromptLogic,
    DeleteRoleDetailedPromptLogic,
    DeleteRolerequestsPromptLogic,
    MoveRolerequestTimeAllocationPromptLogic,
    RemoveRolerequestTimeAllocationPromptLogic,
    CantPasteBookingPrompt,
    BatchedCRUDErrorPromptLogic,
    DeleteBookingsPromptLogic,
    SingleCRUDErrorPromptLogic,
    ProgressRolesErrorPromptLogic,
    CreateAndRequestErrorPromptLogic,
    DeleteRoleGroupPromptLogic,
    ProgressRolesExtendJobRangePromptLogic,
    RejectRolesErrorPromptLogic,
    RollForwardSingleCreateErrorPromptLogic,
    RollForwardBatchCreateErrorPrompt,
    UpdateRolerequestStatusErrorPromptLogic,
    PublishRoleErrorPrompt,
    RemoveRolePublicationPrompt,
    EditRolePublicationErrorPrompt,
    DeleteRoleTemplatePromptLogic,
    RenameRoleTemplatePromptLogic,
    WithdrawRoleApplicationPromptLogic,
    SplitBarsPromptLogic,
    SetPasswordConfirmationPromptLogic
};
