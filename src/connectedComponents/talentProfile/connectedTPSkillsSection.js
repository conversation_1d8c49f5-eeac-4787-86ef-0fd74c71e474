import { connect } from 'react-redux';
import TpSkillsSection from '../../lib/talentProfile/tpSkillsSection';
import { getGroupedResourceSkills } from '../../selectors/resourceSkillsSelectors';
import { profileSkillsSectionVisible, getProfileSkillsSectionConfig, getTPResourceId, getResourceAuditSectionSelector, getResourceManager, getResourceSkillApprovalPermission } from '../../selectors/talentProfileSelectors';
import { editResourceSkillsWindowOpen } from '../../actions/editResourceSkillsWindowActions';
import { SKILL_SECTION_EDIT_BUTTON_LABEL, SKILL_SECTION_ADD_BUTTON_LABEL } from '../../constants/skillsSectionConsts';
import { getAriaLabelDeleteSkillButtonSelector, getSkillsStaticMessages } from '../../selectors/editSkillsWindowSelectors';
import { TABLE_NAMES } from '../../constants';
import { isSectionSysMaintainedSelector } from '../../selectors/skillStructureSelectors';
import { recommendationSkillsWindowLoad, resourceSkillsApprovalsPending, resourceSkillsApprovalsHistory } from '../../actions/resourceSkillsActions';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';

const mapStateToProps = (state) => {
    const config = getProfileSkillsSectionConfig(state);
    const resourceId = getTPResourceId(state);
    const resourceSkills = getGroupedResourceSkills(state)(resourceId);
    const hasResourceSkills = Object.keys(resourceSkills).length === 0;
    const skillLabels = getSkillsStaticMessages(state);
    const { addSkillsButtonLabel = SKILL_SECTION_ADD_BUTTON_LABEL, editSkillsButtonLabel = SKILL_SECTION_EDIT_BUTTON_LABEL } = skillLabels;
    const skillsAuditInfo = getResourceAuditSectionSelector(state)(TABLE_NAMES.RESOURCESKILL);
    const getAriaLabelDeleteSkillButton = getAriaLabelDeleteSkillButtonSelector(state);
    const getIsSectionSysMaintained = isSectionSysMaintainedSelector(state);
    const recommendationsEnabled = getFeatureFlagSelector(FEATURE_FLAGS.RECOMMENDATIONS)(state);
    const skillApprovalFeatureFlagEnabled = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state);
    const getFeatureFlag = (flag) => getFeatureFlagSelector(flag)(state);
    const resourceManager = getResourceManager(state);
    const resourceSkillApprovalPermission = getResourceSkillApprovalPermission(state);

    return {
        visible: profileSkillsSectionVisible(state),
        id: config.SectionKey || 'skillSection',
        resourceId,
        resourceSkills,
        config,
        editButtonText: hasResourceSkills ? addSkillsButtonLabel : editSkillsButtonLabel,
        skillsStaticLabels: skillLabels,
        hasResourceSkills,
        skillsAuditInfo,
        getAriaLabelDeleteSkillButton,
        getIsSectionSysMaintained: (sectionId) => getIsSectionSysMaintained(sectionId),
        recommendationsEnabled,
        skillApprovalFeatureFlagEnabled,
        getFeatureFlag,
        resourceManager,
        resourceSkillApprovalPermission
    };
};

const mapDispatchToProps = (dispatch) => {
    return {
        onEditClick: (resourceId) => {
            dispatch(editResourceSkillsWindowOpen(resourceId));
        },
        loadRecommendations: (resourceId) => {
            dispatch(recommendationSkillsWindowLoad(resourceId));
        },
        loadSkillsApprovalsPending: (resourceId) => {
            dispatch(resourceSkillsApprovalsPending(resourceId));
        },
        loadSkillsApprovalsHistory: (resourceId, sortBy, pageSize, pageNumber) => {
            dispatch(resourceSkillsApprovalsHistory(resourceId, sortBy,pageSize, pageNumber));
        }
    };
};

const ConnectedTpSkillsSection = connect(
    mapStateToProps,
    mapDispatchToProps
)(TpSkillsSection);

export { ConnectedTpSkillsSection };
