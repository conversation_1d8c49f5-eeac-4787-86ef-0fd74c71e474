import { connect } from 'react-redux';
import { omit } from '../utils/commonUtils';
import { shouldSaveIfAnyChanges } from '../utils/workspaceUtils';
import ManagePlansWindow from '../lib/managePlansWindow';
import { getOrderedPrivateWorkspaces, getOrderedPublicWorkspaces, getWorkspacesForInsert,
    getDefaultWorkspaceStructure, getWorkspaceSettings, getSelectedWorkspaceGuid,
    workspaceBelongsToResource, getSaveAsNewPlanWorkspaceSelector } from '../selectors/workspaceSelectors';
import { hasFunctionalAccessGlobal } from '../selectors/applicationFnasSelectors';
import { getApplicationUserId } from '../selectors/applicationUserSelectors';
import { setManageMyPlansWindowVisibility } from '../actions/managePlansSectionActions';
import { getManageMyPlansWindowStaticMessagesSelector } from '../selectors/workspaceSelectors';
import { JOBS_PAGE_ALIAS } from '../constants/jobsPageConsts';
import { promptAction } from '../actions/promptActions';
import * as listPageActions from '../actions/listPageActions';
import * as managePlansSectionActions from '../actions/managePlansSectionActions';
import { getFeatureFlagSelector } from '../selectors/featureManagementSelectors';
import { FEATURE_FLAGS, WORKSPACE_ACCESS_TYPES, WORKSPACE_EDIT_RIGHTS } from '../constants/globalConsts';

const applyManageRightsToPlans = (plans, userId, hasManagePublicPlansAccess) => {
    return plans.map(plan => {
        const canManage = workspaceBelongsToResource(plan, userId) || hasManagePublicPlansAccess;

        return {
            ...plan,
            canManage
        };
    });
};

const mapStateToProps = state => {
    const listPageWorkspaces = state.listPage.workspaces || {};
    let { selected } = listPageWorkspaces;
    const { workspacesInEditMode, managePublicPlansFNAName } = state.plannerPage.manageMyPlans;
    const workspacesInEditModeGuids = Object.keys(workspacesInEditMode);
    const plansForInsertInEditMode = listPageWorkspaces.workspacesStructureChanges ? getWorkspacesForInsert(state.listPage.workspaces).filter(workspace => workspacesInEditModeGuids.includes(workspace.workspace_guid)) : [];
    const workspacesStructureMap = (listPageWorkspaces.workspacesStructure && listPageWorkspaces.workspacesStructure.map) || {};
    const privatePlans = [...getOrderedPrivateWorkspaces(plansForInsertInEditMode), ...getOrderedPrivateWorkspaces(workspacesStructureMap)];
    const defaultWorkspaceStructure = getDefaultWorkspaceStructure(listPageWorkspaces);
    const publicPlans = applyManageRightsToPlans(
        [...getOrderedPublicWorkspaces(plansForInsertInEditMode), ...getOrderedPublicWorkspaces(workspacesStructureMap)],
        getApplicationUserId(state),
        hasFunctionalAccessGlobal(state, managePublicPlansFNAName)
    );
    const visible = state.plannerPage.manageMyPlans.visible;
    const getState = () => state;
    const staticMessages = getManageMyPlansWindowStaticMessagesSelector(state);
    const listPageAndBulkUpdateFeatureFlag = getFeatureFlagSelector(FEATURE_FLAGS.LIST_PAGE_AND_BULK_UPDATE)(state);

    return {
        visible,
        staticMessages,
        privatePlansColumn: {
            type: WORKSPACE_ACCESS_TYPES.PRIVATE,
            title: listPageAndBulkUpdateFeatureFlag ? staticMessages.privateWorkspacesColumnTitle : staticMessages.privatePlansColumnTitle,
            activePlanGuid: selected,
            plans: privatePlans
        },
        publicPlansColumn: {
            type: WORKSPACE_ACCESS_TYPES.PUBLIC,
            title: listPageAndBulkUpdateFeatureFlag ? staticMessages.publicWorkspacesLabel : staticMessages.publicPlansLabel,
            plans: publicPlans,
            activePlanGuid: selected
        },
        workspacesInEditMode,
        defaultWorkspace: defaultWorkspaceStructure,
        getState,
        listPageAndBulkUpdateFeatureFlag
    };
};

const mapDispatchToProps = dispatch => {
    return {
        onClose: () => {
            dispatch(setManageMyPlansWindowVisibility(false));
        },
        promptActions: {
            promptDeletePlan: (workspaceGuid, getState) => {
                const dispatchAction = listPageActions.deleteListPageWorkspace(workspaceGuid);
                dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
            },
            promptRenamePlan: (workspaceGuid, newDescription, getState) => {
                const dispatchAction = listPageActions.renameListPageWorkspace(workspaceGuid, { 'workspace_description': newDescription });
                dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
            },
            promptSelectPlan: (workspaceGuid, getState) => {
                const state = getState();
                const workspaces = state.listPage.workspaces;
                const currentWorkspaceGuid = getSelectedWorkspaceGuid(workspaces);

                dispatch(setManageMyPlansWindowVisibility(false));

                if (shouldSaveIfAnyChanges(workspaces, currentWorkspaceGuid)) {
                    const dispatchAction = listPageActions.saveListPageWorkspaceIfAnyChanges(currentWorkspaceGuid, workspaceGuid);
                    dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
                } else {
                    dispatch(listPageActions.digestSelectListPageWorkspace(workspaceGuid));
                }
            },
            promptMovePlan: (uuid, accessType, editRights, getState) => {
                const workspaceData = {
                    'workspace_accesstype': accessType,
                    'workspace_editrights': editRights
                };
                const dispatchAction = listPageActions.moveListPageWorkspace(uuid, workspaceData);
                dispatch(promptAction(dispatchAction, JOBS_PAGE_ALIAS));
            }
        },
        sectionActions :{
            WsAddEditMode :(guid, type) => {
                dispatch(managePlansSectionActions.manageWorkspacesAddEditMode(guid, type));
            },
            WsRemoveEditMode:(guid) => {
                dispatch(managePlansSectionActions.manageWorkspacesRemoveEditMode(guid));
            }
        },
        actions :{
            renamePlan: (workspaceGuid,newDescription) => {
                dispatch(listPageActions.renameListPageWorkspace(workspaceGuid, { 'workspace_description' : newDescription }));
            },
            digestCreatePlan: (defaultWorkspaceGuid, privatePlans, newPlanLabel) => {
                const doCreate = false;
                dispatch(listPageActions.digestCreateListPageWorkspace(WORKSPACE_ACCESS_TYPES.PRIVATE, WORKSPACE_EDIT_RIGHTS.EDIT, defaultWorkspaceGuid, privatePlans, newPlanLabel, doCreate));
            },
            createPlan: (uuid, newDescription) => {
                const selectCreatedWorkspace = false;
                dispatch(listPageActions.digestListPageWorkspaceStructureChange(uuid, { 'workspace_description' : newDescription }));
                dispatch(listPageActions.createListPageWorkspace(uuid, selectCreatedWorkspace));
            },
            removeCreatePlanChange: (uuid) => {
                dispatch(listPageActions.removeCreateListPageWorkspaceChange(uuid));
            },
            copyPlan: (newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName, getState) => {
                const doCreate = true;
                const templateWorkspaceSettings = getWorkspaceSettings(getState().listPage.workspaces, newWorkspaceTemplateGuid);
                const newWorkspaceSettings = getSaveAsNewPlanWorkspaceSelector(getState().listPage.workspaces);
                const { workspaceColourthemeGuid = null, workspaceCustomColourTheme = [] } = newWorkspaceSettings;

                if (!templateWorkspaceSettings) {
                    const selectWorkspace = false;
                    dispatch(listPageActions.loadCopyListPageWorkspaceTemplate(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, selectWorkspace));
                } else {
                    dispatch(listPageActions.digestCopyListPageWorkspace(newWorkspaceAccessType, newWorkspaceEditRights, newWorkspaceTemplateGuid, doCreate, originalWorkspaceName, workspaceColourthemeGuid, workspaceCustomColourTheme));
                }
            },
            movePlan: (uuid, accessType, editRights) => {
                const workspaceData = {
                    'workspace_accesstype': accessType,
                    'workspace_editrights': editRights
                };
                dispatch(listPageActions.moveListPageWorkspace(uuid, workspaceData));
            }
        }
    };
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {
    return {
        ...propsFromDispatch,
        ...ownProps,
        ...omit(propsFromState, ['getState']),
        promptActions: {
            promptDeletePlan: (workspaceGuid) =>
                propsFromDispatch.promptActions.promptDeletePlan(workspaceGuid, propsFromState.getState),
            promptRenamePlan: (workspaceGuid, newDescription) =>
                propsFromDispatch.promptActions.promptRenamePlan(workspaceGuid, newDescription, propsFromState.getState),
            promptSelectPlan: (workspaceGuid) => {
                propsFromDispatch.promptActions.promptSelectPlan(workspaceGuid, propsFromState.getState);
            },
            promptMovePlan: (workspaceGuid, accessType, editRights) => {
                propsFromDispatch.promptActions.promptMovePlan(workspaceGuid, accessType, editRights, propsFromState.getState);
            }
        },
        actions: {
            ...propsFromDispatch.actions,
            copyPlan: (newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName) => {
                propsFromDispatch.actions.copyPlan(newWorkspaceTemplateGuid, newWorkspaceAccessType, newWorkspaceEditRights, originalWorkspaceName, propsFromState.getState);
            },
            digestCreatePlan: (defaultWorkspaceGuid) => {
                propsFromDispatch.actions.digestCreatePlan(defaultWorkspaceGuid, propsFromState.privatePlansColumn.plans, propsFromState.staticMessages.newPlanLabel);
            }
        }
    };
};

const ConnectedListPageManageMyWorkspacesWindow = connect(
    mapStateToProps,
    mapDispatchToProps,
    mergeProps
)(ManagePlansWindow);

export { ConnectedListPageManageMyWorkspacesWindow };