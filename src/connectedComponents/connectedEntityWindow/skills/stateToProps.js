import { TALENT_PROFILE_MARK_DELETED_MESSAGE, TALENT_PROFILE_CANCEL_DELETION_MESSAGE } from '../../../constants/talentProfileConsts';
import { getUIResourceSkillSelector, checkSkillPendingApproval } from '../../../selectors/resourceSkillsSelectors';
import { getSkillFieldInfoSelector, getSkillInfoSelector, getSkillFormConfigSelector } from '../../../selectors/skillStructureSelectors';
import { getSkillsStaticMessages } from '../../../selectors/editSkillsWindowSelectors';
import { createSelector } from 'reselect';

const skillsLabelsSelector = createSelector(
    staticSkillMessages => staticSkillMessages.markDeletedMessage,
    staticSkillMessages => staticSkillMessages.cancelDeletionMessage,
    (markDeletedMessage = TALENT_PROFILE_MARK_DELETED_MESSAGE, cancelDeletionMessage = TALENT_PROFILE_CANCEL_DELETION_MESSAGE) => {
        return { markDeletedMessage, cancelDeletionMessage };
    }
);
export const skillsMapStateToProps = (state) => {
    const getFieldsConfig = getSkillFormConfigSelector(state);
    const getUISkill = getUIResourceSkillSelector(state);
    const getSkillFieldInfo = getSkillFieldInfoSelector(state);
    const getSkillInfo = getSkillInfoSelector(state);
    const staticSkillMessages = getSkillsStaticMessages(state);
    const skillLabels = skillsLabelsSelector(staticSkillMessages);

    return {
        skillLabels,
        getFieldsConfig,
        getUISkill,
        getSkillFieldInfo,
        getSkillInfo,
        getIsSkillPending: (resourceId, skillId) => checkSkillPendingApproval(state, resourceId, skillId)
    };
};
