import { connect } from 'react-redux';
import NotificationsRoot from '../../lib/notifications/index';
import { updatePageParams } from '../../actions/pageStateActions';
import { loadNotificationHistory, markAllNotificationRead, notificationsLoadData, onChangeNotificationReadUnreadStatus } from '../../actions/notificationsPageActions';
import { getNotificationsActiveTabSelector, getStaticMessagesBySubSectionSelector, getNotificationTemplate, getNotificationByIdSelector, getNotificationsHistory, getNotificationsHistoryData } from '../../selectors/notificationsPageSelectors';
import { getEntityWindowCollectionAliasForNotification, getEntityWindowOperationForNotification, getNotificationListItemContextMenuProps, getNotificationQueryParams, notificationHistoryListActionCreationFunc, getNotificationsTabs, isOldNotificationPayloadStructure, getNotificationEntityName, isBookingSeriesNotificationStructure, getAllNotificationsTabs } from '../../utils/notificationsPageUtils';
import { NOTIFICATIONS_PAGE_ALIAS, DUMMY_GUID, NOTIFICATION_SETTINGS_USER_PAGE, NOTIFICATION_EVENTS } from '../../constants/notificationsPageConsts';
import { getApplicationAccessSelector } from '../../selectors/userEntityAccessSelectors';
import store from '../../store/configureStore';
import { isEmpty } from 'lodash';
import { ENTITY_WINDOW, NOTIFICATION_DATA_ACTIONS } from '../../actions/actionTypes';
import { ENTITY_WINDOW_MODULES } from '../../constants';
import { getTranslationsSectionSelector } from '../../selectors/internationalizationSelectors';
import { getNotificationsSettingsDispatches, getNotificationsSettingsState } from '../../utils/notificationsSettingsUtils';
import { ENTITY_WINDOW_OPERATIONS } from '../../constants/entityWindowConsts';
import { getFeatureFlagSelector } from '../../selectors/featureManagementSelectors';
import { FEATURE_FLAGS } from '../../constants/globalConsts';


const mapStateToProps = (state) => {
    const hasManagerRequest = getFeatureFlagSelector(FEATURE_FLAGS.SKILL_APPROVAL)(state);
    const notificationStaticMessagesTabs = getStaticMessagesBySubSectionSelector(state, getAllNotificationsTabs(state));
    const activeTab = getNotificationsActiveTabSelector(state)(notificationStaticMessagesTabs);
    const mappedNotificationsTabs = getNotificationsTabs(notificationStaticMessagesTabs);
    const activeTabIndex = mappedNotificationsTabs.findIndex(item => item.tabDefaultName.toLowerCase() == activeTab.toLowerCase());
    const notificationsTabConfig = {
        defaultActive: activeTabIndex.toString(),
        tabDetails: mappedNotificationsTabs
    };
    const staticMessages = getStaticMessagesBySubSectionSelector(state, 'notificationEvents');
    const { informationMessage, markAllReadLabel, loadMoreButtonLabel } = getStaticMessagesBySubSectionSelector(state, 'notificationHistoryPageHeader');
    const notificationActionsStaticMessages = getStaticMessagesBySubSectionSelector(state, 'notificationActions');
    const commonStaticMessages = getTranslationsSectionSelector(state, 'common');
    const notificationHistory = getNotificationsHistory(state) || {};
    const notificationEvents = getNotificationTemplate(state)(staticMessages);
    const userEntityAccessWrapper = getApplicationAccessSelector(state);
    const isLoading = notificationHistory.isLoading || false;
    const showMoreProps = {
        displayShowMore: notificationHistory.rowCount > notificationEvents.length,
        isLoadingMoreInProgress: notificationHistory.isLoadingMoreInProgress
    };

    const getHeaderProps = () => {
        return {
            informationMessage,
            buttonConfig: {
                label: markAllReadLabel,
                disableMarkAllRead: notificationHistory.isAllNotificationsRead || false
            }
        };
    };

    const contextualMenuProps = {
        notificationHistory,
        userEntityAccessWrapper,
        notificationActionsStaticMessages,
        moreOptionsButtonLabel: commonStaticMessages.moreOptionsButtonLabel
    };

    const notificationSettings = getNotificationsSettingsState(state, NOTIFICATION_SETTINGS_USER_PAGE);

    return {
        hasManagerRequest,
        notificationHistory,
        notificationsTabConfig,
        notificationEvents,
        headerProps: getHeaderProps(),
        loadMoreButtonLabel,
        getItemsContextualMenuProps: (item, index) => getNotificationListItemContextMenuProps(item, index, contextualMenuProps),
        noResultsText: commonStaticMessages.noResultsMessage,
        showMoreProps,
        isLoading,
        notificationStaticMessagesTabs,
        notificationSettings
    };
};

const dispatchEntityWindowOpenAction = (notificationId, dispatch) => {
    const state = store.getState();
    const notification = getNotificationByIdSelector(state)(notificationId);

    if (isEmpty(notification)) {
        return;
    }

    const { notificationDescription = {}, notificationPreferenceKey = '', notificationRead = false } = notification;
    const oldNotificactionPayloadStructure = isOldNotificationPayloadStructure(notification);
    const isBookingSeriesNotification = isBookingSeriesNotificationStructure(notification);
    const entityName = oldNotificactionPayloadStructure ? notificationDescription.EntityName : notificationDescription.entityName;
    const actualEntityName = getNotificationEntityName(entityName);
    const entityId = oldNotificactionPayloadStructure ? notificationDescription.EntityGuid : notificationDescription[`${actualEntityName}_guid`];
    const tableName = actualEntityName;
    const userEntityAccessWrapper = getApplicationAccessSelector(state);
    const operation = getEntityWindowOperationForNotification(notificationPreferenceKey, userEntityAccessWrapper, tableName, entityId);
    const collectionAlias = getEntityWindowCollectionAliasForNotification(tableName);
    const isRecommendationNotification = notificationPreferenceKey.toLowerCase() === NOTIFICATION_EVENTS.RESOURCE_SKILL_RECOMMENDATION.toLowerCase();
    const recommendationEnabled = getFeatureFlagSelector(FEATURE_FLAGS.RECOMMENDATIONS)(state);

    if (isRecommendationNotification) {
        //NOTE: DON'T DO ANYTHING IF IT IS READ ACCESS
        if (operation === ENTITY_WINDOW_OPERATIONS.EDIT && recommendationEnabled) {
            dispatch({
                type: NOTIFICATION_DATA_ACTIONS.LOAD_RECOMMENDATION_NOTIFICATION,
                payload: {
                    entityId
                }
            });
        }
    } else if (isBookingSeriesNotification) {
        dispatch({
            type: NOTIFICATION_DATA_ACTIONS.LOAD_FIRST_BOOKING_IN_THE_SERIES,
            payload: {
                bookingseries_guid: notificationDescription.bookingseries_guid,
                tableName,
                collectionAlias,
                operation: ENTITY_WINDOW_OPERATIONS.READ
            }
        });
    } else if (operation && entityId && entityId !== DUMMY_GUID) {
        dispatch({
            type: `${ENTITY_WINDOW.OPEN}_${ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL}`,
            payload: {
                tableName,
                collectionAlias,
                entity: {},
                operation,
                entityId,
                lazyLoadEntityData: true,
                moduleName: ENTITY_WINDOW_MODULES.NOTIFICATION_PAGE_MODAL,
                tableNames: [
                    tableName
                ]
            }
        });
    }

    if (!notificationRead) {
        dispatch(onChangeNotificationReadUnreadStatus(notificationId, true));
    }
};

const dispatchLoadMoreNotificationsAction = (dispatch) => {
    const state = store.getState();
    const existingNotifications = getNotificationsHistoryData(state);
    const queryParams = getNotificationQueryParams(existingNotifications);
    dispatch(loadNotificationHistory({ queryParams, isLoadingMoreInProgress: true }));

};

const dispatchContextualNotificationsAction = (action, dispatch) => {
    const state = store.getState();
    notificationHistoryListActionCreationFunc(action, dispatch, state);
};

const matDispatchToProps = (dispatch) => {

    return {
        loadNotificationData: (payload) => {
            dispatch(notificationsLoadData(payload));
        },
        updatePageParams: (payload) => {
            dispatch(updatePageParams(NOTIFICATIONS_PAGE_ALIAS, payload));
        },
        onMarkAllReadButtonClick: (preferenceKeys) => {
            dispatch(markAllNotificationRead(preferenceKeys));
        },
        loadMoreButtonClick: () => dispatchLoadMoreNotificationsAction(dispatch),
        onNotificationClick: (notificationId) => dispatchEntityWindowOpenAction(notificationId, dispatch),
        dispatchOnAction: (action) => dispatchContextualNotificationsAction(action, dispatch),
        ...getNotificationsSettingsDispatches(dispatch)
    };
};

const mergeProps = (propsFromState, propsFromDispatch, ownProps) => {

    const { onNotificationSettingSave, onFieldDataChange, onFormReset, loadTableDatas } = propsFromDispatch;

    const notificationSettingsData = {
        ...ownProps,
        ...propsFromState.notificationSettings,
        onNotificationSettingSave,
        onFieldDataChange,
        onFormReset,
        loadTableDatas,
        alias: NOTIFICATION_SETTINGS_USER_PAGE
    };

    return {
        ...propsFromState,
        ...propsFromDispatch,
        ...ownProps,
        onAction: (action) => {
            propsFromDispatch.dispatchOnAction(action);
        },
        notificationSettingsData
    };
};

const ConnectedNotifications = connect(
    mapStateToProps,
    matDispatchToProps,
    mergeProps
)(NotificationsRoot);

export default ConnectedNotifications;