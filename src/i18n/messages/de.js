﻿// Any string wrapped in ${ } or { } is a placeholder string and should not be translated literally.
export default {
    //common validation errors
    emailValidationError: 'Bitte geben Sie eine gültige E-Mail-Adresse an.',
    //commonly used text
    fieldValue: 'Feldwert',
    confirmation: 'Bestätigung',
    invalid: 'Ungültig',
    current: '{index} (Aktuell)',
    dismissText: 'Ablehnen',
    mandatoryValidation: '* Pflichtfeld',
    //common component
    selectionListLicenseWarning: '{contactUs}, um Ihr Limit zu erhöhen.',
    selectionListLicenseError: '{contactUs}, um Ihr Limit zu erhöhen.',
    selectionListDiaryCustomMsg: '{activeCount} von {licensedCount} verwendeten Terminkalender',
    selectionListSkillsCustomMsg: '{activeCount} von {licensedCount} Kompetenzen verwendet',
    commonComponentConfirmationModalConsequence: 'Ich verstehe die Auswirkungen dieser Änderung.',
    commonComponentConfirmationModalEmptyMsg: ' ',
    noOptionsAvailable: 'Keine Optionen verfügbar',
    actionBarSaveButtonLabel: 'Änderungen speichern',
    actionBarConfirmChangesLabel: 'Änderungen bestätigen',
    actionBarCancelButtonLabel: 'Abbrechen',
    actionBarDiscardChangesLabel: 'Änderungen verwerfen',
    actionBarLeavePageMessage: 'Möchten Sie die Seite wirklich verlassen?',
    actionBarUnsavedChanges: 'Auf dieser Seite gibt es noch nicht gespeicherte Änderungen. Trotzdem die Seite verlassen?',
    actionBarFormErrorMessage: 'In diesem Formular sind Fehler',
    deleteModalCancelButtonLabel: 'Nein, Sicherheitsprofil übernehmen',
    deleteModalSubmitButtonLabel: 'Ja, Sicherheitsprofil löschen',
    securityProfileDeleteModalTitle: 'Sicherheitsprofil {profileName} löschen?',
    securityProfileDeleteModalText: 'Möchten Sie dieses Sicherheitsprofil dauerhaft löschen?',
    cannotBeUndoneText: 'Das kann nicht rückgängig gemacht werden.',
    addAnItemText: 'Element hinzufügen',
    selectionListSortText: 'Von A - Z sortieren',
    deleteSectionConfirmation: 'Möchten Sie {sectionName} wirklich löschen?',
    deleteSectionConfirmationTitle: 'Bestätigung Abschnitt löschen',
    CreateContextMenuLabel: 'Erstellen',
    RenameContextMenuLabel: 'Umbenennen',
    DuplicateContextMenuLabel: 'Duplizieren',
    DeleteContextMenuLabel: 'Löschen',
    DUPLICATE_USING_APIContextMenuLabel: 'DUPLICATE_USING_API',
    toasterDefaultSuccessMessage: 'Änderungen gespeichert',
    toasterDefaultErrorMessage: 'Speichern fehlgeschlagen, bitte erneut versuchen',
    toasterDefaultWarningMessage: 'Einige Änderungen konnten nicht gespeichert werden, bitte aktualisieren und erneut versuchen',
    toasterDefaultUnsavedChangesMessage: 'Änderungen konnten nicht gespeichert werden, bitte Fehler korrigieren und erneut versuchen',
    actionCannotBeUndoneMessage: 'Diese Aktion kann nicht rückgängig gemacht werden.',
    noDataAvailableText: 'Keine Daten',
    licensedCountLabel: '{activeCount} von {licensedCount} verwendeten ',
    defaultLimitationInfo: 'Die Limitierung basiert auf Ihrer aktuellen Lizenzvereinbarung.',
    contactUsText: 'Kontaktieren Sie uns',
    importText: 'Importieren',
    noResultsFound: 'Keine Ergebnisse gefunden',
    reorderText: 'Neu ordnen',
    editText: 'Bearbeiten',
    resetText: 'Zurücksetzen',
    searchText: 'Suchen',
    nameText: 'name',

    //skill filter cascader component
    skillFilterResetButtonDisabledMessage: 'Zurücksetzen nicht möglich: Keine Kompetenzen ausgewählt.',
    skillFilterApplyButtonDisabledMessage: 'Anwenden nicht möglich: Keine Kompetenzen ausgewählt.',
    skillFilterApplyButtonDisabledForMaxCountMessage: 'Anwenden nicht möglich: Maximal {maxSkillSelection} Auswahlen möglich.',
    //Import data
    entityImportSelectValuePlaceholder: 'Wert wählen',
    ImportSuccessfulToasterMessage: 'Import abgeschlossen',
    ImportUnsuccessfulToasterMessage: 'Import fehlgeschlagen',
    operationLogChangesSaved: 'Gespeicherte Änderungen des Vorgangsprotokolls',
    //userManagement Messages
    userManagementLicenseWarning: 'Sie sind kurz vor dem für Ihre Lizenz zulässigen Limit. Setzen Sie bestehende Benutzer auf \'Inaktiv\' oder {contactUs}, um das Limit Ihrer Lizenzvereinbarung zu erhöhen.',
    userManagementLicenseError: 'Sie haben das Limit Ihrer Lizenzvereinbarung erreicht. Setzen Sie bestehende Benutzer auf \'Inaktiv\' oder {contactUs}, um das Limit Ihrer Lizenzvereinbarung zu erhöhen.',
    userManagementLicenseErrorHeading: 'können maximal {number} aktive Benutzer angemeldet sein.',
    userManagementLicenseActiveUsers: 'Sie haben {number} Benutzer auf \'Aktiv.',
    userManagementLicenseUserLimit: 'Ihre aktuelle Lizenz umfasst bis zu {number} aktive Benutzer.',
    userManagementLicenseContactUS: 'Setzen Sie einige Benutzer auf \'Inaktiv\' oder {contactUs}, um Ihr Limit zu erhöhen.',
    userManagementInactiveUserHeading: '{number} Benutzer auf \'Inaktiv\' setzen?',
    userManagementSetInactiveUserPopup: 'Möchten Sie {number} Benutzer auf \'Inaktiv\' setzen?',
    userManagementUnAssignBooks: 'Diese können sich nicht mehr einloggen und werden aus aktuellen Buchungen herausgenommen. Sie können keinen zukünftigen Buchungen zugeordnet werden.',
    userManagementReportingArchive: 'Wenn sie früheren Buchungen zugeordnet waren, werden diese Daten zu Berichts- / Archivierungszwecken gespeichert.',
    userManagementInactiveConfirmation: '{number} Benutzer auf \'Inaktiv\' setzen und aus den aktuellen Buchungen herausnehmen',
    userManagementInactivePopupPrimaryButton: 'Benutzer deaktivieren',
    userManagementInactivePopupSecondaryButton: 'Abbrechen',
    //for confirmation modal popup need to change later
    userManagementDeletePopupMessage: 'Ausgewählte Benutzer löschen?',
    userManagementDeleteWarningMessage: 'Möchten Sie {number} Benutzer dauerhaft löschen?',
    userManagementDeleteBookingsWarning: 'Ihre gesamten Daten werden aus dem System gelöscht. Frühere ihnen zugewiesene Buchungen werden aufgehoben und können sich auf alle diesbezüglichen Berichte auswirken.',
    userManagementDeleteHistoricData: 'Setzen Sie diesen Benutzerstatus einfach auf \'Inaktiv\', wenn Sie die bisherigen Daten erhalten möchten.',
    userManagementDeleteConfirmation: 'Ausgewählte Benutzer löschen und sie aus allen Buchungen oder Berichten im System entfernen',
    userManagementDeletePrimaryButton: 'Benutzer löschen',
    userManagementDeleteSecondaryButton: 'Ausgewählte Benutzer nicht löschen',
    userManagementDescription: 'Benutzer hier hinzufügen, bearbeiten oder löschen. Zu den Benutzerprofilen wechseln, um weitere Änderungen vorzunehmen.',
    userManagementSubTitle: 'Aktive Benutzer',
    userManagementNotifyUserCountDescription: '{activeUser} Ihrer {totalUser} Benutzer sind aktiv. Ihre Lizenz umfasst bis zu {licencedActiveUser} aktive Benutzer.',
    userManagementAddUserButtonFroCommandBar: 'Benutzer hinzufügen',
    userManagementToNavigateImportFeatureMessage: 'Umfassendere Aktualisierungen können mit der {Import} Funktion durchgeführt werden.',
    userManagementFormLabelsSecurityProfile: 'Sicherheitsprofil',
    userManagementFormLabelsUserStatusDescription: 'Das Setzen des Benutzers auf aktiv ermöglicht es ihm, sich in sein Konto einzuloggen.',
    userManagementFormLabelsName: 'Name',
    userManagementFormLabelsEmail: 'E-Mail',
    userManagementFormLabelsUserStatus: 'Aktiver Nutzer',
    userManagementFormLabelsUserLastLogin: 'Letzte Anmeldung',
    userManagementValidationFirstName: 'Bitte Vornamen eingeben',
    userManagementValidationLastName: 'Bitte Nachnamen eingeben',
    userManagementValidationEmailReqd: 'Bitte Ihre E-Mail eingeben',
    userManagementValidationEmailMsg: 'Die Eingabe ist keine gültige E-Mail',
    userManagementValidationSecurityProfile: 'Sicherheitsprofil muss ausgewählt werden',
    userManagementTooltipGoToProfile: 'Zum Profil gehen',
    userManagementTooltipResetPass: 'Passwort zurücksetzen',
    userManagementTooltipResendEmail: 'Einladungs-E-Mail senden',
    userManagementTooltipMarkDelete: 'Zum Löschen  kennzeichnen',
    userManagementFirstNamePlaceholder: 'Vorname',
    userManagementLastNamePlaceholder: 'Nachname',
    userManagementEmailPlaceholder: 'E-Mail eingeben',
    userManagementSecurityProfilePlaceholder: 'Ein Sicherheitsprofil auswählen',
    userManagementTitle: 'Benutzerverwaltung',
    userManagementStatusActive: 'Aktiv',
    userManagementStatusInactive: 'Inaktiv',
    userManagementDuplicateEmail: 'Bitte achten Sie darauf, dass die Benutzer individuelle E-Mail-IDs haben.',
    userManagementUserAccess: 'Unzureichende Berechtigungen zum Bearbeiten dieser Ressourcen',
    //comopanyInfo Messages
    companyInfoNameLabel: 'Name des Unternehmens',
    companyInfoLogoLabel: 'Logo',
    companyUploadLogoLabel: 'Logo hochladen',
    companyInfoLogoThumbnailLabel: 'Logo-Vorschaubild',
    companyUploadLogoThumbnailLabel: 'Logo-Vorschaubild hochladen',
    companyInfoSupportPhoneNumberLabel: 'Telefonnummer Kundendienst',
    companyInfoApplicationLanguage: 'Anwendungssprache',
    companyInfoSupportEmailLabel: 'E-Mail Kundendienst',
    companyInfoInstanceOwnerLabel: 'Eigentümer der Anwendung',
    companyInfoLogoControlMessage: 'Das Logo sollte eine Größe von 80 px X 24 px und ein .png Format haben',
    companyInfoUploadControlText: 'Klicken oder ziehen Sie die Datei zum Hochladen in diesen Bereich',
    companyInfoLogoThumbnailControlMessage: 'Das Logo-Vorschaubild sollte eine Größe von 24 px X 24 px und ein .png Format haben',
    companyInfoPhoneNumberErrorMessage: 'Geben Sie bitte eine gültige Telefonnummer an',
    companyInfoEmailErrorMessage: 'Geben Sie bitte eine gültige E-Mail-Adresse an',
    companyInfoLogoErrorMessage: 'Laden Sie bitte ein gültiges Logo hoch',
    companyInfoLogoThumbnailErrorMessage: 'Laden Sie bitte ein gültiges Logo-Vorschaubild hoch',
    companyInfoLookUpNoMatches: 'Keine Übereinstimmungen',
    companyInfoSelectValuePlaceholder: 'Wert auswählen',
    companyInfoHelpSupportMessage: 'Bitte kontaktieren Sie zur Unterstützung',
    companyInfoVersion: 'Version {version}',
    companyInfoTitle: 'Informationen zum Unternehmen',
    companyInfoFileUploadFailed: 'Hochladen der Datei {fileName} fehlgeschlagen.',
    //Currency Messages
    currencyHeaderText: 'Währung',
    currencyDefaultMessage: 'Systemwährung',
    currencyDescription: 'Wählen Sie die in Ihrer Anwendung zu verwendende Währung.',
    baseCurrencyLabel: 'Basiswährung',
    //diary calendar messages
    diaryCalendarHeading: 'Kalender',
    diaryCalendarSubHeading: 'Personalisieren Sie Ihren Jahreskalender durch die Festlegung von standardmäßigen Arbeitszeiten und arbeitsfreien Tagen wie Feiertage und Betriebsferien.',
    diaryCalendarCustomDayTitle: 'Benutzerdefinierten Tag hinzufügen',
    diaryCalendarCustomPeriodTitle: 'Benutzerdefinierten Zeitraum hinzufügen',
    diaryCalendarSelectDateRangeRequiredMsg: 'Bitte wählen Sie einen Datumsbereich',
    diaryCalendarCustomPeriodRequiredMsg: 'Name für benutzerdefinierten Zeitraum erforderlich',
    diaryCalendarWorkPatternRequiredMsg: 'Bitte wählen Sie die Arbeitszeiten',
    diaryCalendarSelectDateRequiredMsg: 'Bitte wählen Sie ein Datum',
    diaryCalendarCustomDayRequiredMsg: 'Name für benutzerdefinierten Tag erforderlich',
    diaryCalendarDayTypeRequiredMsg: 'Bitte wählen Sie einen Tagtyp',
    diaryCalendarReplaceCustomPeriodMsg: 'Möchten Sie „{overlappingPeriods}“ durch einen neuen benutzerdefinierten Zeitraum ersetzen?',
    diaryCalendarReplaceCustomDayMsg: 'Möchten Sie „{overlappingDayName}“ durch einen neuen benutzerdefinierten Tag ersetzen?',
    //for confirmation modal popup need to change later
    diaryCalendarSaveHistoricalDataAlert: 'Sind Sie sicher, dass Sie die Änderungen an den bisherigen Daten speichern möchten?',
    diaryCalendarSavePastDayChangesMsg: 'Ihre Änderungen können sich auf die Berechnungen für frühere Buchungen auswirken. Die Änderungen können nicht rückgängig gemacht werden.',
    diaryCalendarStandardWorkPatternLabel: 'Standard-Arbeitszeiten',
    diaryCalendarCustomDaysGridHeading: 'Benutzerdefinierte Tage',
    diaryCalendarCustomPeriodsGridHeading: 'Benutzerdefinierte Zeiträume',
    diaryCalendarCustomDaysAddBtn: 'Benutzerdefinierten Tag hinzufügen',
    diaryCalendarCustomPeriodsAddBtn: 'Benutzerdefinierten Zeitraum hinzufügen',
    diaryCalendarCustomDayNamePlaceholder: 'Name benutzerdefinierter Tag',
    diaryCalendarCustomPeriodNamePlaceholder: 'Name benutzerdefinierter Zeitraum',
    diaryCalendarCustomDayTypePlaceholder: 'Tagestyp benutzerdefinierter Tag',
    diaryCalendarCustomWorkPatternPlaceholder: 'Arbeitszeiten benutzerdefinierter Zeitraum',
    diaryCalendarCustomDayIsInUseMessage: 'Überschneidet sich mit einem existierenden benutzerdefinierten Tag',
    diaryCalendarCustomPeriodIsInUseMessage: 'Überschneidet sich mit einem existierenden benutzerdefinierten Zeitraum',
    diaryCalendarCustomGridDateRequiredMessage: 'Bitte ein Datum angeben',
    diaryCalendarCustomGridRangeRequiredMessage: 'Bitte einen Datumsbereich angeben',
    diaryCalendarCustomGridNameRequiredMessage: 'Bitte einen Namen angeben',
    diaryCalendarCustomGridDayTypesRequiredMessage: 'Bitte einen Tagestyp angeben',
    diaryCalendarCustomGridWorkPatternsRequiredMessage: 'Bitte Arbeitszeiten angeben',
    diaryCalendarTotalHourSumErrorMessage: 'Die Gesamtstundenzahl muss kleiner oder gleich 24 Stunden sein',
    diaryCalendarHoursAginstDurationError: 'Anfangs- und Endzeit müssen in der Anzahl der zugeordneten Stunden enthalten sein',
    diaryCalendarCustomDayPlaceholder: 'Bitte benutzerdefinierten Tagesnamen eingeben',
    diaryCalendarDayTypePlaceholder: 'Tagestyp auswählen',
    diaryCalendarCustomPeriodPlaceholder: 'Bitte benutzerdefinierten Zeitraum eingeben',
    diaryCalendarWorkPatternPlaceholder: 'Arbeitszeiten auswählen',
    diaryCalendarRangeDateLabel: 'Start- und Enddatum',
    diaryCalendarPatternLabel: 'Vorlage',
    diaryNameRequiredMessage: 'Bitte einen Kalendernamen angeben',
    uniqueDiaryNameMessage: 'Bitte einen individuellen Kalendernamen eingeben',
    diaryWarningTitle: 'Terminkalender kann nicht gelöscht werden',
    diaryWarningMessage: '„{diaryNames}“ Kalender kann nicht gelöscht werden. Dieser Kalender ist einer Ressource zugewiesen.',
    //button labels
    confirmButtonLabel: 'Bestätigen',
    cancelButtonLabel: 'Abbrechen',
    saveButtonLabel: 'Speichern',
    okButtonLabel: 'OK',
    //table headings
    diaryCalendarCustomDaysDateTableHeading: 'Datum',
    //same value need to check
    diaryCalendarCustomDaysNameTableHeading: 'Name',
    diaryCalendarCustomPeriodsRangeTableHeading: 'Start- und Enddatum',
    diaryCalendarCustomPeriodsWorkPatternTableHeading: 'Arbeitsmuster',
    //work pattern
    workPatternHeading: 'Arbeitsmuster',
    workPatternCommandBarFieldName: 'Arbeitsmusternamen bearbeiten',
    workPatternSubHeading: 'Ein Arbeitsmuster kann durch die Auswahl eines bestimmten Startdatums und individueller Tagestypen erstellt werden. Hier können Sie Arbeitsmuster hinzufügen, bearbeiten oder löschen.',
    //for confirmation modal popup need to change later
    workPatternSaveInUseAlert: 'Möchten Sie die Änderungen für das verwendete Arbeitsmuster wirklich speichern?',
    workPatternSaveInUseChangesMessage: 'Ihre Änderungen können sich auf den Terminkalender auswirken. Das kann nicht rückgängig gemacht werden.',
    workPatternAddButton: 'Einen Tag hinzufügen',
    workPatternReqValidation: 'Bitte wählen Sie einen Tagestyp',
    workPatternUniqueMsg: 'Bitte geben Sie eine einzigartige Bezeichnung für das Arbeitsmuster ein',
    workPatternReqdMsg: 'Bitte geben Sie eine Bezeichnung für das Arbeitsmuster ein',
    workPatternStartDayLabel: 'Starttag',
    workPatternDayTableHead: 'Tag',
    workPatternWarningMessage: 'Zum Löschen aus den Terminkalendern entfernen.',
    workPatternWarningTitle: 'Sie können das Arbeitsmuster {selection} nicht löschen, da es verwendet wird.',
    //Day Types
    dayTypePageHeading: 'Tagestypen',
    dayTypeHeading: 'Tagestyp',
    dayTypeCommandBarFieldName: 'Tagestypnamen bearbeiten',
    dayTypeSubHeading: 'Richten Sie den Tagestyp (Arbeitstag oder arbeitsfreier Tag) so ein, dass er die regulären Arbeitszeiten Ihres Unternehmens abbildet.',
    //for confirmation modal popup need to change later
    dayTypeSaveInUseAlert: 'Möchten Sie die Änderungen für den verwendeten Tagestyp wirklich speichern?',
    dayTypeSaveInUseChangesMessage: 'Ihre Änderungen können sich auf den Terminkalender auswirken. Das kann nicht rückgängig gemacht werden.',
    dayTypeWorkingHoursRequiredMessage: 'Bitte geben Sie die Arbeitszeit ein',
    dayTypeContingencyTimeRequiredMessage: 'Bitte geben Sie eine Eventualzeit ein',
    dayTypeWorkingHoursCannotZeroMessage: 'Arbeitszeit kann nicht 00:00 sein',
    dayTypeWorkDayLabel: 'Arbeitstag',
    dayTypeNonWorkDayLabel: 'Arbeitsfreier Tag',
    dayTypeWorkTimeLabel: 'Arbeitszeit',
    dayTypeContingencyTimeHours: 'Eventualzeit',
    dayTypeTitleRequiredMessage: 'Bitte geben Sie die Bezeichnung des Tagestyps ein',
    dayTypeTitleUniqueMessage: 'Bitte geben Sie einen einzigartigen Tagestyp ein',
    dayTypeWarningTitle: 'Sie können den Tagestyp {selection} nicht löschen, da er verwendet wird.',
    dayTypesWarningMessage: 'Zum Löschen aus den Arbeitsmustern entfernen.',
    //entity import
    entityImportPageHeader: 'Daten importieren',
    entityImportPageSummaryText: 'Importieren Sie Daten in Ihre Anwendung in zwei einfachen Schritten:<ol><li>Laden Sie die entsprechende Vorlage herunter und geben Sie die Daten in die Vorlage ein. Achten Sie darauf, dass alle Pflichtfelder ausgefüllt sind.</li><li>Laden Sie die von Ihnen ausgefüllte Vorlage hoch</li></ol>Die von Ihnen in die Vorlage eingegeben Daten werden in die Anwendung importiert.',
    entityImportDownloadTemplatesHeader: 'Eine Vorlage herunterladen',
    entityImportDownloadTemplatesNumber: '1',
    entityImportUploadDataHeader: 'Laden Sie die von Ihnen ausgefüllte Vorlage hoch',
    entityImportUploadDataNumber: '2',
    entityImportJobLabel: 'Aufträge',
    entityImportClientLabel: 'Kunden',
    entityImportResourceLabel: 'Ressourcen',
    entityImportSkillLabel: 'Qualifikationen',
    entityImportUploadControlText: 'Klicken oder ziehen Sie eine Datei zum Hochladen in diesen Bereich',
    entityImportSelectUploadFileLabel: 'Markieren Sie eine hochzuladende Datei',
    entityImportImportSuccessful: 'Import abgeschlossen',
    entityImportImportUnsuccessful: 'Import fehlgeschlagen',
    entityImportTemplateDownloadFailed: 'Vorlage kann nicht heruntergeladen werden',
    entityImportUploadControlError: 'Markieren Sie bitte die hochzuladende Datei',
    entityImportUploadDropDownError: 'Bitte wählen Sie den Typ',
    entityImportUploadDropDownPlaceholder: 'Wählen Sie einen Datentyp',
    entityImportFileUploadFailed: 'Hochladen der Datei fehlgeschlagen.',
    entityImportTypeOfData: 'Datentyp',
    entityImportUploadAndVerify: 'Hochladen und verifizieren',
    entityImportCancelBtn: 'Abbrechen',
    entityImportConfirmImport: 'Import bestätigen',
    entityImportFormValidateMsg: 'Klicken, um den Import vorab zu überprüfen',
    entityImportmailSubject: 'Limit der Cloud-Lizenz speichern',
    entityImportUploadProcessed: '{EntriesProcessedCnt} {currentEntityType}-Einträge verarbeitet.',
    entityImportTemplateFileName: 'EntitätImportTemplate',
    processFormErrorCorrectionText: 'Zum Abschließen des Imports bitte die folgenden Fehler korrigieren.',
    processFormAlternateOption: 'Alternativ können Sie diese auch löschen oder die Aktualisierung abbrechen, die Microsoft Excel-Datei aktualisieren und erneut hochladen.',
    processFormRowNoFromExcelMsg: 'Die entsprechenden Zeilennummern aus der Microsoft Excel-Datei werden unten angezeigt.',
    processFormRequiredClientNameField: 'Das Feld Kundenname ist ein Pflichtfeld.',
    processFormRequiredClientCodeField: 'Das Feld Kundencode ist ein Pflichtfeld.',
    processFormRequiredJobTitleField: 'Das Feld Auftragsbezeichnung ist ein Pflichtfeld.',
    processFormRequiredSkillNameField: 'Das Feld Qualifikationen ist ein Pflichtfeld.',
    processFormRequiredSkillInfoField: 'Das Feld Qualifikations-Info ist ein Pflichtfeld.',
    processFormRequiredSkillSectionField: 'Das Feld Kompetenzbereich ist ein Pflichtfeld.',
    processFormRequiredFirstNameField: 'Das Feld Vornamen ist ein Pflichtfeld.',
    processFormRequiredLastNameField: 'Das Feld Nachnamen ist ein Pflichtfeld.',
    processFormRequiredEmailField: 'Das Feld E-Mail ist ein Pflichtfeld.',
    processFormProcessed: 'verarbeitet.',
    processFormProcessedWithError: 'verarbeitet mit Fehlern.',
    processFormProcessedWithNoError: 'verarbeitet ohne Fehler.',
    processFormWithError: 'mit Fehlern.',
    processFormWithNoError: 'ohne Fehler.',
    processFormLicenseUserContError: 'Diese {currentEntityType}-Einträge können nicht importiert werden.',
    processFormLicenseUserDivLine1: 'Ihre Lizenz beinhaltet bis zu <b>{allowedActiveUsersCount}</b> aktive Nutzer.',
    processFormLicenseUserDivLine2_1: 'Dieser Import führt zu {totalRecordsProcessed} aktiven Nutzern.',
    processFormContactUs: '{contactUs}',
    processFormLicenseUserDivLine2_2: 'um Ihr Limit zu erhöhen.',
    processFormLicenseUserContErrorAlert: 'Etwas ist schief gelaufen. Der Import kann nicht verarbeitet werden',
    processFormContactUsText: 'Kontaktieren Sie uns',
    //color scheme
    colourSchemeHeader: 'Farbthema',
    colourSchemeFieldName: 'Farbdesignnamen bearbeiten',
    colourSchemeHeaderAddButtonText: 'Farbregel hinzufügen',
    colourSchemeSummaryText: 'Lassen Sie jede Buchungsart durch Festlegung Ihrer bevorzugten Farbe für die individuellen Felder in einer anderen Farbe anzeigen.',
    colourSchemeRolesSummaryText: 'Lassen Sie jede Position durch Festlegung Ihrer bevorzugten Farbe für die individuellen Felder in einer anderen Farbe anzeigen.',
    colourSchemeConfirmModalText: 'Durch Änderung des Feldes werden alle bestehenden Farbthema-Regeln gelöscht. Fortfahren?',
    colourSchemeTableRequired: 'Bitte wählen Sie eine Tabelle',
    colourSchemeFieldRequired: 'Bitte wählen Sie ein Feld',
    colourSchemeGridEmptyText: 'Es wurden noch keine Farbregeln erstellt',
    colourSchemeGridAddButtonText: 'Eine Regel hinzufügen',
    colourSchemePreviewText: 'Textvorschau',
    colourSchemeFieldValueRequired: 'Bitte einen Feldwert aus der Liste wählen',
    colourSchemeFieldValueUnique: 'Feldwert sollte einzigartig sein',
    colourSchemeLookUpNoMatches: 'Keine Übereinstimmungen',
    colourSchemeSelectValuePlaceholder: 'Wert wählen',
    colourSchemeResourceLookupPlaceholder: 'Nicht zugewiesen',
    colourSchemeColourSchemeAddButton: 'Farbregel hinzufügen',
    colourSchemeTable: 'Tabelle',
    colourSchemeField: 'Feld',
    colourSchemePreviewTextTitle: 'Vorschau',
    colourSchemeColourCodeTextTitle: 'Farbe',
    colourSchemeCreateColorTheme: 'Farbthema erstellen',
    colourSchemeFieldDropdownPlaceholder: 'Feld festlegen',
    colourSchemeTableDropdownPlaceholder: 'Keine',
    colorSchemeUniqueTitle: 'Bitte geben Sie eine einzigartige Bezeichnung für das Farbthema ein',
    colorSchemeRequiredTitle: 'Bitte geben Sie eine Bezeichnung für das Farbthema ein',
    colourThemeTabTitle_Bookings: 'Buchungen',
    colourThemeTabTitle_Roles: 'Positionen',
    colourSchemeDescriptionPlaceholder: 'Farbbeschreibung',
    //conflicts
    conflictPageHeader: 'Konflikte',
    conflictsMsgsSubHeaderLabel: 'Wann Konflikte angezeigt werden sollen',
    conflictsMsgsSubLabel_line1: 'Legen Sie fest, ob Konflikte angezeigt werden sollen. \'Wählen Sie den Schwellenwert, ab dem eine oder mehrere einer Ressource zugewiesene Buchungen als Konflikt angezeigt werden.',
    conflictsMsgsResourceLoadingControlLabel: 'Wenn die Ressourcenauslastung auf Minimum ist',
    conflictsMsgsShowConflictsLabel: 'Konflikte anzeigen',
    conflictsMsgsConfermationModelHeader: 'Bitte geben Sie einen gültigen Auslastungsschwellenwert ein.',
    conflictsMsgsConfermationModelSubHeader: 'Der Auslastungsschwellenwert sollte zwischen {minValue} und {maxValue} % liegen.',
    conflictsMsgsShowResourceLoadingNote: 'Akzeptierte Auslastungswerte liegen zwischen {minValue} und {maxValue} %.',
    conflictsMsgsOkBtn: 'OK',
    conflictsMsgsYesText: 'Ja',
    conflictsMsgsNoText: 'Nein',

    //Service accounts
    serviceAccounts: {
        maximumFieldLengthValidationMessage: 'Maximal ${maximumFieldSize} Zeichen',
        pageHeader: 'Dienstkontoverwaltung',
        addEntity: 'Dienstkonto hinzufügen',
        saveButtonLabel: 'Änderungen speichern',
        cancelButtonLabel: 'Abbrechen',
        markedForDeletionMessage: 'Zum Löschen  markiert. Wird gelöscht, sobald Sie die Änderungen bestätigen. ',
        cancelDeletion: 'Löschvorgang abbrechen',
        serviceAccountManagementTitle: 'Dienstkontoverwaltung',
        serviceAccountsSubTitle: 'Aktive Dienstkonten',
        serviceAccountDescription: 'Sie möchten etwas entwickeln, das sich integrieren lässt und Retain erweitert? Dienstkonten hier hinzufügen, bearbeiten oder löschen.',
        serviceAccountNavigateToRetainApiDocumentationMessage: 'In unserer Hilfe-Dokumentation finden Sie auch mehr Informationen über <linkText>Retain Cloud APIs</linkText>.',
        serviceAccountManagementUsedAccountsWarning: 'Sie nähern sich dem Grenzwert von 5 Dienstkonten, die hinzugefügt werden können. Verwalten Sie die vorhandenen Konten oder entfernen Sie nicht genutzte Konten.',
        serviceAccountManagementUsedAccountsError: 'Sie haben den Grenzwert von 5 Dienstkonten erreicht, die hinzugefügt werden können. Verwalten Sie die vorhandenen Konten oder entfernen Sie nicht genutzte Konten.',
        emptyStateMessage: 'Keine Ergebnisse',
        setPassword: 'Kennwort einstellen',
        password: 'Kennwort',
        name: 'Name',
        tenant: 'Teilnehmer',
        securityProfile: 'Sicherheitsprofil',
        email: 'E-Mail',
        savePasswordTooltip: 'Speichern Sie die Änderungen, bevor Sie ein Kennwort festlegen',
        nameValidationMessage: 'Geben Sie einen Namen ein',
        emailValidationMessage: 'Geben Sie Ihre E-Mail-Adresse ein',
        typeHerePlaceholder: 'Hier eingeben',
        nameColumnTitle: 'Name',
        emailColumnTitle: 'E-Mail',
        securityProfileColumnTitle: 'Sicherheitsprofil',
        actionsColumnTitle: 'Aktionen',
        emailExplanation: 'Geben Sie eine eindeutige E-Mail-Adresse ein, die noch nicht für ein vorhandenes Konto genutzt wird',
        formHasErrorsMessage: 'Dieses Formular enthält Fehler'
    },

    //workflows
    workflowsSettings: {
        roleByNameWorkflowPageHeader: 'Rollen nach Bezeichnung',
        roleByRequirementsWorkflowPageHeader: 'Rollen nach Voraussetzungen',
        rolesByNamePageDescriptionLabel: 'Rollen können verwendet werden, um eine Ressource für eine Aufgabe anzufordern. Rollen nach Namen werden verwendet, wenn Sie die gewünschte Ressource wissen.',
        rolesByRequirementsPageDescriptionLabel: 'Rollen können verwendet werden, um eine Ressource für eine Aufgabe anzufordern. Für Rollen nach Voraussetzungen können Sie Voraussetzungen eingeben, aufgrund derer Sie eine passende Ressource suchen.',
        draftStateDescription: 'Rollen können als Entwurf gespeichert werden, wenn sie noch nicht bereit sind, als Anfrage eingereicht zu werden.',
        requestedStateDescription: 'Rollen, die als Buchungen aktiviert oder abgelehnt werden können.',
        liveStateDescription: 'Rollen, die gebucht worden sind.',
        rejectedStateDescription: 'Rollen, die abgelehnt worden sind.',
        archivedStateDescription: 'Rollen, die keine Aktion mehr verlangen',
        statesLegendTitle: 'Status des Arbeitsablaufs',
        actorsSectionTitle: 'Akteure im Arbeitsablauf',
        actorsSectionDescription: 'Definieren Sie, was jeder einzelne Akteur im Arbeitsablauf tun kann.',
        requesterActorTitle: 'Anforderer',
        roleByNameRequesterActorDescription: 'Erstellt Rollen, um Anfragen für einen Auftrag zu stellen.',
        roleByRequirementsRequesterActorDescription: 'Erstellt Rollen, um Anfragen mit einer Reihe von Anforderungen einzureichen.',
        whoCanCreateRolesLabel: 'Alle können Rollen erstellen für',
        roleByRequirementsWhoCanCreateRolesLabel: 'Alle können Rollen erstellen auf',
        whoCanCreateRolesForThemselvesLabel: 'Alle können für sich selbst Rollen erstellen auf',
        creatorActionsInfoBannerLabel: 'Folgende Aktionen sind nur für den Ersteller der Rolle verfügbar.',
        deleteRolesWithAssigneesInfoBannerLabel: 'Um Rollen mit mehreren Beauftragten zu löschen, muss der Benutzer folgende Bedingung für alle zugeordneten Personen erfüllen.',
        draftRolesActionsTitle: 'Entwurf',
        requesterCanEditDeleteLabel: 'Entwurfsrollen bearbeiten und löschen',
        requesterCanSubmitLabel: 'Anfrage absenden',
        requesterCanArchiveLabel: 'Archiv',
        requestedRolesActionsTitle: 'Angefordert',
        requesterCanRestartRequestedLabel: 'Neustart',
        requesterCanDeleteRequestedLabel: 'Angeforderte Rollen löschen',
        restartingActionsTitle: 'Neustart',
        requesterCanRestartRejectedLabel: 'Abgelehnte Rollen neu starten',
        requesterCanRestartArchivedLabel: 'Archivierte Rollen neu starten',
        completedRolesActionsTitle: 'Fertiggestellte Rollen',
        requesterCanDeleteLiveLabel: 'Aktive Rollen löschen',
        requesterCanDeleteRejectedLabel: 'Abgelehnte Rollen löschen',
        requesterCanDeleteArchivedLabel: 'Archivierte Rollen löschen',
        assignerActorTitle: 'Zuweiser',
        assignerActorDescription: 'weist den angeforderten Rollen auf Grundlage der Anforderungen zu Ressourcen zu.',
        assignerWhoCanRespondLabel: 'kann Rollen zuweisen',
        assignerCanAssignResourcesLabel: 'Ressourcen zu Rollen zuweisen',
        approverActorTitle: 'Genehmiger',
        approverActorDescription: 'reagiert auf Anforderungen',
        appproverWhoCanRespondLabel: 'kann auf Anforderungen reagieren zum',
        approverCanMakeLiveLabel: 'Aktivieren',
        criteriaRoleCanMakeLiveLabel: 'Aktivieren einer Rolle oder einer zugeordneten Person',
        approverCanRejectLabel: 'Ablehnen',
        criteriaRoleCanRejectLabel: 'Ablehnen einer Rolle oder einer zugeordneten Person',
        approverCanRestartLabel: 'Neustart',
        approverCanDeleteRequestedLabel: 'Angeforderte Rollen löschen',
        approverCanDeleteLiveLabel: 'Aktive Rollen löschen',
        approverCanDeleteRejectedLabel: 'Abgelehnte Rollen löschen',
        approverCanDeleteArchivedLabel: 'Archivierte Rollen löschen',
        approverSelectedResourcesInvalidValue: 'Max ${maxLimitCount} Ressourcen. Wenn mehr Genehmiger notwendig sind, sollten Sie vielleicht stattdessen \'Ausgewählte Sicherheitsprofile\' verwenden.',
        assignerSelectedResourcesInvalidValue: 'Max ${maxLimitCount} Ressourcen. Wenn mehr Zuweiser notwendig sind, sollten Sie vielleicht stattdessen \'Ausgewählte Sicherheitsprofile\' verwenden.',
        requesterSelectedResourcesInvalidValue: 'Max. ${maxLimitCount} Ressourcen. Wenn weitere Anforderer notwendig sind, sollte stattdessen die Option \'Ausgewählte Sicherheitsprofile\' in Betracht gezogen werden.',
        selectedSecurityProfilesInvalidValue: 'Max. ${maxLimitCount} Sicherheitsprofile',
        addAnotherPrefix: 'Hinzufügen',
        noResultsMessagePrefix: 'Kein',
        noResultsMessageSuffix: 'mit diesem Namen wurde gefunden.',
        multiValueFieldErrorMessagePrefix: 'Mindestens eine Option wählen',
        saveButtonLabel: 'Änderungen speichern',
        cancelButtonLabel: 'Abbrechen',
        formHasErrorsMessage: 'Formular weist Fehler auf',
        noResultsFoundMessage: 'Keine Ergebnisse gefunden',
        roleCreatorLabel: 'Der Rollenersteller und',
        whoCanActAsRequesterLabel: 'können als Rollenanforderer agieren.'
    },
    //report settings
    reportSettingsSubLabel: 'Legen Sie Zielwerte für Ihr Unternehmen fest',
    reportSettingsErrorTitle: 'Ungültiger Zielwert',
    reportSettingsBillabillityLabel: 'Globales Ziel für die fakturierbare Nutzung',
    reportSettingsBillabilityRule: 'Legen Sie ein globales Ziel  für den Vergleich mit den fakturierbaren Ist-Werten der Nutzung fest',
    reportSettingsJobOpportunityLabel: 'Schwellenwert für Auftragschancen',
    reportSettingsJobOpportunityRule: 'Mindestprozentsatz der Chance für einen fakturierbaren Auftrag',
    reportSettingsFieldTitle: 'Fakturierbare Nutzung',

    //Colour schemes
    deleteColourSchemeTitle: 'Farbthema {itemTobeDeleted} löschen?',
    deleteColourSchemeInformationMessage: 'Alle Projekte, die dieses Thema verwenden, werden auf das Standardthema zurückgesetzt. ',
    deleteColourSchemeWarningMessage: 'Diese Änderung kann nicht rückgängig gemacht werden.',
    colourSchemeCheckMessage: 'Ich verstehe, welche Auswirkungen das Löschen dieses Farbthemas hat.',
    deleteColourSchemePrimaryButton: 'Farbthema löschen',
    deleteColourSchemeSecondaryButton: 'Farbthema übernehmen',

    colorPickerText: 'Farbwähler',

    //security Profile
    securityProfilePageHeader: 'Sicherheitsprofile',
    securityProfileFieldName: 'Sicherheitsprofilnamen bearbeiten',
    functionalAccessHeading: 'Allgemeines',
    functionalAccessSubHeading: 'Steuert, auf welche Seiten und Funktionen dieses Sicherheitsprofil Zugriff hat. Sie können beispielsweise eine Regel für den funktionalen Zugriff verwenden, um den Zugriff auf die Administratoreinstellungen zu sperren.',
    yesLabel: 'Ja',
    noLabel: 'Nein',
    entityAccessSubHeading: 'Legen Sie die Zugänglichkeitsstufe fest, die dieses Sicherheitsprofil auf {entityName} hat. Schalten Sie',
    skillEntitySubHeading: 'Legen Sie die Zugänglichkeitsstufe fest, die dieses Sicherheitsprofil auf {entityName} hat. Schalten Sie',
    entityAccessSubHeadingRemaining: 'Zugriff ein oder aus oder legen Sie detaillierte Regelungen für Zugriffsberechtigungen fest.',
    readEntityResource: '{entityName}s sind für Nutzer immer sichtbar. Sie können jedoch die Sichtbarkeit bestimmter {entityName}s über die nachstehenden Bedienelementen einschränken. {lineBreak} Lesesicherheit übersteigt alle anderen Sicherheitsregeln.',
    readEntityJob: '{entityName}s sind für Nutzer immer sichtbar. Sie können jedoch die Sichtbarkeit bestimmter {entityName}s über die nachstehenden Bedienelementen einschränken. {lineBreak} Benutzer sind immer in der Lage, die Aufgaben anzuzeigen, für die sie gebucht sind. Lesesicherheit übersteigt alle anderen Sicherheitsregeln.',
    readEntityBooking: '{entityName}s sind je nach den für den Auftrag und die Ressource festgelegten Lesebedingungen sichtbar. Lesesicherheit übersteigt alle anderen Sicherheitsregeln.',
    readEntityRole: '{entityName}s sind je nach den für den Auftrag und die Ressource festgelegten Lesebedingungen sichtbar. Lesesicherheit übersteigt alle anderen Sicherheitsregeln.',
    readEntityScenario: '{entityName} sind für alle Nutzer immer sichtbar. Lesesicherheit übersteigt alle anderen Sicherheitsregeln.',
    readEntityRoleRequest: '{entityName}s sind für alle Nutzer immer sichtbar. Lesesicherheit übersteigt alle anderen Sicherheitsregeln.',
    readEntityClient: '{entityName}s sind für alle Nutzer immer sichtbar. Lesesicherheit übersteigt alle anderen Sicherheitsregeln.',
    createEntity: 'Eine Deaktivierung blendet die Optionen auf der Benutzeroberfläche aus und dieses Sicherheitsprofil wird für die Erstellung von {entityName} gesperrt.',
    editEntity: 'Eine Deaktivierung blendet die Optionen auf der Benutzeroberfläche aus und dieses Sicherheitsprofil wird für die Bearbeitung von {entityName} gesperrt.',
    readEntitySkill: 'Kompetenzen und Zertifizierungen sind für Benutzer immer sichtbar. Sie können aber mithilfe der Steuerelemente unten einschränken, welche Kompetenzen und Zertifizierungen Benutzer ihrem eigenen Profil hinzufügen können.',
    deleteEntity: 'Eine Deaktivierung blendet die Optionen auf der Benutzeroberfläche aus und dieses Sicherheitsprofil wird für die Löschung von {entityName} gesperrt.',
    customConditionsAreaHeader: '{entityName}s, die ...',
    liveRoleSubHeading: 'Aktive Positionen',
    liveRoleBookingMessage: 'Das Erstellen von aktiven Buchungen anhand von Positionen wird durch die Buchungsberechtigungen gesteuert; bitte sehen Sie',
    workflowCardSubHeading: 'Aufgaben-Workflow',
    workflowCardMessage: 'Verwalten, wer Aufgaben über die Einstellung „Workflow“ anfragen, genehmigen oder sonstige Maßnahmen ergreifen kann.',
    workflowCardBtnText: 'Zu Aufgaben-Workflows gehen',
    workflowTurnedOff: '\'Workflows\' auf der Registerkarte „Allgemein“ in Ihrem Sicherheitsprofil  aktivieren, um auf diese Seite zuzugreifen.',
    subRuleCreateRequest: 'Eine Deaktivierung blendet die Optionen auf der Benutzeroberfläche aus und dieses Sicherheitsprofil wird für die Erstellung von Anfragen gesperrt.',
    subRuleRejectRequest: 'Eine Deaktivierung blendet die Optionen auf der Benutzeroberfläche aus und dieses Sicherheitsprofil wird für die Ablehnung von Anfragen gesperrt.',
    subRuleAssignCriteriaRoles: 'Eine Deaktivierung blendet die Optionen auf der Benutzeroberfläche aus und dieses Sicherheitsprofil wird für die Zuweisung von vorgeschlagenen Positions-Ressourcen, die die Kriterien erfüllen, gesperrt.',
    readRuleCondition: 'Welche {entityName}s können sie sehen?',
    createRuleCondition: 'Welche {entityName}s können sie erstellen?',
    skillReadRuleCondition: 'Welche Kompetenzen und Zertifizierungen können Benutzer ihrem Profil hinzuzufügen?',
    editRuleCondition: 'Welche {entityName}s können sie bearbeiten?',
    deleteRuleCondition: 'Welche {entityName}s können sie löschen?',
    securityProfileNameRequiredMsg: 'Bitte geben Sie eine Bezeichnung für ein Sicherheitsprofil ein',
    uniqueSecurityProfileNameMsg: 'Bitte geben Sie eine einzigartige Bezeichnung für das Sicherheitsprofil ein',
    delete_failureWarningTitle: 'Sicherheitsprofil kann nicht gelöscht werden',
    delete_failureWarningMessage: 'Sicherheitsprofil {profileName} kann nicht gelöscht werden. Dieses Sicherheitsprofil wird verwendet.',
    delete_failureButtonLabel: 'OK',
    fieldSecurityHeading: '{entityName}-Felder',
    fieldSecuritySubHeading: 'Legen Sie fest, mit welchen {entityName}-Feldern dieses Sicherheitsprofil interagieren kann. Standardmäßig können die Felder bearbeitet werden, sie können jedoch auch auf „schreibgeschützt“ oder „ausgeblendet“ gesetzt werden.',
    fieldSecurityInfoForCondition: 'Für',
    fieldSecurityInfoThisFieldIsCondition: 'Dieses Feld ist',
    fieldSecurityInfoOtherwiseCondition: 'Ansonsten ist das Feld',
    mandatoryNotification: 'Das ist ein Pflichtfeld. Die Einschränkung eines Pflichtfeldes kann unerwartete Folgen haben.',
    editAccessNotification: 'Der Bearbeitungszugriff für {entityName}s wurde deaktiviert. Die Option \'Editierbar\' hat keine Auswirkung auf den Zugriff des Feldes.',
    readOnlyNotification: 'Das Feld ist vom System schreibgeschützt und kann nicht bearbeitet werden',
    externalIdNotificationMessage: ' Dieses Feld kann nur über das API-Portal bearbeitet werden',
    note: 'Hinweis',
    important: 'Wichtig',
    emptyFieldSecurityViewMessage: 'Es wurden noch keine Felder hinzugefügt',
    accessLevel: 'Zugriffsebene',
    tabMessage: '{tabName}-Tab',
    skillCategoriesLabel: 'Kompetenzkategorien',
    departmentLabel: 'Abteilungen',
    divisionLabel: 'Bereiche',
    skillEntityTypeLabel: 'Kompetenzarten',
    serviceLineLabel: 'Servicepositionen',
    skillsCertificationLabel: 'Kompetenzen und Zertifizierungen',
    viewbudgetEntity: 'Ermöglichen Sie Benutzern die Anzeige der geschätzten Budgetwerte für Rollen und der tatsächlichen Werte, sobald Ressourcen zugewiesen sind',
    managerApprovalAlert: 'DE_Users must have a manager set in \'Reports to\' field or they will not be able to update their skills_DE',
    managerApprovalSubHeading: 'DE_For users with this security profile any changes they make to their own skills will need to be approved by their manager. Skill preferences can be changed without approval_DE',
    customConditions: {
        operators: {
            Int: {
                LessThan: 'Weniger als',
                LessThanOrEqual: 'Weniger als oder gleich',
                Equals: 'Gleich',
                GreaterThanOrEqual: 'Größer als oder gleich',
                GreaterThan: 'Größer als',
                NOT_EQUALS_OPERATOR: 'Ist nicht gleich'
            },
            DateTime: {
                LessThanOrEqual: 'Vorher',
                GreaterThanOrEqual: 'Nachher'
            },
            ID: {
                IN_OPERATOR: 'Ist eins von',
                NOT_IN_OPERATOR: 'Ist nicht eins von'
            },
            Bool: {
                Equals: 'Gleich'
            }
        },
        valueTypes: {
            relativeToToday: 'Relativ zu heute',
            blank: 'Leer',
            selectedValues: 'Ausgewählte Werte',
            loggedInUserValue: 'Wert des eingeloggten Benutzers',
            existingValue: 'Vorhandener Wert',
            customValue: 'Benutzerdefinierter Wert'
        },
        relativeDateValues: {
            PLUS_180: 'Heute + 180 Tage',
            PLUS_90: 'Heute + 90 Tage',
            PLUS_28: 'Heute + 28 Tage',
            PLUS_7: 'Heute + 7 Tage',
            PLUS_1: 'Morgen',
            TODAY: 'Heute',
            MINUS_1: 'Gestern',
            MINUS_7: 'Heute - 7 Tage',
            MINUS_28: 'Heute - 28 Tage',
            MINUS_90: 'Heute - 90 Tage',
            MINUS_180: 'Heute - 180 Tage'
        },
        noConditionOperatorError: 'Bitte wählen Sie einen Betreiber',
        noConditionValueTypeError: 'Bitte Art des Werts auswählen',
        addConditionsListButtonLabel: '+ Bedingung für UND hinzufügen',
        andOperatorLabel: 'UND',
        maxConditionsCountLabel: ' Maximal 3',
        addConditionRowButtonLabel: '+ Bedingung für ODER hinzufügen',
        orOperatorLabel: 'ODER',
        fieldHeaderLabel: 'Feld',
        operatorHeaderLabel: 'Betreiber',
        valueTypeHeaderLabel: 'Art des Werts',
        valueHeaderLabel: 'Wert',
        noResultsFoundMessage: 'Keine Ergebnisse gefunden',
        pleaseEnterFieldLabel: 'Bitte Feld eingeben',
        conditionFieldNamePlaceholder: 'Feld auswählen ...',
        yesLabel: 'Ja',
        noLabel: 'Nein',
        wholeNumberInputError: 'Bitte gültigen Wert eingeben',
        commonPredefinedConditionsLabel: 'Vordefinierte Bedingung verwenden',
        inheritReadPredefinedConditionsLabel: 'Nur für Auftrag und Ressource festgelegte Lesebedingungen verwenden',
        commonCustomConditionLabel: 'Benutzerdefinierte Bedingung erstellen',
        inheritReadCustomConditionLabel: 'Benutzerdefinierte Bedingung zu den Lesebedingungen für Auftrag und Ressource hinzufügen',
        addAnotherPrefix: 'Hinzufügen',
        deleteRowLabel: 'Zeile löschen',
        pleaseEnterValueLabel: 'Bitte Wert eingeben',
        noResultsMessagePrefix: 'Nein',
        noResultsMessageSuffix: 'wurde mit dieser Bezeichnung gefunden.',
        jsonConditionLabel: 'Benutzerdefinierte JSON-Regel festlegen',
        jsonConditionWarningBannerText: 'Benutzerdefinierte JSON-Regeln sollten mit Vorsicht verwendet werden, da sie zu Leistungseinbußen führen können.\nDie Fehlkonfiguration von JSON-Regeln kann zu Instabilität führen.',
        invalidJsonErrorMessage: 'Achten Sie bitte darauf, dass das JSON gültig ist.',
        apiPortalInfoText: 'Validieren Sie JSON-Regeln automatisch, indem Sie außerhalb des Textbereichs klicken',
        apiPortalLabel: 'API-Portal.',
        inheritReadJsonConditionLabel: 'Benutzerdefinierte JSON-Regel zusätzlich zu den eingestellten Lesebedingungen für Auftrag und Ressource festlegen.',
        //Skills
        addSkillHeader: 'Kompetenzen und Zertifizierungen zur Profilseite hinzufügen',
        allSkillsLabel: 'Alle Kompetenzen und Zertifizierungen',
        onlyTheseSkillsLabel: 'Nur diese',
        onlyRelatedSkillsLabel: 'Nur Kompetenzen und Zertifizierungen in Bezug zu diesen',
        emptySkillErrorMessage: 'Bitte wählen Sie mindestens einen Wert',
        emptyJsonConditionErrorMessage: 'Bitte wählen Sie mindestens eine der Optionen'
    },
    //entitiesConfiguration
    planningDataAliasUseCaseInfo: 'Ein Alias ist ein gängigerer Begriff, der in Ihrer gesamten Anwendung verwendet werden kann. Fügen Sie Aliasse hinzu, um nicht geläufige Begriffe besser identifizieren zu können.',
    aliasHeading: 'Alias',
    singularAliasLabel: 'Singular-Alias',
    pluralAliasLabel: 'Plural-Alias',
    BookingDescription: 'Eine Buchung ist eine Auftragszuweisung an eine Ressource für einen bestimmten Zeitraum und beinhaltet eine messbare Stundenanzahl. Buchungen können für Aufträge wie Projektmanagement oder Tests sowie für Aufgaben wie Urlaub oder Krankheitszeiten generiert werden. Jede Buchung muss gegen einen Auftrag gebucht werden.',
    JobDescription: 'Ein Auftrag ist ein Projekt, eine Aufgabe oder eine Arbeit, für die eine Ressource erforderlich ist. Buchungen können für Aufträge wie Projektmanagement oder Tests sowie für Aktivitäten wie Urlaubs- oder Krankheitszeiten generiert werden.',
    ResourceDescription: 'Ressourcen bezieht sich auf Materialien, Personal oder sonstige Aktivposten, die von einem Unternehmen für effiziente Arbeitsabläufe genutzt werden können. Jeder Posten mit begrenzter Kapazität, der einen Auftrag ausführen kann, kann als Ressource betrachtet werden. Personal, Schulungsräume und Maschinen sind beispielsweise Ressourcen.',
    ClientDescription: 'Kunden\' bezieht sich auf die Klienten oder Kunden Ihres Unternehmens. Ein Kunde kann mehrere Aufträge haben.',
    rolerequestgroupDescription: 'Szenario ist eine Sammlung von Aufgaben für einen bestimmten Auftrag.',
    rolerequestDescription: 'Eine Position ist eine Vorstufe für eine oder mehrere Buchungen. Sie können in Bezug auf eine Ressource oder auf bestimmte Kriterien erstellt werden.',
    DepartmentDescription: 'Eine Abteilung ist ein Segment eines Unternehmens, das sich auf einen bestimmten Geschäftsprozess spezialisiert hat und sich häufig aus Ressourcen mit ähnlichen Qualifikationen und Verantwortlichkeiten zusammensetzt',
    DivisionDescription: 'Eine Division ist eine Geschäftseinheit oder ein Segment eines Unternehmens auf höchster Ebene und setzt sich aus mehreren Abteilungen zusammen',
    //Skill Types Message
    //Levels
    skillPageHeader: 'Kompetenzen und Zertifizierungen',
    skillTypeLevelsTabTitle: 'Niveaus',
    skillTypeSkillsTabTitle: 'Qualifikationen',
    fieldsTabTitle: 'Felder',
    addSkill: 'Eine Qualifikation hinzufügen',
    addRetainSkillLibrary: 'Aus Retain-Bibliothek hinzufügen',
    levelNameDescriptionText: 'Die Niveaus für diesen Qualifikationstyp werden wie folgt bezeichnet',
    levelNameInfoText: '{levelName} 1 ist die niedrigste Ebene.',
    addLevels: '{levelName} hinzufügen',
    skillTypeLevelNameRequiredMessage: 'Bitte geben Sie eine Niveaubezeichnung ein',
    whiteSpaceValidation: 'Nur Leerraum ist bei der Option nicht zulässig',
    skillLevelRequiredValidation: 'Niveaubezeichnung ist erforderlich',
    skillLevelUniqueValidation: 'Niveaubezeichnung existiert bereits',
    skillLevelNamePlaceholder: 'Ebenenbezeichnung',
    skillLevelDescriptionPlaceholder: 'Ebenenbeschreibung',
    //Skill fields
    skillFieldHeading: 'Zusätzliche Felder für diesen Qualifikationstyp',
    addFieldButtonLabel: 'Ein Feld hinzufügen',
    skillCommandBarFieldName: 'Kompetenznamen bearbeiten',
    //reusable grid
    cancelDeletion: 'Löschen abbrechen',
    markedForDeletion: 'Zum Löschen markiert. Wird nach Bestätigung der Änderungen gelöscht. ',
    addButtonReusableGrid: 'Eine Zeile hinzufügen',
    mandatory: 'Obligatorisch',
    markDeleteWarningTitle: 'Möchten Sie wirklich „{recordName}“ zum Löschen markieren?',
    markDeleteWarningTitleDefault: 'Möchten Sie diesen Datensatz wirklich zum Löschen markieren?',
    //Field Properties
    fieldFormattingDisabledTooltipText: 'Kann bei integrierten Feldern nicht geändert werden',
    fieldPropetiesAliasDefinition: 'Ein Alias ist ein gängigerer Begriff, der in Ihrer gesamten Anwendung verwendet werden kann.',
    fieldPropetiesChooseAliasMessage: 'Wählen Sie für die ausgewählte Tabelle einen Alias und geben Sie Bezeichnungen für Feldnamen ein, die in Ihrer gesamten Anwendung verwendet werden können.',
    noDescriptionMsg: 'Hier klicken, um eine Bezeichnung hinzuzufügen',
    configurePageField: '{pageTitle}-Felder',
    descriptionText: 'Bezeichnung',
    builtInTabsTitle: 'Integrierte Felder',
    customFieldTabsTitle: 'Benutzerdefinierte Felder',
    builtInTabsDescription: 'Das sind die standardmäßig in das System integrierten {entity}-Felder. Sie können nicht geändert werden. Sie können jedoch ausgeblendet werden, wenn sie nicht benötigt werden.',
    customFieldTabsDesc: 'Das sind Felder, die Sie der Anwendung hinzugefügt haben. Sie werden neben den System-{entity}-Feldern angezeigt.',
    builtInFieldTab: 'Integriert',
    customText: 'Benutzerdefiniert',
    customFieldAddButton: 'Ein benutzerdefiniertes Feld hinzufügen',
    emptyCustomViewMessage: 'Es wurden noch keine benutzerdefinierten Felder hinzugefügt',
    emptyBuiltInViewMessage: 'Es wurden noch keine integrierten Felder hinzugefügt',
    fieldLabelReqdValidation: 'Die Feldbezeichnung darf nicht leer sein',
    uniqueFieldNameValidation: 'Das ${label} muss eindeutig sein',
    bracketsFieldNameValidation: 'Das ${label} darf weder [ noch ] enthalten',
    lookupFieldInputRequired: 'Das Suchfeld darf nicht leer sein',
    noMatchesText: 'Keine Übereinstimmungen',
    lookUpField: 'Werte aus',
    lookUpLinkText: 'Suchfeld-Konfiguration',
    maxCharMessage: 'Maximal {maxChar} Zeichen',
    maxCharTagMessage: 'Limit wird nur auf benutzerdefinierte Schlagwörter angewendet. Max. {maxChar}',
    incorrectDecimalFormatMsg: 'Falsches Dezimalformat (Geben Sie einen numerischen Wert mit maximal 10 Dezimalstellen ein)',
    incorrectTargetBillabilityMessage: 'Der zulässige Mindestwert ist 0',
    inputBeyondLimitMsg: 'Eingabewert liegt außerhalb des Limits',
    noOfChars: 'Anzahl der Zeichen',
    typeText: 'Typ',
    labelText: 'Bezeichnung',
    hiddenText: 'Ausgeblendet',
    decimalPlacesText: 'Dezimalstellen',
    exampleText: 'Beispiel',
    lookupValuesLabel: 'Referenzwerte',
    newFieldValuesLabel: 'Bezeichnung der neuen Liste',
    nextLabel: 'Weiter',
    prevLabel: 'Zurück',
    fieldValuesTitle: 'Feldwerte',
    fieldValuesSubTitle: 'Legen Sie die Werteliste für dieses Feld fest',
    lookupValuesNotifyMessage: 'Das kann nach der Erstellung des Feldes nicht mehr geändert werden. Die Werte in der Liste können später auf der Seite \'Werte\' bearbeitet werden.',
    newFieldNameRequiredMessage: 'Feldwertbezeichnung ist erforderlich',
    fieldAliasRequiredValidation: '{fieldAlias}-Alias ist erforderlich',
    fieldDescriptionRequiredValidation: '{fieldDescription}-Bezeichnung ist erforderlich',
    newFieldValuesRequiredMessage: 'Feldwertliste darf nicht leer sein',
    multiSelectText: 'Mehrfachauswahl',
    valuesListLabel: 'Werteliste',
    useExistingValuesText: 'Eine vorhandene Werteliste verwenden',
    createNewValuesListText: 'Eine neue Werteliste erstellen',
    createNewValuesSaveInfo: 'Neue Liste wird auf der Seite \'Werte\' gespeichert',
    planningDataSaveInfo: 'Das kann nach der Erstellung des Feldes nicht mehr geändert werden',
    formattingLabel: 'Formatierung',
    minimumLabel: 'Min.',
    maximumLabel: 'Max.',
    valuesText: 'Werte',
    valueFieldsCommandBarFieldName: 'Feldwertnamen bearbeiten',
    calculatedMessage: 'Dieses Feld wird automatisch aus den Daten in Ihrer Anwendung berechnet.',
    rangeLabel: 'Bereich',
    systemReadonlyMessage: 'Dieses Feld ist schreibgeschützt und enthält von der Anwendung aktualisierte Daten',
    systemRequiredMessage: 'Dieses Feld wird vom System zur Erstellung von {entity} benötigt',
    fieldNameLabel: 'Feldbezeichnung',
    deletefieldsHeading: 'Felder löschen',
    keepFieldsHeading: 'Felder übernehmen',
    newText: 'Neu',
    fieldText: 'Feld',
    warningMessageWithFieldNames: 'Sie sind im Begriff, {fieldsArray} ',
    warningMessageMoreFields: 'und {number} weitere Felder zu löschen. Durch das Löschen dieser Felder werden alle darin enthaltenen Daten gelöscht. ',
    warningMessageLessFields: 'Felder. Durch das Löschen dieser Felder werden alle darin enthaltenen Daten gelöscht.',
    deletefieldsHeadingConfirmation: 'Felder und darin enthaltene Daten löschen',
    deleteSkillsButtonLabel: 'Qualifikationen löschen',
    keepSelectedSkillsLabel: 'Ausgewählte Qualifikationen übernehmen',
    fieldPropertiesLicenseWarning: 'Sie sind kurz vor dem gemäß Ihrer Lizenz zulässigen Limit. {contactUs}, um das Limit für Ihren Vertrag zu erhöhen.',
    fieldPropertieslicenseError: 'Sie haben das gemäß Ihrer Lizenz zulässige Limit erreicht. {contactUs}, um das Limit für Ihren Vertrag zu erhöhen.',
    showLookupLink: 'Suchwerte anzeigen',
    lookupValueUniqueValidation: 'Dieser Wert wurde bereits hinzugefügt',
    pressEnterMessage: 'Den Wert durch Drücken der Eingabetaste hinzufügen',
    defaultValueLabel: 'Standardwert',
    noDefaultValueStaticHeaderText: 'Kein \'Standard',
    multipleDefaultValueStaticHeaderText: 'Mehrere Werte',
    noDefaultPlaceHolder: 'Kein \'Standard',
    invalidDefaultValueValidation: 'Ungültiger Standardwert',
    defaultValueSelectOwnValueText: 'Wert auswählen oder leer lassen',
    defaultValueInheritValueText: 'Wert von angemeldetem Benutzer übernehmen',
    defaultValueInheritSummaryText: 'Von Benutzer(n) übernehmen',
    defaultValueFieldText: '{fieldName} des Benutzers wird übernommen',
    defaultValueFieldDisabledText: 'Nach dem Speichern dieses Feldes steht eine neue Liste mit Suchwerten zur Verfügung, die als Standardwerte festgelegt werden können',
    inheritUserField: '{fieldName} des Benutzers',
    yesMsg: 'Ja',
    noMsg: 'Nein',
    //Field look up values config
    fieldLookupValueAddButton: 'Einen Wert hinzufügen',
    noResultsText: 'Keine Ergebnisse',
    fieldLookupValuesDescription: 'Hinzufügen, Bearbeiten oder Löschen von Werten, die in Listen angezeigt werden. Eine Liste für „Standort“ kann z. B. Werte wie London, Paris und New York anzeigen.',
    fieldLookupValuesEntityData: 'Felder, die diese Werte aktuell verwenden',
    saveFieldLookupValueAlert: 'Markierte Werte löschen?',
    saveFieldLookupValueWarning: 'Durch das Löschen von Werten werden diese aus den Suchdropdowns und allen anderen Bereichen entfernt, in denen diese Werte angezeigt werden.',
    fieldLookupCheckMessage: 'Sie haben {deleteCount} Werte markiert, die gelöscht werden sollen.',
    yesDeleteValuesButtonLabel: 'Ja, löschen Sie die Werte',
    noKeepValuesButtonLabel: 'Nein, behalten Sie die Werte bei',
    optionIsRequiredMessage: 'Option ist erforderlich',
    optionAlreadyExist: 'Option existiert bereits',
    uniqueValueNameMessage: 'Bitte geben Sie eine einzigartige Bezeichnung für den Wert ein',
    requiredValueNameMessage: 'Bitte geben Sie die Bezeichnung eines Wertes ein',
    fieldLookupValueInUseTitle: 'Wertelisten konnten nicht gelöscht werden',
    fieldLookupValueInUseMessage: 'Aktuell von einem Feld verwendete Wertelisten können nicht gelöscht werden.',
    fieldLookupValueInUseSubMessage: 'Sie müssen vor dem Löschen der Wertelisten alle verknüpften Felder löschen.',
    fieldLookupValuesSortText: 'Reihenfolge der Werte',
    fieldLookupValuesDefaultSortModeText: 'Alphabetisch (A - Z)',
    fieldLookupValuesCustomSortModeText: 'Inkrementell (benutzerdefinierte Reihenfolge)',
    fieldLookupValuesSortModeDescriptionText: 'Menüs und Listen werden in dieser Reihenfolge angezeigt',
    fieldLookupValuesCustomSortGridText: 'Den niedrigstwertigen Wert in der Liste an erster Stelle platzieren',
    noneText: 'Keine',
    typeNewValue: 'Geben Sie einen neuen Wert ein',
    //System Settings
    fieldPropertiesPageHeader: 'Felder',
    fieldLookupValuesPageHeader: 'Werte',
    gridColoumnTitle_Option: 'Option',
    gridColoumnTitle_Target_Billability: 'Fakturierbare Nutzung',
    //PageNames
    pageNameHeader: 'Seitennamen',
    pageNamesHeaderSummary: 'Markieren Sie die Seiten und ihre Anzeigenamen, die im linken Menü angezeigt werden sollen.',
    menuListTitle: 'Menüliste',
    settingValMsg: 'Bitte einen Einstellwert eingeben',
    maxSizeFieldMsg: 'Das Feld darf maximal 40 Zeichen enthalten',
    displayText: 'Anzeige',
    enableText: 'Aktivieren',
    summaryPageNameText: 'Logo – kein sichtbares Etikett',

    //dynamic generated child sections from setting api (System settings)
    Talent_Profile: 'Talentprofil',
    Jobs: 'Aufträge',
    Scheduler: 'Terminplaner',
    Timesheets: 'Zeiterfassungsbögen',
    Report: 'Bericht',
    Role_inbox: 'Funktion – Posteingang',
    Roles_board: 'Funktionstafel',
    Table_View: 'Tabellenansicht',
    //Currencies
    currenciesPageHeader: 'Währungen',
    //charge codes
    chargeTypePageHeader: 'Gebührentypen',
    whiteSpaceValidationChargeType: 'Nur Leerraum ist bei \'Gebührenart\' nicht zulässig',
    chargeCodeContent: 'Gebührenarten ermöglichen die Generierung unterschiedlicher Gebührensätze für verschiedenartige Aufträge. Erstellen Sie verschiedene Gebührenarten für die unterschiedlichen Tätigkeitsbereiche in Ihrem Unternehmen.',
    chargeRateContent: 'Hier Kosten- und Einnahmensätze für verschiedene Gebührencodes für spezifische Zeiträume {ChargeRateLinkText} hinzufügen, bearbeiten oder löschen',
    chargeRateRedirection: 'hier.',
    chargeCodeUsageWarning: 'Diese Gebührenart wird aktuell <strong>von {chargeTypeJobCount}-Aufträgen verwendet.</strong>',
    chargeCodeWarning: 'Das Löschen dieser Gebührenart führt dazu, dass sie aus allen Datensätzen entfernt wird und nicht mehr für Budget relevante Daten und Berichte verwendet werden kann.',
    chargeTypeDeletionWarning: 'Gebührenart {chargeTypeName} löschen',
    deleteChargeTypePrimaryButton: 'Gebührenart löschen',
    deleteChargeTypeSecondaryButton: 'Gebührenart übernehmen',
    chargeCodeCheckMessage: 'Ich verstehe, welche Auswirkungen das Löschen dieser Gebührenart hat',
    chargeCodeUniqueValidation: 'Bitte geben Sie eine einzigartige Gebührenart ein',
    chargeCodeReqdValidation: 'Gebührenart ist erforderlich',
    chargeCodeAddButton: 'Eine Gebührenart hinzufügen',
    noChargeCodeAvailable: 'Keine Gebührenart verfügbar',
    waterMarkChargeCode: 'Unbenannte Gebührenart',
    waterMarkDescription: 'Bezeichnung der Gebührenart',
    chargeTypeHeader: 'Gebührenart',

    //charge rates
    chargeRatePageHeader: 'Gebührenraten',
    hourlyChargeRatesHeading: 'Stundensätze pro Auftragstyp',
    waterMarkRevenue: 'Umsatz des Gebührensatzes',
    waterMarkCost: 'Kosten des Gebührensatzes',
    chargeRateDeleteMessage: 'Sie werden diesen Gebührensatz für alle zukünftigen Verwendungen löschen.',
    chargeRateDeleteSubMessage: 'Dieser Gebührensatz bleibt für laufende Buchungen bestehen.',
    cantUndoMessage: 'Das kann nicht rückgängig gemacht werden.',
    checkMessage: 'Ich verstehe die Konsequenzen dieser Löschung',
    deleteChargeRateTitle: 'Gebührensatz {deletedChargeRate} löschen?',
    deleteChargeCodeSpecificChargeRateTitle: 'Gebührensatz „{deletedChargeCodeName}“ aus „{deletedChargeRate}“ löschen?',
    deleteChargeRateHeading: 'Dieser Gebührensatz wird aktuell von {chargeRateResourceCount}-Ressourcen verwendet.',
    deleteChargeRateWarningMessage: 'Das Löschen führt dazu, dass er aus allen Datensätzen entfernt wird und nicht mehr für Budget relevante Daten und Berichte verwendet werden kann.',
    deleteChargeRateCheckboxMessage: 'Ich verstehe, welche Auswirkungen das Löschen dieses Gebührensatzes hat',
    deleteChargeRateButtonLabel: 'Gebührensatz löschen',
    keepChargeRateButtonLabel: 'Gebührensatz übernehmen',
    editChargeRateTitle: 'Aktuellen Gebührensatz „{editedChargeRateName}“ ändern?',
    editChargeRateHeading: 'Das ist der aktuelle Gebührensatz für die Gebührenart „{editedChargeRateName}“.',
    editChargeRateSubHeading: 'Wenn Sie diesen Gebührensatz ändern, wird er für alle Berechnungen und Berichte für diesen Zeitraum geändert.',
    editChargeRateWarningMessage: 'Möchten Sie den aktuellen Gebührensatz für „{editedChargeRateName}“ wirklich ändern?',
    editChargeRatePrimaryButton: 'Aktiven Satz ändern',
    addCostRevenueButtonLabel: 'Kosten und Einnahmen hinzufügen',
    customGridRangeOverlappingMessage: 'Es sind bereits Kosten und Einnahmen für Daten in diesem Bereich definiert',
    customGridRangeRequiredMessage: 'Bitte einen Datumsbereich eingeben',
    customGridRevenueRequiredMessage: 'Bitte Einnahmen eingeben',
    customGridCostRequiredMessage: 'Bitte Kosten eingeben',
    chargeRateNameField: 'Gebührensatznamen bearbeiten',
    chargeRateTitleRequiredMessage: 'Bitte geben Sie eine Bezeichnung für den Gebührensatz ein',
    chargeRateTitleUniqueMessage: 'Bitte geben Sie eine einzigartige Bezeichnung für den Gebührensatz ein',
    customGridNegativeMessage: 'Dieser Wert darf nicht negativ sein',
    dateRangeLabel: 'Datumsbereich',
    revenueLabel: 'Umsatz',
    costLabel: 'Kosten',
    //skills
    noRecommendations: 'Keine neuen Empfehlungen',
    addSkillsHeading: 'Qualifikationen für diesen Qualifikationstyp hinzufügen',
    activeSkillTitle: '\'{activeSkillConfiguration}\' ist einer der Kompetenzbereiche, die im Talentprofil eines Benutzers erscheinen.',
    skillHeaderText: 'Qualifikationen unter {activeSkillConfiguration}',
    skillHeaderDescription: 'Benutzer können die folgenden Qualifikationen unter dem Abschnitt \'{activeSkillConfiguration}\' für ihre Profile auswählen.',
    skillImportMessage: 'Für umfangreiche Aktualisierungen können Sie die Funktion {Importieren} verwenden',
    addSkillButtonFromCommandBar: 'Eine Qualifikation hinzufügen',
    skillNameLabel: 'Qualifikationsbezeichnung',
    skillType: 'Art',
    addSkillCategoryText: 'Kategorie hinzufügen',
    skillCategory: 'Kategorie',
    skillSubCategory: 'Unterkategorie',
    tagsLabel: 'Schlagwörter',
    skillTagExplaination: 'Tags sind Label, die Skills zugeordnet werden können. Tags erleichtern die Suche nach einem Skill, besonders wenn dieser unter einem anderen Namen bekannt ist. Drücken Sie nach dem Hinzufügen eines jeden Tags vor dem Speichern auf EINGABE.',
    skillNameRequired: 'Bitte geben Sie eine Bezeichnung für eine Qualifikation ein',
    uniqueSkillNameRequired: 'Bitte geben Sie eine einzigartige Bezeichnung für die Qualifikation ein',
    uniqueSkillItem: 'Die Bezeichnung für die Qualifikation muss einzigartig sein',
    deleteSkillFieldsPopupHeading: 'Markierte Felder löschen?',
    deleteSkillFieldsPopupWarningMessage: 'Möchten Sie die ausgewählten Qualifikationsfelder dauerhaft löschen? {number} Qualifikationsfelder werden aus diesem/den Qualifikationstyp(en) gelöscht und aus den Ressourcenprofilen sowie aus allen sonstigen Bereichen, in denen das Qualifikationsfeld angezeigt wird, entfernt.',
    deleteSkillFieldsButtonLabel: 'Qualifikationsfelder löschen',
    keepSkillFieldsButtonLabel: 'Qualifikationsfelder übernehmen',
    deleteSkillLevelsPopupHeading: 'Markierte Niveaus löschen?',
    deleteSkillLevelsPopupWarningMessage: 'Möchten Sie die ausgewählten Qualifikationsniveaus dauerhaft löschen? Gelöschte Qualifikationsniveaus werden aus den Ressourcenprofilen und allen sonstigen Bereichen, in denen diese Qualifikationsniveaus angezeigt werden, entfernt.',
    deleteSkillLevelsButtonLabel: 'Qualifikationsniveaus löschen',
    keepSkillLevelsButtonLabel: 'Qualifikationsniveaus übernehmen',
    deleteSkillsPopupHeading: 'Markierte Qualifikationen löschen?',
    deleteSkillsPopupWarningMessage: 'Möchten Sie die ausgewählten Qualifikationen dauerhaft löschen? {number} Qualifikationen werden aus dem System gelöscht und aus den Benutzerprofilen sowie aus allen sonstigen Bereichen, in denen die Qualifikationen angezeigt werden, entfernt.',
    keepSkillsButtonLabel: 'Qualifikationen übernehmen',
    deleteSkillsAndLevelsPopupHeading: 'Ausgewählte Qualifikationen und Qualifikationsniveaus löschen?',
    deleteSkillsAndLevelsPopupWarningMessage: 'Möchten Sie die ausgewählten Qualifikationen und Qualifikationsniveaus dauerhaft löschen?',
    deleteSkillsAndFieldsPopupHeading: 'Ausgewählte Qualifikationen und Qualifikationsfelder löschen?',
    deleteSkillsAndFieldsPopupWarningMessage: 'Möchten Sie die ausgewählten Qualifikationen und Qualifikationsfelder dauerhaft löschen?',
    deleteLevelsAndFieldsPopupHeading: 'Ausgewählte Qualifikationsniveaus und Qualifikationsfelder löschen?',
    deleteLevelsAndFieldsPopupWarningMessage: 'Möchten Sie die ausgewählten Qualifikationen, Qualifikationsniveaus und Qualifikationsfelder dauerhaft löschen?',
    deleteSkillsLevelsAndFieldsPopupHeading: 'Ausgewählte Qualifikationen, Qualifikationsniveaus und Qualifikationsfelder löschen?',
    deleteSkillsLevelsAndFieldsPopupWarningMessage: 'Möchten Sie die ausgewählten Qualifikationen, Qualifikationsniveaus und Qualifikationsfelder dauerhaft löschen?',
    deletePopupWarningMessage: 'Objekte werden aus diesem Qualifikationstyp gelöscht und aus den Ressourcenprofilen sowie aus allen sonstigen Bereichen, in denen sie angezeigt werden, entfernt.',
    deleteSkillsPopupConfirmation: 'Ich verstehe die Konsequenzen dieser Handlung',
    deleteItemsButtonLabel: 'Diese Objekte löschen',
    keepItemsButtonLabel: 'Diese Objekte übernehmen',
    deleteAndModifySkillFieldsPopupHeading: 'Änderungen speichern und ausgewählte Felder löschen',
    deleteAndModifySkillFieldsPopupWarningMessage: 'Möchten Sie die Änderungen speichern und die ausgewählten Qualifikationsfelder dauerhaft löschen? {number} Qualifikationsfelder werden aus diesem/den Qualifikationstyp(en) gelöscht und aus den Ressourcenprofilen sowie aus allen sonstigen Bereichen, in denen das Qualifikationsfeld angezeigt wird, entfernt.',
    deleteAndModifySkillLevelsPopupHeading: 'Änderungen speichern und ausgewählte Niveaus löschen',
    deleteAndModifySkillLevelsPopupWarningMessage: 'Möchten Sie die Änderungen speichern und die ausgewählten Qualifikationsniveaus dauerhaft löschen? Gelöschte Qualifikationsniveaus werden aus den Ressourcenprofilen und allen sonstigen Bereichen, in denen diese Qualifikationsniveaus angezeigt werden, entfernt.',
    deleteAndModifySkillsPopupHeading: 'Änderungen speichern und ausgewählte Qualifikationen löschen',
    deleteAndModifySkillsPopupWarningMessage: 'Möchten Sie die Änderungen speichern und die ausgewählten Qualifikationen dauerhaft löschen? {number} Qualifikationen werden aus dem System gelöscht und aus den Benutzerprofilen sowie aus allen sonstigen Bereichen, in denen die Qualifikationen angezeigt werden, entfernt.',
    deleteAndModifySkillsAndLevelsPopupHeading: 'Änderungen speichern und ausgewählte Qualifikationen und Qualifikationsniveaus löschen',
    deleteAndModifySkillsAndLevelsPopupWarningMessage: 'Möchten Sie die Änderungen speichern und die ausgewählten Qualifikationen und Qualifikationsniveaus dauerhaft löschen?',
    deleteAndModifySkillsAndFieldsPopupHeading: 'Änderungen speichern und ausgewählte Qualifikationen und Qualifikationsfelder löschen',
    deleteAndModifySkillsAndFieldsPopupWarningMessage: 'Möchten Sie die Änderungen speichern und die ausgewählten Qualifikationen und Qualifikationsfelder dauerhaft löschen?',
    deleteAndModifyLevelsAndFieldsPopupHeading: 'Änderungen speichern und ausgewählte Qualifikationsniveaus und Qualifikationsfelder löschen',
    deleteAndModifyLevelsAndFieldsPopupWarningMessage: 'Möchten Sie die Änderungen speichern und die ausgewählten Qualifikationsniveaus und Qualifikationsfelder dauerhaft löschen?',
    deleteAndModifySkillsLevelsAndFieldsPopupHeading: 'Änderungen speichern und ausgewählte Qualifikationen, Qualifikationsniveaus und Qualifikationsfelder löschen',
    deleteAndModifySkillsLevelsAndFieldsPopupWarningMessage: 'Möchten Sie die Änderungen speichern und die ausgewählten Qualifikationen, Qualifikationsniveaus und Qualifikationsfelder dauerhaft löschen?',
    skillCategoryDeletePopupHeading: 'Qualifikationstyp löschen?',
    skillCategoryDeletePopupWarningMessage: 'Möchten Sie diese Kompetenzkategorie löschen? Diese Kompetenzkategorie enthält {number} Kompetenzen, deren Verknüpfungen alle aufgehoben werden, und sämtliche Felder im Zusammenhang mit dieser Kategorie werden aus dem System gelöscht, ebenso wie alle Profile, die sie referenzieren.',
    deleteSkillCategoryButtonLabel: 'Qualifikationstyp löschen',
    keepSkillCategory: 'Qualifikationstyp übernehmen',
    fieldAlreadyExistHeader: 'Bitte geben Sie eine einzigartige Feldbezeichnung ein',
    fieldAlreadyExistDescription: 'Diese Feldbezeichnung existiert bereits. Bitte verwenden Sie das bereits existierende Feld oder geben Sie diesem Feld eine einzigartige Bezeichnung.',
    skillNamePlaceholder: 'Qualifikationsbezeichnung eingeben',
    skillDescriptionPlaceholder: 'Qualifikationsbeschreibung eingeben',
    skillNameHeader: 'Qualifikationsbezeichnung',
    fieldTypeHeader: 'Feldtyp',
    categoryText: 'Kategorie',
    defaultValueText: 'Standardwert',
    newTag: 'Neues Schlagwort',
    headerLevels: 'Niveaus',
    licenseCountSkill: '{activeCount} von {licensedCount} Qualifikationen werden für diesen Qualifikationstyp verwendet',
    skillsLicenseWarning: 'Sie sind nahe an der Obergrenze der für Ihre Lizenz zulässigen Anzahl an Kompetenzen. {contactUs}, um das Limit Ihres Plans zu erhöhen.',
    skillsLicenseError: 'Sie haben die zulässige Anzahl an Kompetenzen für Ihre Lizenz erreicht. {contactUs}, um das Limit Ihres Plans zu erhöhen.',
    licenseCountSkillLevel: '{activeCount} von {licensedCount} Niveaus werden für diesen Qualifikationstyp verwendet',
    skillLevelsLicenseWarning: 'Sie sind kurz vor dem gemäß Ihrer Lizenz zulässigen Niveaulimit für diesen Qualifikationstyp. {contactUs}, um das Limit für Ihren Vertrag zu erhöhen.',
    skillLevelsLicenseError: 'Sie haben das gemäß Ihrer Lizenz zulässige Niveaulimit für diesen Qualifikationstyp erreicht. {contactUs}, um das Limit für Ihren Vertrag zu erhöhen.',
    licenseCountSkillField: '{activeCount} von {licensedCount} Felder werden für diesen Qualifikationstyp verwendet',
    skillFieldsLicenseWarning: 'Sie sind kurz vor dem gemäß Ihrer Lizenz zulässigen Limit für Felder für diesen Qualifikationstyp. {contactUs}, um das Limit für Ihren Vertrag zu erhöhen.',
    skillFieldsLicenseError: 'Sie haben das gemäß Ihrer Lizenz zulässige Limit für Felder für diesen Qualifikationstyp erreicht. {contactUs}, um das Limit für Ihren Vertrag zu erhöhen.',
    adminCommandBarActionLabel: 'Aktionen',
    adminCommandBarEditLabel: 'Bearbeiten',
    adminCommandBarUserStatusLabel: 'Aktiver Nutzer',
    adminCommandBarSetActiveLabel: 'Auf aktiv setzen',
    adminCommandBarSetInactiveLabel: 'Auf inaktiv setzen',
    adminCommandBarResendEmailInviteLabel: 'Einladungs-E-Mail senden',
    adminCommandBarResetPassphraseLabel: 'Passwort zurücksetzen',
    disabledSkillType: 'Bitte speichern oder löschen Sie die Änderungen, ehe Sie fortfahren',
    adminCommandBarSendCMeSurveyLabel: 'C-me-Umfrage senden',
    // Skill Category
    skillCategoryRequired: 'Bitte geben Sie einen Kategorienamen ein',
    uniqueCategoryNameRequired: 'Bitte geben Sie einen eindeutigen Kategorienamen ein',
    primarySkillLabel: 'Primäre Kompetenz',
    secondarySkillLabel: 'Sekundäre Kompetenz',
    preferredSkillLabel: 'Bevorzugte Kompetenz',
    skillCategoryLabel: 'Kompetenzkategorie',
    importInProgressText: 'Kompetenzen werden aktuell aus der Retain-Bibliothek importiert',
    subscribed: 'Abonniert',
    notSubscribed: 'Nicht abonniert',
    selectedImportDataLabel: '<bold>${skillCount} Kompetenzen ausgewählt aus ${categoryCount} Kategorien</bold>',
    skillCategoryExpiryMandatoryInfo: 'Legen Sie „Ja“ fest, um den Ablauf der Qualifikation als Pflichtfeld festzulegen',
    skillExpiryEnabledInfo: 'DE_Set this to Yes to enable Skill expiry fields for this skill category_DE',
    importClickInfo: 'Durch Klicken auf <bold>Importieren</bold> wird diese Importaufgabe in die Warteschlange gestellt',
    skillCategorySubscribeInfo: 'Abonnieren, um über Änderungen an dieser Kompetenzkategorie benachrichtigt zu werden',
    importProgressInfo: 'Bitte prüfen Sie das <bold>Vorgangsprotokoll</bold>, um den Fortschritt dieser Aufgabe zu sehen',
    skillExpiryDateLabel: 'DE_Skill expiry is mandatory_DE',
    skillExpiryEnabledLabel: 'DE_Enable skill expiry dates_DE',
    preNotificationLabel: 'Benachrichtigung vor Ablauf',
    watcherLabel: 'Kompetenzkategorie-Beobachter',
    existingSkillsHeader: 'Kompetenzen, die bereits hinzugefügt wurden oder denselben Namen wie vorhandene Kompetenzen in Ihrem Unternehmen haben, werden aus dieser Liste ausgeblendet',

    //skill Library
    addSkillLibrary: 'Aus Retain-Bibliothek hinzufügen',
    skillLibraryHeader: 'Retain-Kompetenzbibliothek',
    importLibraryStep1: {
        title: 'Kompetenzen auswählen',
        description: 'Kompetenzen auswählen, die Sie importieren möchten'
    },
    importLibraryStep2: {
        title: 'Überprüfen',
        description: 'Von Ihnen ausgewählte Kompetenzen überprüfen'
    },
    importLibraryStep3: {
        title: 'Importieren',
        description: 'Bereit zum Importieren'
    },
    searchSkillPlaceholder:'DE_Search skill_DE',
    //Notifications
    notificationsPage: {
        commandBarConfig: {
            pageTitle: 'Benachrichtigungen'
        },
        notificationSettingSummary: {
            notificationSettingSubHeading: 'Wählen Sie, ob Benachrichtigungen nach ihrer Art angezeigt werden sollen.'
        },
        notificationSettingConfig: {
            onLabel: 'Ein',
            offLabel: 'Aus'
        },
        notificationEvents: {
            bookingAssignedLabel: 'Ihnen wurde von ${editorName} ${jobName} vom ${startDate} - ${endDate} ${timeAllocation} zugewiesen',
            bookingUpdateLabel: 'Ihre Buchung für ${jobName} vom ${startDate} - ${endDate} ${timeAllocation} wurde von ${editorName} bearbeitet',
            bookingDeleteLabel: '${jobName} vom ${startDate} - ${endDate} ${timeAllocation} ist Ihnen nicht mehr zugewiesen',
            roleRequestCreateLabel: '${editorName} hat ${resourceName} für ${jobName} ${startDate} - ${endDate} ${timeAllocation} angefragt',
            roleRequestRejectLabel: '${editorName} hat Ihre Anfrage für ${resourceName} für ${jobName} ${startDate} - ${endDate} ${timeAllocation} abgewiesen',
            roleRequestLiveLabel: '${editorName} hat Ihre Anfrage für ${resourceName} für ${jobName} ${startDate} - ${endDate} ${timeAllocation} aktiviert',
            repeatBookingAssignedLabel: 'Ihnen wurde von ${editorName} ${jobName} vom ${startDate} - ${endDate} ${timeAllocation} zugewiesen. Diese Buchung wiederholt sich alle ${interval} bis ${untilDate}',
            repeatBookingUpdateLabel: 'Ihre Buchung für ${jobName} vom ${startDate} - ${endDate} ${timeAllocation} wurde von ${editorName} bearbeitet. Diese Buchung wiederholt sich alle ${interval} bis ${untilDate}',
            repeatBookingDeleteLabel: '${jobName} vom ${startDate} - ${endDate} ${timeAllocation} ist Ihnen nicht mehr zugewiesen. Diese Buchung wiederholt sich alle ${interval} bis ${untilDate}',
            loadingSectionSuffix: '% der Arbeitsstunden',
            timeSectionSuffix: 'Stunden insgesamt gebucht',
            hoursPerDaySuffix: 'Stunden pro Tag',
            resourceSkillExpiryLabel: 'DE_You have skills set to expire in ${expiryDay} days ⏳ Renew it before the expiry date to keep your credentials up to date_DE',
            resourceManagerSkillExpiryLabel: 'DE_${resourceName} has skills set to expire in ${expiryDay} days ⏳ Ensure they renew it before the expiry date to keep your credentials up to date_DE',
            resourceSkillRecommendationLabel: 'DE_Update your skills and get noticed! Here are some skills you could add to your profile_DE',
            skillExpiryLabel: 'DE_${skillName} ${entityType} expires on ${expiryDate}_DE',
            extraSkillExpiryLabel: 'DE_${count} more expiring soon_DE'
        },
        notificationActions: {
            viewBookingLabel: '${bookingSingularLowerAlias} anzeigen',
            editBookingLabel: '${bookingSingularLowerAlias} bearbeiten',
            viewPlansLabel: 'Pläne anzeigen',
            viewRoleGroupLabel: 'In ${entitySingularLower} anzeigen',
            makeLiveLabel: 'Live schalten',
            markAsReadLabel: 'Als gelesen markieren',
            markAsUnreadLabel: 'Als ungelesen markieren',
            deleteLabel: 'Löschen'
        },
        notificationHistoryPageHeader: {
            informationMessage: 'Zeigt alle Benachrichtigungen an. Gelesene Benachrichtigungen werden nach 30 Tagen automatisch gelöscht',
            markAllReadLabel: 'Alle als gelesen markieren',
            loadMoreButtonLabel: 'Mehr anzeigen'
        },
        notificationTabs: {
            notificationHistoryLabel: 'Verlauf',
            notificationSettingsLabel: 'Einstellungen'
        },
        newNotificationTabs: {
            notificationHistoryLabel: 'DE_All_DE',
            notificationRequestLabel:'DE_Requests_DE',
            notificationSettingsLabel: 'Einstellungen'
        },
        notificationsSettings: {
            pageTitle: 'Mitteilungen',
            notificationSettingsHeader: 'Standardmitteilungen',
            notificationsSettingsAdminPageDescription: 'Legen Sie die Standardmitteilungseinstellungen für neue Benutzer fest. Benutzer können diese Einstellungen unter Mitteilungen > Einstellungen ihrem Bedarf entsprechend außer Kraft setzen',
            notificationsSettingsDescription: 'Wählen Sie, über was und wie Sie Mitteilungen erhalten möchten.',
            bookingNotifications: '${bookingSingularAlias} Mitteilungen',
            allNotifications: 'Alle Mitteilungen',
            bookingPost: 'Wenn Sie einer Gruppe zugeordnet sind ${bookingSingularAlias}',
            bookingPatch: 'Wenn ein Ihnen zugewiesener ${bookingSingularAlias} aktualisiert wird',
            bookingDelete: 'Wenn Sie nicht mehr einem ${bookingSingularAlias} zugewiesen sind oder dieser gelöscht wird',
            emailFrequency: 'E-Mail-Häufigkeit',
            roleNotificaitons: '${rolerequestSingularAlias} Mitteilungen',
            roleRequestCreate: 'Wenn ein ${resourceSingularAlias}, den Sie verwalten, angefordert wird',
            roleRequestReject: 'Wenn ein ${rolerequestSingularAlias}, den Sie erstellt haben, abgelehnt wird',
            roleRequestLive: 'Wenn ein ${rolerequestSingularAlias}, den Sie erstellt haben, live geschaltet wird',
            webApp: 'Web-App',
            email: 'E-Mail',
            globalNotificationSettingsHeaderTitle: 'Globale Benachrichtigungsoptionen',
            globalNotificationSettingsHeaderDescription: 'Benachrichtigungseinstellungen für alle Benutzer verwalten',
            globalNotificationSettingsToggleTitle: 'Benutzer über unbestätigte \n\nBuchungen benachrichtigen',
            globalNotificationSettingsToggleDescription: 'Unbestätigte Buchungen in Benachrichtigungen einschließen',
            resourceSkillNotifications: 'DE_Skill notifications_DE',
            resourceSkillExpiry: 'DE_When a skill you have is expiring_DE',
            resourceManagerSkillExpiry: 'DE_When a resource you manage has a skill that\'s expiring_DE',
            resourceSkillRecommendation: 'DE_Recommend skills to add to your profile based on your job title or primary skill_DE'
        }
    },

    //Timesheets Page
    timesheetsPage: {
        noTimesheetsAvailable: 'Sie haben keine Stunden erfasst',
        jobLabel: 'Auftrag',
        monday: 'Montag',
        tuesday: 'Dienstag',
        wednesday: 'Mittwoch',
        thursday: 'Donnerstag',
        friday: 'Freitag',
        saturday: 'Samstag',
        sunday: 'Sonntag',
        total: 'Gesamt',
        hours_logged: 'erfasste Stunden',
        commandBarConfig: {
            pageTitle: 'Stundenzettel'
        },
        hoursRequiredLabel: 'Das ist ein Pflichtfeld',
        maximumHourErrorLabel: 'Maximal 24',
        minimumHourErrorLabel: 'Mindestens 0',
        showTimesheetsLabel: 'Stundenzettel anzeigen ab',
        submitButtonLabel: 'Speichern',
        cancelButtonLabel: 'Abbrechen',
        timesheetDeletionWarning: 'Auftrag streichen?',
        timesheetDataDeletionMessage: 'Möchten Sie <bold>${jobDescription}</bold> und die damit verbundenen Stunden aus diesem Stundenzettel streichen?',
        timesheetCheckMessage: 'Ich verstehe, welche Auswirkungen das Löschen der Daten dieses Stundenzettels hat',
        deleteTimesheetPrimaryButton: 'Ja, den Auftrag streichen',
        deleteTimesheetSecondaryButton: 'Nein, den Auftrag übernehmen',
        addJobButtonLabel: 'Auftrag hinzufügen',
        applyJobButtonLabel: 'Hinzufügen',
        hoursAriaLabel: 'Stunden eingeben'
    },

    //Reporting settings Page
    reportingTitle: 'Berichtswesen',
    reportSettingPage: {
        datasetRefreshModalTitle: 'DE_Dataset refresh status_DE',
        lastRunLabel: 'DE_Last Run_DE',
        successStatus: 'DE_Dataset refresh completed successfully_DE',
        failureStatus: 'DE_Dataset refresh failed_DE',
        inProgressStatus: 'DE_Dataset refresh is in progress..._DE',
        cancelledStatus: 'DE_Dataset refresh was cancelled_DE',
        unknownStatus: 'DE_Unknown status_DE',
        datasetRefreshButton: 'DE_Refresh dataset_DE',
        cancelButton: 'DE_Cancel_DE'
    },

    //Report Page
    reportPage: {
        commandBarConfig: {
            pageTitle: 'Berichte'
        },
        pageTitle: 'Berichte',
        editLabel: 'Bearbeiten',
        viewLabel: 'Anzeigen',
        printLabel: 'Drucken',
        reportsIHaveCreatedLabel: 'Meine erstellten Berichte',
        sharedReportsItemLabel: 'Mit mir geteilte Berichte',
        manageMyReportsItemLabel: 'Meine Berichte verwalten ...',
        reportNoEditPermissions: 'Unzureichende Berechtigungen zur Bearbeitung dieses Berichts',
        emptyReportTitle: 'Keine Berichte gefunden',
        emptyReportPageText: 'Es sind keine Berichte zur Anzeige vorhanden',
        sharedReportsSectionTitle: 'Geteilte Berichte, die ich bearbeiten kann',
        reportDetailsTitle: 'Berichtdetails',
        systemInfoTitle: 'Systeminformationen',
        updatedByLabel: 'Aktualisiert von',
        createdByLabel: 'Erstellt von',
        createdOnLabel: 'Erstellt am',
        updatedOnLabel: 'Aktualisiert am',
        nameLabel: 'Name',
        reportAccessLabel: 'Berichtszugriff',
        emptyReportDetailsText: 'Beginnen Sie mit der Erstellung von Berichten, damit sie hier angezeigt werde.',
        modalHeaderTitle: 'Meine Berichte verwalten',
        newReportButtonLabel: 'Neuer Bericht',
        newReportButtonTooltip: 'Maximal ${licenseCount} Berichte sind pro Mandant zulässig. Ohne Lesezugriff werden möglicherweise nicht alle Berichte angezeigt.',
        deleteButtonLabel: 'Ausgewählten Bericht löschen',
        saveChangesButtonLabel: 'Änderungen speichern',
        discardButtonLabel: 'Änderungen verwerfen',
        cancelButtonLabel: 'Abbrechen',
        pendingDeleteButtonLabel: 'Wird gelöscht ...',
        pendingSaveButtonLabel: 'Wird gespeichert ...',
        unsavedChangesLabel: 'Nicht gespeicherte Änderungen',
        youHaveUnsavedChangesLine: 'Dieser Bericht enthält nicht gespeicherte Änderungen. Möchten Sie die Seite trotzdem verlassen?',
        allProfilesLabel: 'Alle Profile',
        readOnlyLabel: 'Schreibgeschützt',
        editAccessLabel: 'Zugriff bearbeiten',
        helpTextLabel: 'Ersteller können ihre Berichte stets anzeigen und bearbeiten',
        maxLimitReachedLabel: 'Max. 20 ${resourcePluralLowerAlias} oder Sicherheitsprofile',
        reportAccessEntityPicker: {
            searchLabel: 'Suchen',
            applyButtonText: 'Übernehmen',
            notLoadedLabel: 'Nicht geladen',
            notLoadedValueLabel: 'Nicht geladener Wert',
            showMoreButtonText: 'Mehr anzeigen'
        },
        deleteReportPrompt: {
            title: 'Bericht „${reportName}“ löschen?',
            confirmDeleteLabel: 'Ja, Bericht löschen',
            declineDeleteLabel: 'Nein, Bericht beibehalten',
            createdByLine: 'Dieser Bericht wurde von ${createdBy} erstellt.',
            permanentlyDeleteLine: 'Soll dieser Bericht dauerhaft gelöscht werden?',
            youLabel: 'Sie'
        },
        toasterMessages: {
            create: 'Bericht erstellt',
            delete: 'Bericht gelöscht',
            edit: 'Bericht bearbeitet',
            save: 'Bericht gespeichert'
        },
        selectLabel: 'Auswählen',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        securityProfilesLabel: 'Sicherheitsprofile',
        emptyNameError: 'Geben Sie einen Namen ein',
        duplicateNameError: 'Dieser Name wird bereits verwendet. Geben Sie einen eindeutigen Namen ein.'
    },

    //Callback component
    callbackComponentText: 'Weiterleiten...',

    // Planner/Jobs page
    plannerPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            pageTitle: 'Planer',
            addLabel: 'Hinzufügen',
            editLabel: 'Bearbeiten',
            editRoleByNameButtonLabel: '${rolerequestPluralCapitalAlias} nach Bezeichnung bearbeiten',
            editRoleByCriteriaButtonLabel: '${rolerequestPluralCapitalAlias} nach Anforderungen bearbeiten',
            viewLabel: 'Anzeigen',
            barsLabel: 'Leistenoptionen',
            showLabel: 'Anzeigen',
            increaseDateRangeLabel: 'Datumsbereich erweitern',
            decreaseDateRangeLabel: 'Datumsbereich verkürzen',
            dateRangeLabel: 'Datumsbereich',
            goToTodayLabel: 'Gehe zu heute',
            goToDateLabel: 'Gehe zu Datum',
            jobsLabel: 'Aufträge',
            resourcesLabel: 'Ressourcen',
            filtersLabel: 'Filter',
            bookingLabel: 'Buchung',
            roleLabel: 'Position',
            jobLabel: 'Auftrag',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} aus einer Vorlage',
            clientLabel: 'Kunde',
            cutLabel: 'Ausschneiden',
            copyLabel: 'Kopieren',
            pasteLabel: 'Einfügen',
            restartLabel: 'Neustarten',
            archiveLabel: 'Archivieren',
            rejectLabel: 'Ablehnen',
            makeLiveLabel: 'Live schalten',
            submitRequestLabel: 'Anforderung übermitteln',
            createLabel: 'Erstellen',
            deleteLabel: 'Löschen',
            manageLabel: 'Verwalten',
            manageRoleTemplatesLabel: 'Vorlagen ${rolerequestSingularLowerAlias} verwalten',
            moreLabel: 'Mehr',
            newWorkspaceLabel: 'DE_New Workspace_DE',
            saveAsNewWorkspaceLabel: 'DE_Save as a new workspace_DE',
            manageMyWorkspacesLabel: 'DE_Manage My Workspaces_DE',
            privateWorkspacesLabel: 'DE_Private Workspaces_DE',
            publicWorkspacesLabel: 'DE_Public Workspaces_DE',
            noPublicWorkspacesCreatedLabel: 'DE_No public workspaces have been created_DE',
            noPrivateWorkspacesCreatedLabel: 'DE_No private workspaces have been created_DE',
            newPlanLabel: 'Neuer Plan',
            saveAsNewPlanLabel: 'Als neuen Plan speichern',
            manageMyPlansLabel: 'Meine Pläne verwalten',
            privatePlansLabel: 'Private Pläne',
            publicPlansLabel: 'Öffentliche Pläne',
            saveChangesLabel: 'Änderungen speichern',
            saveChangesToPublicLabel: 'Änderungen unter „Öffentlich“ speichern',
            noPublicPlansCreatedLabel: 'Es wurden noch keine öffentlichen Pläne erstellt',
            noPrivatePlansCreatedLabel: 'Es wurden noch keine privaten Pläne erstellt',
            noRoleTemplatesCreatedLabel: 'Es wurden keine Vorlagen hinzugefügt',
            customDateRangeLabel: 'Benutzerdefinierter Datumsbereich',
            dayLabel: 'Tag',
            '5daysLabel': '5 Tage',
            '7daysLabel': '7 Tage',
            '10daysLabel': '10 Tage',
            weekLabel: 'Woche',
            '2weeksLabel': '2 Wochen',
            '4weeksLabel': '4 Wochen',
            '6weeksLabel': '6 Wochen',
            monthLabel: 'Monat',
            '2monthsLabel': '2 Monate',
            '3monthsLabel': '3 Monate',
            '6monthsLabel': '6 Monate',
            yearLabel: 'Jahr',
            weekendsLabel: 'Wochenenden',
            potentialConflictsLabel: 'Potenzielle Konflikte anzeigen',
            baseFilterLabel: 'Aufträge anzeigen',
            rollForwardLabel: 'Duplizieren',
            rollForwardTooltipText: 'Ausgewählten ${bookingEntityAlias} in einen anderen ${jobEntityAlias} oder ein anderes Datum kopieren',
            byNameSuffix: 'nach Bezeichnung',
            byRequirementSuffix: 'nach Anforderungen',
            findResourcesLabel: '${resourceEntityAlias} suchen...',
            findResourceToolTipText: '${resourceEntityAlias} auf Grundlage von Kriterien (f) suchen',
            showMenuTooltipText: 'Nicht zugewiesene ${rolePluralLowerCase} sind jetzt standardmäßig ausgeblendet. Verwenden Sie das Menü „Anzeigen“, um das zu ändern.',
            showInViewLabel: 'In der Ansicht ${pluralViewNameAlias} anzeigen',
            restorePlansLabel: 'Ansicht zurücksetzen',
            restorePlanTooltipText: 'Setzen Sie die aktuelle Ansicht in den Ursprungszustand zurück. Verwenden Sie das Dropdown-Menü rechts, um diese Ansicht als Plan zu speichern.'
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: 'Zurückliegende ${subRowEntityAlias}',
            hideFutureEntityLabel: 'Zukünftige ${subRowEntityAlias}',
            hideUnassignedRowsEntityLabel: 'Nicht zugewiesene Zeilen',
            hideUnassignedBookingsEntityLabel: 'DE_Unassigned ${bookingPluralLowerCase}_DE',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inaktiv ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Zeige ${subRowEntityAlias} an, für die/den es nur ${bookingPluralCapitalized} gibt, die heute oder früher enden',
            hideFutureEntitiesExplanation: 'Zeige ${subRowEntityAlias} an, für die/den es nur ${bookingPluralCapitalized} gibt, die später als am Ende des sichtbaren Datumsbereichs beginnen',
            hideRolesExplanation: 'Zeige ${subRowEntityAlias} an, für die es ${rolePluralLowerCase} gibt',
            hideDraftRolesExplanation: 'Zeige ${roleSingularCapitalized} Entwürfe an',
            hideRequestedRolesExplanation: 'Zeige ${rolePluralCapitalized}-Anfragen, die aktive ${bookingPluralCapitalized} werden könnten',
            toggleShowUnassignedRoles: 'Nicht zugewiesene ${rolePluralLowerCase}',
            toggleShowRolesByName: '${rolePluralCapitalized} nach Bezeichnung',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} nach Anforderungen',
            hideJobTimelineToggleLabel: '${jobEntityAlias} Zeitstrahl',
            hideJobMilestonesToggleLabel: '${jobEntityAlias} Meilensteine',
            hideJobTimelineExplanation: 'Start- und Enddaten für ${jobEntityAlias} anzeigen',
            hideJobMilestonesExplanation: 'Spezifische Daten für ${jobEntityAlias} anzeigen'
        },
        selectionBar: {
            editAllButtonLabel: 'Alle bearbeiten',
            deleteAllButtonLabel: 'Alle löschen',
            editButtonLabel: 'Bearbeiten',
            editRoleByNameButtonLabel: '${rolerequestPluralCapitalAlias} nach Bezeichnung bearbeiten',
            editRoleByCriteriaButtonLabel: '${rolerequestPluralCapitalAlias} nach Anforderungen bearbeiten',
            deleteButtonLabel: 'Löschen',
            archiveAllButtonLabel: 'Alle archivieren',
            archiveButtonLabel: 'Archivieren',
            restartAllButtonLabel: 'Alle neustarten',
            restartButtonLabel: 'Neustarten',
            submitRequestAllButtonLabel: 'Anforderung übermitteln',
            submitRequestButtonLabel: 'Anforderung übermitteln',
            createButtonLabel: 'Buchung erstellen', // Ist bei der Implementierung von Vorgängen zum Erstellen und Anfragen in die verwendeten Objekt-Aliasse zu ändern
            makeLiveSingularButtonLabel: 'Live schalten',
            makeLivePluralButtonLabel: 'Live schalten',
            insufficientRightsToEditAndDelete: 'Keine erforderliche Berechtigung zum Bearbeiten / Löschen dieses ${entityAlias}',
            insufficientActionRights: 'Keine erforderliche Berechtigungen zur Durchführung der Aktion für alle ${entityAlias}',
            selectedLabel: 'ausgewählte(s)',
            maxBookingsSuffix: 'Max.',
            rollForwardLabel: 'Duplizieren',
            rollForwardTooltipText: 'Ausgewählten ${bookingEntityAlias} in einen anderen ${jobEntityAlias} oder ein anderes Datum kopieren‘',
            showInView: 'In der Ansicht ${pluralViewNameAlias} anzeigen'
        },
        barOptions: {
            defaultLabel: 'Standard',
            mediumLabel: 'Medium',
            expandedLabel: 'Erweitert'
        },
        showLabelsOnBarsLabel: 'Beschriftung auf Leisten anzeigen',
        legendLabel: 'Legende',
        barFieldsLabel: '${barSingularAlias} Balken-Felder',
        colourSchemeLabel: 'Farbthema',
        customColourThemeLabel: 'Benutzerdefiniertes Farbthema',
        customColourSchemeLabel: 'Benutzerdefiniertes ${barSingularAlias}-Farbthema',
        editedSuffix: 'bearbeitet',
        createdSuffix: 'erstellt',
        deletedSuffix: 'gelöscht',
        archivedSuffix: 'archiviert',
        restartedSuffix: 'neugestartet',
        rejectedSuffix: 'abgelehnt',
        requestedSuffix: 'angefragt',
        liveSuffix: 'weitergeleitet zu',
        publishedRoleSuffix: 'veröffentlicht in ${marketplaceAlias}',
        scheduleRoleForPublishingSuffix: 'geplant für die Veröffentlichung in ${marketplaceAlias}',
        publicationEditedSuffix: 'Veröffentlichung bearbeitet',
        publicationRemovedSuffix: 'aus der Liste der Positionen entfernt',
        applyButtonText: 'Anwenden',
        searchLabel: 'Suchen',
        notLoadedLabel: 'nicht geladen',
        notLoadedValueLabel: 'nicht geladener Wert',
        goToPageLabel: 'Gehe zu Seite',
        legend: {
            legendTitle: 'Legende',
            coloursColumnSubTitle: 'Die Balkenfarben basieren auf den',
            barTypes: {
                draftRoles: '${rolerequestSingularCapitalAlias}-Entwürfen',
                roleRequestsToLiveBookings: 'Zeige ${rolerequestSingularCapitalAlias}-Anfragen, die aktive ${bookingPluralLowerAlias} werden könnten',
                unconfirmed: 'Unbestätigt',
                planned: 'Geplant',
                excludesNonWorkingDays: 'Schließt arbeitsfreie Tage aus',
                includesNonWorkingDays: 'Schließt arbeitsfreie Tage ein',
                inConflict: 'Es besteht ein Konflikt',
                startDateNonWorking: 'Startdatum an ausgeblendetem Wochenende',
                endDateNonWorking: 'Enddatum an ausgeblendetem Wochenende',
                bothDatesNonWorking: 'Start- und Enddatum an ausgeblendetem Wochenende',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} Anfragen, die aktiv geschaltet wurden ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Farbschema',
                barTypesTabTitle: 'Balkentypen',
                milestonesTabTitle: '${jobSingularAlias} Details'
            },
            jobDetailsLabels: {
                milestonesColumnTitle: 'Meilensteine',
                timelineColumnTitle: 'Zeitstrahl',
                linesColumnTitle: 'Zeilen',
                normalLineLabel: '${jobSingularAliasLinesSection} mit Start- und Enddaten',
                dashedLineLabel: '${jobSingularAliasLinesSection} mit fehlenden Start- und/oder Enddaten',
                lineEndsColumnTitle: 'Zeilenende',
                onscreenDatesLabel: 'Start-/Enddatum ${jobSingularAliasLineEndsSection} auf dem Bildschirm',
                offscreenDatesLabel: 'Start-/Enddatum ${jobSingularAliasLineEndsSection} außerhalb des Bildschirms',
                statesColumnTitle: 'Status',
                incompletedStateLabel: 'Unerledigt',
                completedStateLabel: 'Erledigt',
                overduedStateLabel: 'Überfällig'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'Keine Farbregeln hinzugefügt für'
        },
        plans: {
            manageMyPlansLabel: 'Meine Pläne verwalten',
            newPlanLabel: 'Neuer Plan',
            privatePlansColumnTitle: 'Meine Pläne',
            copyPlanLabel: 'Plan kopieren',
            readOnlyLabel: 'Schreibgeschützt',
            editAccessLabel: 'Zugriff bearbeiten',
            renameLabel: 'Umbenennen',
            deleteLabel: 'Löschen',
            moveToPublicLabel: 'Nach „Öffentlich“ verschieben',
            makeCopyLabel: 'Eine Kopie erstellen',
            makePublicCopyLabel: 'Eine öffentliche Kopie erstellen',
            makePrivateCopyLabel: 'Eine private Kopie erstellen',
            moveToPrivateLabel: 'Nach „Privat“ verschieben',
            privatePlansLabel: 'Private Pläne',
            publicPlansLabel: 'Öffentliche Pläne',
            manageMyWorkspacesLabel: 'DE_Manage My Workspaces_DE',
            newWorkspaceLabel: 'DE_New Workspace_DE',
            privateWorkspacesColumnTitle: 'DE_My Workspaces_DE',
            privateWorkspacesLabel: 'DE_Private Workspaces_DE',
            publicWorkspacesLabel: 'DE_Public Workspaces_DE',
            copyWorkspaceLabel: 'DE_Copy workspace_DE',
            editWorkspaceLabel: 'DE_Edit workspace_DE'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'Buchen ${entityAlias}',
            addBookingToJobRecordListCaption: 'Buchen auf ${entityAlias}',
            searchLabel: 'Suche',
            sortLabel: 'Sortieren',
            sortyByLabel: 'Sortieren nach',
            columnsLabel: 'Spalten',
            applyButtonText: 'Anwenden',
            detailsLabel: 'Details',
            notLoadedLabel: 'nicht geladen',
            notLoadedValueLabel: 'nicht geladener Wert',
            historyFieldPlaceholder: 'Nicht spezifiziert',
            pastLabel: 'zurückliegend',
            lastLoginLabel: 'Letzte Anmeldung',
            expandAndCollapseText: 'Zeile expandieren oder reduzieren',
            expandAllCaption: 'Alles expandieren',
            collapseAllCaption: 'Alles reduzieren',
            sortLabelButton: 'Sortieren ${order}',
            resourcesLabel: 'DE_Resources_DE',
            jobsLabel: 'DE_Jobs_DE',
            calculatingSortCalcFields: 'DE_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_DE',
            numberResults: 'DE_${rowCount} results_DE',
            resourceLabel: 'DE_Resource_DE',
            jobLabel: 'DE_Job_DE'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Zuweisungsänderung',
            outsideJobDatesLabel: 'Außerhalb von Auftragsdaten',
            datesConflictWithBookingLabel: 'Terminkonflikt mit Buchung',
            bookingConflictLabel: 'Buchungskonflikt',
            inactiveResourceLabel: 'ist inaktiv. Auf eine aktive oder nicht vergebene Ressource setzen, um Änderungen zu speichern',
            fromLabel: 'Von',
            toLabel: 'Bis',
            noDiaryAssignmentLabel: 'Kein Terminkalender zugewiesen',
            selectMultipleBarsHintPrefix: 'Auf „+“ klicken,',
            selectMultipleBarsHint: 'um mehrere',
            splitBookingBarHintPrefix: 'Taste S gedrückt halten,',
            splitBookingBar: 'um zu teilen ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            customDateRangeLabel: 'benutzerdefinierte Datumsbereiche auszuwählen',
            goToLabel: 'Gehe zu',
            todayLabel: 'Heute',
            dayLabel: 'Tag',
            '5daysLabel': '5 Tage',
            '7daysLabel': '7 Tage',
            '10daysLabel': '10 Tage',
            weekLabel: 'Woche',
            '2weeksLabel': '2 Wochen',
            '4weeksLabel': '4 Wochen',
            '6weeksLabel': '6 Wochen',
            monthLabel: 'Monat',
            '2monthsLabel': '2 Monate',
            '3monthsLabel': '3 Monate',
            '6monthsLabel': '6 Monate',
            yearLabel: 'Jahr',
            customLabel: 'Benutzerdefiniert',
            weekendsLabel: 'Wochenenden',
            prevLabel: 'Weiter',
            nextLabel: 'Zurück'
        },
        multiSelectionTooltip: {
            selectionTooltip: '${entityCount} ${entityAlias} ausgewählt',
            mixedSelectionTooltip: '${bookingsCount} ${bookingAlias} und ${rolesCount} ${rolerequestAlias} ausgewählt'
        },
        multiSelectionAlert: {
            message: 'Limit erreicht',
            description: 'Sie haben das Auswahllimit von ${maximumItemsCount} Artikeln erreicht'
        }
    },
    jobsPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Hinzufügen',
            jobsLabel: 'Aufträge',
            filtersLabel: 'Filter',
            manageLabel: 'Verwalten',
            editLabel: 'Bearbeiten',
            jobLabel: 'Auftrag',
            duplicateLabel: 'Duplizieren',
            clientLabel: 'Kunde',
            editDetailsLabel: 'Details bearbeiten',
            baseFilterLabel: 'Anzeigen',
            viewAllJobsLabel: 'Alle',
            viewJobsIManageLabel: 'Ich verwalte',
            viewJobsActionRequiredLabel: 'Erforderliche Aktion',
            staticMessageAddJobsMenu: 'DE_Resources can be added via User management_DE'
        }
    },
    resourcesPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Hinzufügen',
            jobsLabel: 'Aufträge',
            filtersLabel: 'Filter',
            manageLabel: 'Verwalten',
            editLabel: 'Bearbeiten',
            jobLabel: 'Auftrag',
            duplicateLabel: 'Duplizieren',
            clientLabel: 'Kunde',
            editDetailsLabel: 'Details bearbeiten',
            baseFilterLabel: 'Anzeigen',
            viewAllJobsLabel: 'Alle',
            viewJobsIManageLabel: 'Ich verwalte',
            viewJobsActionRequiredLabel: 'Erforderliche Aktion'
        }
    },
    roleInboxPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Hinzufügen',
            editLabel: 'Bearbeiten',
            editRoleByNameButtonLabel: '${rolerequestPluralCapitalAlias} nach Bezeichnung bearbeiten',
            editRoleByCriteriaButtonLabel: '${rolerequestPluralCapitalAlias} nach Anforderungen bearbeiten',
            deleteLabel: 'Löschen',
            filtersLabel: 'Filter',
            rolesLabel: 'Positionen',
            showLabel: 'Mehr',
            archiveLabel: 'Archivieren',
            restartLabel: 'Neustarten',
            rejectLabel: 'Ablehnen',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} aus einer Vorlage',
            noRoleTemplatesCreatedLabel: 'Es wurden keine Vorlagen hinzugefügt',
            createLabel: 'Erstellen',
            makeLiveLabel: 'Live schalten',
            submitRequestLabel: 'Anforderung übermitteln',
            byNameSuffix: 'nach Name',
            byRequirementSuffix: 'nach Anforderungen',
            unassignFromRoleLabel: 'Zuweisung trennen für',
            toggleShowUnassignedRoles: 'Nicht zugewiesene ${rolePluralLowerCase}',
            toggleShowRolesByName: '${rolePluralCapitalized} nach Bezeichnung',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} nach Anforderungen',
            manageRoleTemplatesLabel: 'Vorlagen ${rolerequestSingularLowerAlias} verwalten',
            publishToMarketplaceLabel: 'Veröffentlichen in ${marketplaceAlias}',
            editRolePublicationButtonLabel: 'Veröffentlichung ${rolerequestSingularLowerAlias} bearbeiten',
            removeRolePublicationButtonLabel: 'Veröffentlichung ${rolerequestSingularLowerAlias} entfernen'
        }
    },
    marketplacePage: {
        roleCard: {
            startsOnLabel: '${rolerequestSingularCapitalAlias} Beginnt am',
            categoryLabel: 'Kategorie',
            availabilityLabel: 'Ihre Verfügbarkeit für ${rolerequestSingularLowerAlias} ist',
            numberOfResources: 'Anzahl der ${resourcePluralLowerAlias}',
            numberOfFte: 'FTEs',
            systemDetails: 'Veröffentlicht am ${publishedOn} (Aktualisiert am ${updatedOn})',
            pendingResourcesNeededText: '${pendingResources} benötigt werden',
            defaultRoleName: 'Neue Position',
            notAvailableLabel: 'Nicht verfügbar'
        },
        commandBarConfig: {
            allLabel: 'Alle',
            filtersLabel: 'Filter',
            marketplaceLabel: 'Rollenübersicht',
            appliedAllLabel: 'Alle',
            appliedToLabel: 'Ich habe mich beworben für',
            availableForLabel: 'Ich bin verfügbar für'
        },
        entityWindow: {
            roleApplicationSubmitted: 'Bewerbung eingereicht',
            roleApplicationWithdrawn: 'Bewerbung wurde zurückgezogen'
        }
    },
    previewEntityPage: {
        sharePopoverTitle: 'Teilen ${roleAlias}'
    },
    tableViewPage: {
        commandBarConfig: {
            pageTitle: 'Tabellenansicht',
            addLabel: 'Hinzufügen',
            editLabel: 'Bearbeiten',
            viewLabel: 'Anzeigen',
            showLabel: 'Anzeigen',
            increaseDateRangeLabel: 'Datumsbereich erweitern',
            decreaseDateRangeLabel: 'Datumsbereich verkürzen',
            dateRangeLabel: 'Datumsbereich',
            goToTodayLabel: 'Gehe zu heute',
            goToDateLabel: 'Gehe zu Datum',
            jobsLabel: 'Aufträge',
            resourcesLabel: 'Ressourcen',
            filtersLabel: 'Filter',
            bookingLabel: 'Buchung',
            roleLabel: 'Rolle',
            jobLabel: 'Auftrag',
            clientLabel: 'Kunde',
            cutLabel: 'Ausschneiden',
            copyLabel: 'Kopieren',
            pasteLabel: 'Einfügen',
            createLabel: 'Erstellen',
            deleteLabel: 'Löschen',
            manageLabel: 'Verwalten',
            moreLabel: 'Mehr',
            newPlanLabel: 'Neuer Plan',
            saveAsNewPlanLabel: 'Als neuen Plan speichern',
            manageMyPlansLabel: 'Meine Pläne verwalten',
            privatePlansLabel: 'Private Pläne',
            publicPlansLabel: 'Öffentliche Pläne',
            saveChangesLabel: 'Änderungen speichern',
            saveChangesToPublicLabel: 'Änderungen unter „Öffentlich“ speichern',
            noPublicPlansCreatedLabel: 'Es wurden noch keine öffentlichen Pläne erstellt',
            noPrivatePlansCreatedLabel: 'Es wurden noch keine privaten Pläne erstellt',
            customDateRangeLabel: 'Benutzerdefinierter Datumsbereich',
            dayLabel: 'Tag',
            '5daysLabel': '5 Tage',
            '7daysLabel': '7 Tage',
            '10daysLabel': '10 Tage',
            weekLabel: 'Woche',
            '2weeksLabel': '2 Wochen',
            '4weeksLabel': '4 Wochen',
            '6weeksLabel': '6 Wochen',
            monthLabel: 'Monat',
            '2monthsLabel': '2 Monate',
            '3monthsLabel': '3 Monate',
            '6monthsLabel': '6 Monate',
            yearLabel: 'Jahr',
            weekendsLabel: 'Wochenenden',
            potentialConflictsLabel: 'Potenzielle Konflikte anzeigen',
            baseFilterLabel: 'Aufträge anzeigen',
            findResourcesLabel: '${resourceEntityAlias} suchen...',
            findResourceToolTipText: '${resourceEntityAlias} auf Grundlage von Kriterien (F) suchen',
            showMenuTooltipText: '' // This is empty to prevent the Show menu tooltip from showing on tableview page as it is not needed for now.
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: 'Zurückliegende ${subRowEntityAlias}',
            hideFutureEntityLabel: 'Zukünftige ${subRowEntityAlias}',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inaktiv ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Zeige ${subRowEntityAlias} an, für die/den es nur ${bookingPluralCapitalized} gibt, die heute oder früher enden',
            hideFutureEntitiesExplanation: 'Zeige ${subRowEntityAlias} an, für die/den es nur ${bookingPluralCapitalized} gibt, die später als am Ende des sichtbaren Datumsbereichs beginnen'
        },
        selectionBar: {
            editAllButtonLabel: 'Alle bearbeiten',
            deleteAllButtonLabel: 'Alle löschen',
            editButtonLabel: 'Bearbeiten',
            deleteButtonLabel: 'Löschen',
            createButtonLabel: 'Buchung erstellen', // Ist bei der Implementierung von Vorgängen zum Erstellen und Anfragen in die verwendeten Objekt-Aliasse zu ändern
            makeLiveSingularButtonLabel: 'Live schalten',
            makeLivePluralButtonLabel: 'Live schalten',
            insufficientRightsToEditAndDelete: 'Keine erforderliche Berechtigung zum Bearbeiten / Löschen dieses ${entityAlias}',
            insufficientActionRights: 'Keine erforderliche Berechtigungen zur Durchführung der Aktion für alle ${entityAlias}',
            selectedLabel: 'ausgewählte(s)',
            maxBookingsSuffix: 'Max.'
        },
        legendLabel: 'Legende',
        barFieldsLabel: '${barSingularAlias} Balken-Felder',
        colourSchemeLabel: 'Farbthema',
        customColourThemeLabel: 'Benutzerdefiniertes Farbthema',
        customColourSchemeLabel: 'Benutzerdefiniertes ${barSingularAlias}-Farbthema',
        editedSuffix: 'bearbeitet',
        createdSuffix: 'erstellt',
        deletedSuffix: 'gelöscht',
        applyButtonText: 'Anwenden',
        searchLabel: 'Suchen',
        notLoadedLabel: 'nicht geladen',
        notLoadedValueLabel: 'nicht geladener Wert',
        legend: {
            legendTitle: 'Legende',
            coloursColumnSubTitle: 'Die Balkenfarben basieren auf den',
            barTypes: {
                draftRoles: '${rolerequestSingularCapitalAlias}-Entwürfen',
                roleRequestsToLiveBookings: 'Zeige ${rolerequestSingularCapitalAlias}-Anfragen, die aktive ${bookingPluralLowerAlias} werden könnten',
                unconfirmed: 'Unbestätigt',
                planned: 'Geplant',
                excludesNonWorkingDays: 'Schließt arbeitsfreie Tage aus',
                includesNonWorkingDays: 'Schließt arbeitsfreie Tage ein',
                inConflict: 'Es besteht ein Konflikt',
                startDateNonWorking: 'Startdatum an ausgeblendetem Wochenende',
                endDateNonWorking: 'Enddatum an ausgeblendetem Wochenende',
                bothDatesNonWorking: 'Start- und Enddatum an ausgeblendetem Wochenende',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} Anfragen, die aktiv geschaltet wurden ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Farbschema',
                barTypesTabTitle: 'Balkentypen',
                milestonesTabTitle: '${jobSingularAlias} Details'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'Keine Farbregeln hinzugefügt für'
        },
        plans: {
            manageMyPlansLabel: 'Meine Pläne verwalten',
            newPlanLabel: 'Neuer Plan',
            privatePlansColumnTitle: 'Meine Pläne',
            copyPlanLabel: 'Plan kopieren',
            editPlanLabel: 'Plan bearbeiten',
            readOnlyLabel: 'Schreibgeschützt',
            editAccessLabel: 'Zugriff bearbeiten',
            renameLabel: 'Umbenennen',
            deleteLabel: 'Löschen',
            moveToPublicLabel: 'Nach „Öffentlich“ verschieben',
            makeCopyLabel: 'Eine Kopie erstellen',
            makePublicCopyLabel: 'Eine öffentliche Kopie erstellen',
            makePrivateCopyLabel: 'Eine private Kopie erstellen',
            moveToPrivateLabel: 'Nach „Privat“ verschieben',
            privatePlansLabel: 'Private Pläne',
            publicPlansLabel: 'Öffentliche Pläne'
        },
        recordsList: {
            addBookingToJobRecordListCaption: 'Buchen ${entityAlias}',
            addBookingToJobLabel: '${entityAlias} buchen für ${jobName}',
            addBookingToResourceRecordListCaption: 'Buchen auf ${entityAlias}',
            addBookingToResourceLabel: 'Auf ${entityAlias} buchen für ${resourceName}',
            searchLabel: 'Suchen',
            sortLabel: 'Sortieren',
            sortyByLabel: 'Sortieren nach',
            columnsLabel: 'Spalten',
            applyButtonText: 'Anwenden',
            detailsLabel: 'Details',
            notLoadedLabel: 'nicht geladen',
            notLoadedValueLabel: 'nicht geladener Wert',
            historyFieldPlaceholder: 'Nicht spezifiziert',
            pastLabel: 'zurückliegend',
            lastLoginLabel: 'Letzte Anmeldung',
            expandAndCollapseText: 'Zeile aus- und einklappen',
            expandAllCaption: 'Alle ausklappen',
            collapseAllCaption: 'Alle einklappen',
            sortLabelButton: 'Sortieren ${order}',
            resourcesLabel: 'DE_resources_DE',
            jobsLabel: 'DE_jobs_DE',
            calculatingSortCalcFields: 'DE_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_DE',
            resourceLabel: 'DE_Resource_DE',
            jobLabel: 'DE_Job_DE'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Zuweisungsänderung',
            outsideJobDatesLabel: 'Außerhalb von Auftragsdaten',
            datesConflictWithBookingLabel: 'Terminkonflikt mit Buchung',
            bookingConflictLabel: 'Buchungskonflikt',
            inactiveResourceLabel: 'ist inaktiv. Auf eine aktive oder nicht vergebene Ressource setzen, um Änderungen zu speichern',
            fromLabel: 'Von',
            toLabel: 'Bis',
            noDiaryAssignmentLabel: 'Kein Terminkalender zugewiesen',
            selectMultipleBarsHintPrefix: 'Auf „+“ klicken,',
            selectMultipleBarsHint: 'um mehrere',
            splitBookingBarHintPrefix: 'Taste S gedrückt halten,',
            splitBookingBar: 'um zu teilen ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            goToLabel: 'Gehe zu Datum',
            todayLabel: 'Heute',
            monthLabel: 'Monat',
            '1monthsLabel': '1 Monat',
            '2monthsLabel': '2 Monate',
            '3monthsLabel': '3 Monate',
            '4monthsLabel': '4 Monate'
        }
    },
    rolegroupListPage: {
        actionRequiredLabel: 'Maßnahme erforderlich',
        rolesLabel: 'Rolle',
        emptyStateBoldLabel: 'Keine Szenarien',
        emptyStateLabel: 'Szenarien für diesen Auftrag erstellen und vergleichen',
        actionsRequiredLabel: 'Erforderliche Maßnahmen'
    },
    rolegroupDetailsPage: {
        emptyStateLabel: 'Die Liste Ihrer ${rolerequestPluralLowerAlias} wird hier angezeigt',
        resourceInactiveString: 'Zum Fortfahren von {rolerequestPluralLowerAlias} auf eine aktive ${resourceSingularLowerAlias} festlegen.',
        addRoleText: '${rolerequestPluralLowerAlias} hinzufügen',
        addRoleByNameLabel: '${rolerequestSingularCapitalAlias} nach Bezeichnung',
        addRoleByRequirementsLabel: '${rolerequestSingularCapitalAlias} nach Anforderungen',
        roleFromTemplateLabel: '${rolerequestSingularCapitalAlias} aus einer Vorlage',
        noRoleTemplatesCreatedLabel: 'Es wurden keine Vorlagen hinzugefügt',
        notLoadedLabel: 'nicht geladen',
        noValuesLoadedLabel: 'nicht geladener Wert',
        applyText: 'Anwenden',
        searchText: 'Suchen',
        manageRoleTemplates: 'Vorlagen ${rolerequestPluralLowerAlias} verwalten',
        actionButtonLabel: 'Aktionen',
        viewDetails: 'Details anzeigen',
        defaultRoleName: 'Neue Position',
        noRoleGroupSetLabel: 'Kein ${rolerequestgroupSingularLowerAlias} festgelegt',
        noResourcesMeetCriteriaText: 'Ändern oder Löschen Sie einige Anforderungen, und speichern Sie anschließend die Änderungen.',
        noResourcesFoundAdditionalText: 'Es kann bis zu 24 Stunden dauern, bis kürzlich aktualisierte Kompetenzdaten in den Vorschlägen angezeigt werden.',
        moveToButtonLabel: 'Verschieben nach ...',
        noMatchesFoundTopText: 'Keine Übereinstimmungen gefunden',
        unsavedChangesToRoleText: 'Nicht gespeicherte Änderungen an ${rolerequestSingularLowerAlias}',
        suggestionsNotUpToDateText: 'Die Vorschläge sind nicht aktuell. Speichern Sie Änderungen, bevor Sie ${resourcePluralLowerAlias} vorschlagen.',
        assignResource: '${rolerequestSingularLowerAlias} zuweisen',
        unassignResource: 'Zuweisung an ${rolerequestSingularLowerAlias} aufheben',
        addToShortlist: 'Auf die Vormerkliste setzen',
        removeFromShortlist: 'Von der Vormerkliste entfernen',
        shortlist: 'Vorauswahl',
        shortlisted: 'In der Vorauswahl',
        notShortListed: 'Nicht in der Vorauswahl',
        shortlistedBy: 'Vorauswahl von',
        maxShortlistReachedFirstRow: 'Beschränkt auf',
        maxShortlistReachedSecondRow: '6 Ressourcen',
        hiddenSuggestionsTopText: 'Ausgeblendete Vorschläge',
        hiddenSuggestionsText: 'Die vorgeschlagenen ${resourcePluralLowerAlias} sind ausgeblendet, da sie auf einigen Anforderungen basieren, für deren Anzeige Sie keine erforderliche Berechtigung haben.',
        tailMessage: 'Sie konnten keinen passenden Treffer finden? Versuchen Sie, einige Rollenanforderungen zu ändern oder zu entfernen.',
        editButtonLabel: '${rolerequestSingularCapitalAlias} bearbeiten',
        multipleRolesSelectedTopText: 'Mehrere ${rolerequestPluralLowerAlias} gewählt',
        multipleRolesSelectedBodyText: 'Um die vorgeschlagenen ${resourcePluralLowerAlias} anzuzeigen, wählen Sie nur einen ${rolerequestSingularCapitalAlias} nach Anforderungen aus.',
        saveAsTemplateLabel: 'Als Vorlage speichern',
        potentialConflictsTooltip: 'Potenzielle Konflikte mit vorhandener {bookingSingularLowerAlias}',
        maxAllowedRolesLabel: 'Max. 100 ${rolerequestPluralLowerAlias}',
        commandBarConfig: {
            editLabel: 'Bearbeiten',
            duplicateLabel: 'Duplizieren',
            deleteLabel: 'Löschen'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'Buchen ${entityAlias}',
            addBookingToJobRecordListCaption: 'Buchen auf ${entityAlias}',
            searchLabel: 'Suche',
            sortLabel: 'Sortieren',
            sortyByLabel: 'Sortieren nach',
            columnsLabel: 'Spalten',
            applyButtonText: 'Anwenden',
            detailsLabel: 'Details',
            notLoadedLabel: 'nicht geladen',
            notLoadedValueLabel: 'nicht geladener Wert',
            historyFieldPlaceholder: 'Nicht spezifiziert',
            pastLabel: 'zurückliegend',
            lastLoginLabel: 'Letzte Anmeldung',
            expandAndCollapseText: 'Zeile expandieren oder reduzieren',
            expandAllCaption: 'Alles expandieren',
            collapseAllCaption: 'Alles reduzieren',
            sortLabelButton: 'Sortieren ${order}',
            resourcesLabel: 'DE_Resources_DE',
            jobsLabel: 'DE_Jobs_DE',
            calculatingSortCalcFields: 'DE_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_DE',
            numberResults: 'DE_${rowCount} results_DE',
            resourceLabel: 'DE_Resource_DE',
            jobLabel: 'DE_Job_DE'
        }
    },
    roleRequirementsSection: {
        addCriteriaButtonLabel: 'Anforderungen hinzufügen',
        resourceAttributesLabel: '${resourceSingularCapitalAlias}-Attribute',
        mustMeetCriteriaDescription: 'Voraussetzungen',
        mustMeetCriteriaExplanation: '„${resourcePluralCapitalAlias}, die diese Anforderungen nicht erfüllen, werden nicht vorgeschlagen“',
        criteriaEmptyStateMessage: 'Keine Anforderungen hinzugefügt',
        removeRequirementLabel: 'Entfernen',
        resourceSkillsLabel: '${resourceSingularCapitalAlias}-Fähigkeiten',
        searchLabel: 'Suchen',
        applyButtonText: 'Anwenden',
        removeSkillsText: 'Fähigkeiten löschen',
        removeFilterText: 'Fähigkeiten entfernen',
        levelText: 'Ebene',
        levelsText: 'Ebenen',
        anySkillsText: 'Beliebige',
        oneOfSkillsText: 'Eines aus...',
        saveSkillsText: 'Speichern',
        cancelSkillsText: 'Abbrechen'
    },
    entityWindow: {
        basicDetailsSectionTitle: 'Grundlegende Details',
        requirementsSectionTitle: 'Anforderungen',
        milestonesSectionTitle: 'Meilensteine',
        cMeSectionTitle: 'C-me-Eigenschaften',
        workHistoryTitle: 'Aktuelle Arbeit',
        workDetailsSectionTitle: 'Arbeitsdetails',
        budgetSectionTitle: 'Budget',
        budgetDetailsSectionTitle: 'Budget-Details',
        timeAndFinancialsSectionTitle: 'DE_Time and financials_DE',
        revenueSectionTitle: 'DE_Revenue_DE',
        costsSectionTitle: 'DE_Costs_DE',
        profitSectionTitle: 'DE_Profit_DE',
        hoursSectionTitle: 'DE_Hours_DE',
        planningSectionTitle: 'Planung',
        previousRelatedJob: 'Vorheriger zugehöriger Job',
        nextRelatedJob: 'Nächster zugehöriger Job',
        contactSectionTitle: 'Kontakt',
        emplyomentDetailsSectionTitle: 'Angaben zur Beschäftigung',
        skillsSectionTitle: 'Qualifikationen',
        systemInfoSectionTitle: 'System-Infos',
        timeAllocationTitle: 'Zeitkontingent',
        projectHealthTitle: 'Projektstatus',
        fixedTimeSectionSuffix: 'Std.',
        loadingSectionSuffix: '% der Arbeitsstunden',
        timeSectionSuffix: 'gebuchte Stunden insgesamt',
        hoursInTotalSuffix: 'Stunden insgesamt',
        hoursPerDaySuffix: 'Stunden pro Tag',
        FTESuffix: 'VZÄ',
        resourcesSuffix: '${resourcePluralLowerAlias}',
        nameLabel: 'Name',
        updatedToLabel: 'aktualisiert auf',
        fromLabel: 'von',
        numberOfResourcesPrefix: 'Anzahl Ressourcen',
        chargeRateFieldsControlTitle: 'Gebührensatz',
        bookingResourceChargeRateLabel: 'Gebührensatz Ressource',
        bookingOverriddenChargeRateLabel: 'Anderen Gebührensatz verwenden',
        bookingCustomChargeRateLabel: 'Benutzerdefinierten Gebührensatz verwenden',
        bookingRevenueRatesRowTitle: 'Umsatz',
        bookingCostRatesRowTitle: 'Kosten',
        bookingProfitRatesRowTitle: 'Gewinn',
        bookingViewModeChargeRatesTitle: 'Gebührensätze',
        bookingOwnResourceChargeModeLabel: 'Gebührensatz Ressource',
        bookingDifferentResourceChargeModeLabel: 'Anderer Gebührensatz',
        bookingCustomChargeModeLabel: 'Benutzerdefinierter Gebührensatz',
        rolerequestDescriptionPlaceholder: 'z. B. Projektleiter',
        rolerequestOwnResourceChargeModeLabel: 'Gebührensatz Ressource',
        rolerequestDifferentResourceChargeModeLabel: 'Anderer Gebührensatz',
        rolerequestCustomChargeModeLabel: 'Benutzerdefinierter Gebührensatz',
        rolerequestResourceChargeRateLabel: 'Gebührensatz Ressource',
        rolerequestOverriddenChargeRateLabel: 'Anderen Gebührensatz verwenden',
        rolerequestCustomChargeRateLabel: 'Benutzerdefinierten Gebührensatz verwenden',
        rolerequestRevenueRatesRowTitle: 'Umsatz',
        rolerequestCostRatesRowTitle: 'Kosten',
        rolerequestProfitRatesRowTitle: 'Gewinn',
        roleByNameWindowTitle: '${rolerequestCapitalEntityAlias} nach Bezeichnung',
        roleByRequirementWindowTitle: '${rolerequestCapitalEntityAlias} nach Anforderungen',
        manageRoleTemplatesWindowTitle: 'Meine Vorlagen verwalten',
        roleTemplateWindowTitle: '${rolerequestCapitalEntityAlias} Vorlage',
        dateRangeLabel: 'Datumsbereich',
        datesRequiredLabel: 'Daten erforderlich',
        nonWorkSectionFieldText: 'Arbeitsfreie Tage einschließen',
        bookingSectionTitle: 'Buchung',
        rolerequestSectionTitle: 'Anforderungen',
        rolerequestGroupSectionTitle: 'Positionengruppe',
        jobSectionTitle: 'Auftrag',
        resourceSectionTitle: 'Ressource',
        clientSectionTitle: 'Kunde',
        bookingSectionTitlePlural: 'Buchungen',
        jobSectionTitlePlural: 'Aufträge',
        resourceSectionTitlePlural: 'Ressourcen',
        clientSectionTitlePlural: 'Kunden',
        rolerequestSectionTitlePlural: 'Anforderungen',
        rolerequestGroupSectionTitlePlural: 'Positionengruppe',
        moreInfoButtonLabel: 'Weitere Infos',
        duplicateLabel: 'Duplizieren',
        assignToRoleButtonLabel: 'Zuweisen an',
        unassignFromRoleButtonLabel: 'Zuweisung aufheben von',
        rejectButtonLabel: 'Ablehnen',
        restartButtonLabel: 'Neustarten',
        archiveButtonLabel: 'Archivieren',
        createButtonLabel: 'Erstellen',
        createEntityTitle: 'Erstellen ${entityTitleAlias}',
        editEntityTitle: 'Bearbeiten ${entityTitleAlias}',
        editButtonLabel: 'Bearbeiten',
        applyButtonLabel: 'Anwenden',
        closeButtonLabel: 'Schließen',
        withdrawButtonLabel: 'Antrag zurückziehen',
        editRoleByNameButtonLabel: '${rolerequestPluralCapitalAlias} nach Bezeichnung bearbeiten',
        editRoleByCriteriaButtonLabel: '${rolerequestPluralCapitalAlias} nach Anforderungen bearbeiten',
        makeLiveSingularButtonLabel: 'Live schalten',
        makeLivePluralButtonLabel: 'Live schalten',
        submitRequestButtonLabel: 'Anforderung übermitteln',
        addBookingLabel: 'Buchung hinzufügen',
        cancelButtonLabel: 'Abbrechen',
        saveChangesButtonLabel: 'Änderungen speichern',
        saveAllButtonLabel: 'Alle speichern',
        discardChangesButtonLabel: 'Änderungen verwerfen',
        progressButtonLabel: 'Fortfahren',
        deleteButtonLabel: 'Löschen',
        editAllButtonLabel: 'Alle bearbeiten',
        archiveAllButtonLabel: 'Alle archivieren',
        restartAllButtonLabel: 'Alle neustarten',
        submitRequestAllButtonLabel: 'Anfragen übermitteln',
        deleteAllButtonLabel: 'Alle löschen',
        newButtonLabel: 'Erstellen',
        renameLabel: 'Umbenennen',
        viewButtonLabel: 'Anzeigen',
        compareButtonLabel: 'Vergleichen',
        createTemplateLabel: 'Vorlage erstellen',
        roleTemplateLabel: '${rolerequestSingularCapitalAlias} Vorlage',
        rolePublicationWindowTitle: '${rolerequestCapitalEntityAlias} Veröffentlichung',
        insufficientActionRights: 'Keine erforderliche Berechtigungen zur DurchfÃ¼hrung der Aktion fÃ¼r alle',
        manageRoleTemplatesEmptyStateLabel: 'Sie haben keine ${rolerequestSingularLowerAlias} Vorlagen',
        addNewRoleLabel: 'Neuen hinzufÃ¼gen ${rolerequestSingularLowerAlias}',
        roleListBodyEmptyStateLabel: 'Sie kÃ¶nnen einen benannten ${resourceSingularLowerAlias} hinzufÃ¼gen oder ${rolerequestSingularLowerAlias} eingeben, um einen geeigneten zu finden ${resourcePluralLowerAlias}',
        templateDetailsLabel: 'Vorlagendetails',
        provideTemplateNameLabel: 'Bitte geben Sie eine Bezeichnung für Ihre Vorlage ein',
        maxLengthValidationMessage: 'Maximal ${maxNameLength} Symbole erlaubt',
        createdLabel: 'Erstellt',
        myTemplatesLabel: 'Meine Vorlagen',
        deleteMultipleBookinngsButtonLabel: 'Löschen',
        deleteMultipleRolerequestsButtonLabel: 'Löschen',
        bookingStatusFieldExplanation: 'Ressource wird in der gebuchten Zeit weiterhin verfügbar sein',
        tableViewBookingStatusFieldExplanation: 'Unbestätigte ${bookingSingularLowerAlias} werden auf  Seite sichtbar ${plannerPageAlias} sein. ${resourceSingularCapitalAlias} wird in der gebuchten Zeit verfügbar bleiben.',
        nonWorkSectionFieldExplanation: 'Eventualstunden werden an den arbeitsfreien Tagen gebucht',
        jobIsConfidentialFieldExplanation: 'Vertrauliche Aufgaben können durch Personen mit Zugriff angezeigt werden',
        rolerequestRolerequestGroupFieldExplanation: 'Wenn Sie dieses Feld leer lassen, wird eine Rolle außerhalb eines Szenarios erstellt.',
        rolerequestFTEFieldExplanation: '1 Vollzeitäquivalent entspricht ${referenceDiaryTime} Stunden pro Tag',
        resourceSectionFieldExplanation: 'Das Hinzufügen mehrerer Ressourcen generiert eine Buchung für jede einzelne',
        jumpToSectionTitle: 'Wechseln zu',
        additionalSectionTitle: 'Zusätzlicher Abschnitt',
        additionalDetailsSectionTitle: 'Zusätzliche Details',
        commentsSectionTitle: 'Kommentare',
        roleGroupListSectionTitle: 'Positionengruppen',
        detailsPaneTooltipText: 'In der Detailansicht finden Sie Informationen zu dem von Ihnen ausgewählten Objekt. Über die Symbole gelangen Sie zu Auftrags-, Ressourcen- oder Buchungsinformationen.',
        detailsPaneTooltipTitle: 'Detailansicht',
        attachmentsSectionTitle: 'Dokumente',
        moreOptionsButtonLabel: 'Weitere Optionen',
        bookingBudgetDetailsMessage: 'Für Budget-Kalkulationen wird der am ersten Tag der Buchung gültige Gebührensatz angewendet.',
        entityCreatedSuffix: 'erstellt',
        entityDeletedSuffix: 'gelöscht',
        notFoundPrefix: 'Wert',
        notFoundSuffix: 'nicht gefunden',
        roleMarketplaceCriteriaMatchExplanation: 'Bewerber müssen die Anforderungen erfüllen',
        rolerequestDiaryForEstimationLabel: 'Zeitplan für Schätzung',
        selectChargeRateLabel: 'Gebührensatz auswählen',
        customChargeRateLabel: 'Benutzerdefinierter Gebührensatz',
        estimatedBudgetLabel: 'Geschätztes Budget',
        estimatesTabLabel: 'Schätzung',
        assignedTotalsTabLabel: 'Zugewiesene Gesamtbeträge',
        roleGroupCountLabel: '${roleGroupCount} ${rolerequestgroupPluralCapitalAlias}',
        messages: {
            bookingBudgetDetailsMessageText: 'Für Budget-Kalkulationen wird der am ersten Tag der ${bookingSingularLowerAlias} gültige Gebührensatz angewendet.',
            roleBudgetDetailsMessageText: 'Für die Budgetberechnungen wird der am ersten Tag des ${rolerequestSingularLowerAlias} gültige Gebührensatz verwendet.',
            roleAssigneesTotalsDifferenceText: 'Die tatsächlichen Gesamtbeträge können davon abweichen, wenn die Beauftragten von der Schätzung abweichende Zeitpläne oder Gebührensätze haben.',
            bookingMultipleResourcesBudgetDetailsMessageText: `Die jeweiligen Tarife für jede \${bookingSingularLowerAlias} können nach der Erstellung in den entsprechenden Details eingesehen werden.
            Für Budget-Kalkulationen wird der am ersten Tag der \${bookingSingularLowerAlias} gültige Gebührensatz angewendet.`,
            roleResourceWarningText: 'Weisen Sie zum Anfragen einer ${bookingSingularLowerAlias} einen ${resourceSingularLowerAlias} zu.',
            roleResourcesContainUnassigedWarningText: `\${bookingSingularCapitalAlias}-Anfragen können nicht fortgesetzt werden mit
            Nicht zugewiesener \${resourcePluralLowerAlias}, bitte verwerfen Sie die Auswahl „Nicht zugewiesen“ \${resourceSingularLowerAlias}.`,
            criteriaRoleUnassignedResourceText: 'Budgetdaten werden berechnet, wenn ${resourcePluralLowerAlias} dem ${rolerequestSingularLowerAlias} zugewiesen werden.',
            requirementSectionInsufficientPermissionsText: 'Einige Anforderungen sind ausgeblendet, da Sie dafür keine entsprechenden Zugriffsrechte haben.',
            rolerequestCriteriaDPSuggestionPaneMessageText: '${resourcePluralLowerAlias} dem ${rolerequestSingularLowerAlias} durch das !{Suggestion pane} zuordnen.',
            suggestionPaneButtonText: 'Vorschlagsfenster',
            criteriaRoleAssignResourceText: '${resourcePluralLowerAlias} dem ${rolerequestSingularLowerAlias} durch das Vorschlagsfenster zuordnen.',
            criteriaRoleAssignedResourceChangeMessageText: 'Zuweisung zum ${rolerequestSingularLowerAlias} über das Vorschlagsfenster ändern.',
            criteriaRoleAssignResourceToCalculateBudgetText: 'Berechnung des Budgets, das Sie ${resourcePluralLowerAlias} für das ${rolerequestSingularLowerAlias} zuordnen müssen.',
            criteriaRolePublishMessageText: 'Die Aufgabe ${rolerequestSingularLowerAlias} endet automatisch nach dem ${rolerequestSingularLowerAlias} Enddatum - ${endDate}',
            roleApplicationAppliedOnText: 'Sie haben sich für diese ${rolerequestSingularLowerAlias} am .... beworben ${applyDate}.',
            bookingJobOverBudgetMessageText: 'Dieses ${bookingSingularLowerAlias} wird das Budget für dieses ${jobSingularLowerAlias} überschreiten.',
            bookingResourceOverBudgetMessageText: 'Die Buchung von ${resourceSingularLowerAlias} wird das Budget für dieses ${jobSingularLowerAlias} überschreiten.',
            bookingJobHoursOverBudgetMessageText: 'DE_This ${bookingSingularLowerAlias} will put this ${jobSingularLowerAlias}\'s Total hours at ${jobHoursPercentageBudget}% (${totalHoursOverBudget} hrs over) of its Budget hours_DE',
            resourceChargeRateAndDiaryWarningMessage: 'DE_We recommend modifying ${chargeRateAlias} and ${diaryGroupAlias} outside of work hours as recalculating time and financials can take multiple hours depending on how many resources and bookings this change affects (this may include past bookings). Users will experience longer load times while this is happening._DE'
        },
        financialInformationSectionTitle: 'Finanzielle Informationen',
        schedulingSectionTitle: 'Terminplanung',
        rolesSectionTitle: 'Positionen',
        resourceSummaryTitle: 'Zusammenfassung',
        overlappingBookingsTitle: 'Überschneidungen von Buchungen und Rollen',
        saveAsADraft: 'Als Entwurf speichern',
        backToSuggestionLabel: 'Zurück zu den Vorschlägen',
        suggestLabel: 'Vorschlagen',
        suggestedLabel: 'Vorgeschlagen',
        forLabel: 'für',
        moveButtonLabel: 'Verschieben',
        moveToModalTitle: 'Verschieben nach ...',
        searchForLabel: 'Suchen nach ...',
        lastRefreshedText: 'Letzte Aktualisierung',
        SelectionTitleLabel: 'Sammelupdate aller ',
        SelectionDescriptionLabel: 'Werte setzen oder leer lassen, um Werte zu löschen',
        SelectionFieldsCaptionLabel: 'Felder',
        shortlistUptoSixText: 'Sie können bis zu 6 Ressourcen vormerken.',
        manageBudgetLabel: 'Budget verwalten',
        movePendingFTELabel: 'Ausstehende FTEs verschieben',
        removePendingFTELabel: 'Ausstehende FTEs entfernen',
        movePendingResourcesLabel: 'Verschiebung ausstehend ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Ausstehende Ressourcen entfernen',
        publishToMarketplaceLabel: 'Veröffentlichen in ${pageAlias}',
        publishRoleLabel: 'Veröffentlichen ${rolerequestSingularLowerAlias}',
        roleMarketplaceCategoryPlaceholder: 'Kategorie hinzufügen',
        saveAsTemplateLabel: 'Als Vorlage speichern',
        editRolePublicationButtonLabel: '${rolerequestSingularLowerAlias} Veröffentlichung',
        removeRolePublicationButtonLabel: 'Veröffentlichung ${rolerequestSingularLowerAlias} entfernen',
        emptyState: {
            noRoleGroupItemsCoincidenceMessage: 'Zur Anzeige der ${rolerequestgroupSingularLowerAlias}-Details, wählen Sie die ${rolerequestPluralLowerAlias} der gleichen ${rolerequestgroupSingularLowerAlias}.',
            noRoleGroupItemsCoincidenceContent: 'Mehrere ${rolerequestgroupPluralLowerAlias} in der Auswahl'
        },
        createScenarioLabel: 'Erstellen ${rolerequestgroupSingularLowerAlias}',
        editScenarioLabel: 'Bearbeiten ${rolerequestgroupSingularLowerAlias}',
        openScenarioButtonLabel: 'Offen ${rolerequestgroupSingularCapitalAlias}',
        fieldValueCaption: {
            budget: 'DE_of budget_DE',
            target: 'DE_of target_DE'
        },
        reviewSettings: {
            headerTitle: 'DE_Review settings_DE',
            saveChangesButtonLabel: 'DE_Save changes_DE',
            cancelBtnLabel: 'DE_Cancel_DE',
            resourceSkillsReviewLabel: 'DE_Resource skills reviews_DE',
            reviewerOnThisJobLabel: 'DE_Reviewer on this job_DE',
            eligibleForReviewLabel: 'DE_Eligible for reviews_DE',
            reviewerOnThisJobCaptionLabel: 'DE_Choose who can review resource skills on this job_DE',
            pastBookingLabel: 'DE_Only resources with past booking on this job_DE',
            allBookedLabel: 'DE_All booked resources on this job_DE',
            clearSkillsReviewsLabel: 'DE_Clear all skills reviews_DE'
        },
        reviewSection: {
            skillReviewLabel: 'DE_Skill reviews_DE',
            resourceReviewedLabel: 'DE_{0} Resource reviewed_DE',
            submitReviewButtonLabel: 'DE_Submit skills review_DE'
        }
    },
    resourceSummarySection: {
        availabilityText: 'Verfügbarkeit',
        suitabilityText: 'Eignung',
        hoursSuffix: 'h',
        andLabel: 'und'
    },
    suggestedResources: {
        addToShortlist: 'Zur Auswahlliste hinzufügen',
        matchTextSuffix: 'Übereinstimmung',
        isATextMessage: 'ist ein/e ',
        skillsLabel: 'Kompetenzen',
        workExperienceLabel: 'Berufserfahrung',
        skillWithPrefix: 'Fähigkeit mit',
        similarityTextSuffix: 'Ähnlichkeit:',
        suitabilityText: 'Eignung',
        mixedSortOptionText: 'Eignung und Verfügbarkeit',
        sortOrderText: 'Sortierreihenfolge: ',
        suggestionsLabel: 'Vorschläge',
        forLabel: 'für',
        appliedOn: 'Beworben am',
        lastRefreshedText: 'Letzte Aktualisierung',
        refreshLabel: 'Aktualisieren',
        loadingLabel: 'Lädt...',
        noRelevantAISuggestionDataText: 'Unzureichende Angaben zu Fähigkeiten oder Erfahrungen für eine Eignungsbewertung',
        infoBannerMessage: 'Alle Ergebnisse erfüllen die ${resourceSingularLowerAlias} Eigenschaftsanforderungen.',
        aiSuggestionsLabel: 'KI-Vorschläge',
        aiToggleTooltipText: 'Dies aktivieren, um KI-Vorschläge zu verwenden.'
    },
    common: {
        selectRowTextAriaLabel: 'Zeile auswählen für ${name}',
        maximumFieldLengthValidationMessage: 'Maximal ${maximumFieldSize} Zeichen',
        daysString: 'Tage',
        singleDayString: 'Tag',
        workingString: 'Arbeits-',
        nonWorkingString: 'Arbeitsfreie',
        hoursString: 'Zeiten',
        FTEstring: 'FTE',
        pendingString: 'ausstehend',
        hoursPerDaySuffix: 'Stunden pro Tag',
        excludeNonWorkingString: 'schließt arbeitsfreie Tage aus',
        includeNonWorkingString: 'schließt arbeitsfreie Tage ein',
        addPrefix: 'Hinzufügen',
        newPrefix: 'Neu',
        allPrefix: 'Alle',
        addAnotherPrefix: 'Andere hinzufügen',
        clickToEditSuffix: '(zum Bearbeiten anklicken)',
        insufficientPermissionsToEditSuffix: 'Keine entsprechenden Berechtigungen zum Bearbeiten',
        historyFieldPlaceholder: 'Nicht spezifiziert',
        noValueMessage: 'Kein ${fieldInfoAlias} festgelegt',
        milestoneHistoryFieldPlaceholder: 'Kein Meilenstein eingestellt',
        startingLabel: 'Startet',
        endingLabel: 'Endet',
        dueDate: 'Fälligkeitsdatum',
        nameLabel: 'Name',
        restrictedLabel: 'Eingeschränkt',
        markAsCompletedLabel: 'Als abgeschlossen markieren',
        milestonesSectionTitle: 'Meilensteine',
        milestonesSectionTitleNameDueDate: 'Name und Fälligkeitsdatum von Milestones',
        milestonePlaceholder: 'z. B. Projektgenehmigung',
        noString: 'Nein',
        setString: 'festgelegt',
        totalSuffix: 'Gesamt',
        hourlySuffix: 'stündlich',
        noResultsFoundMessage: 'Keine Ergebnisse gefunden',
        noResultsMessage: 'Keine Ergebnisse',
        checkedMessage: 'Ja',
        uncheckedMessage: 'Nein',
        shownLabel: 'Mehr',
        hiddenLabel: 'Ausblenden',
        jobUtitilizationInfo: 'Buchungen von der Verwendung ausschließen',
        excludeValue: 'Ausschließen',
        includeValue: 'Einschließen',
        noResultsMessagePrefix: 'Nein',
        noResultsMessageSuffix: 'wurde mit dieser Bezeichnung gefunden.',
        noOptionsSetSuffix: 'Von Ihrem Administator festgelegte Kategorieoptionen',
        showMorePrefix: 'Mehr',
        showMoreSuffix: 'anzeigen',
        showLessText: 'Weniger anzeigen',
        seeMoreText: 'DE_See more_DE',
        detailsTabLabel: 'Details',
        historyTabLabel: 'Verlauf',
        editAllTabLabel: 'Alle bearbeiten',
        roleListLabel: 'Positionen-Liste',
        newTemplateLabel: 'Neue Vorlage',
        audit: {
            sortLabel: 'Sortieren',
            showMoreText: 'Mehr anzeigen',
            backToTopText: 'Zurück zum Anfang',
            oldValuePrefix: 'von',
            actorPrefix: 'von',
            startingText: 'Start von',
            timeLineActionCreateAlias: 'hinzugefügt',
            timeLineActionUpdateAlias: 'aktualisiert',
            unassignedValue: 'Nicht zugewiesen',
            levelString: 'Ebene',
            timeLineActionUpdateSuffixAlias: 'bis',
            timeLineActionDeleteAlias: 'entfernt',
            timeLineActionRemoveAlias: 'entfernt',
            falseString: 'Nein',
            trueString: 'Ja',
            anyLevelString: 'Alle Ebenen',
            resourceNotFoundCaption: '${resourceSingularCapitalAlias} nicht gefunden',
            templateTexts: {
                historyRecordCreateText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionCreateAlias}',
                historyRecordUpdateText: '${alias} ${timeLineActionUpdateAlias} ${timeLineActionUpdateSuffixAlias} ${valueDescription} ${oldValuePrefix} ${oldValueDescription} ${startingText} ${valueStartDate}',
                historyRecordDeleteText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionRemoveAlias}'
            },
            auditSectionTitles: {
                projectHealthTitle: 'Projektstatus:'
            }
        },
        lastUpdatedLabel: 'Zuletzt aktualisiert',
        updatedByLabel: 'von',
        noDiaryOnDatesLabel: 'Kein Terminkalender',
        noOverlappingBookings: 'Keine Buchungsüberschneidungen',
        archivedLabel: 'Archivieren',
        restartedLabel: 'Neustarten',
        requestedLabel: 'Anfrage',
        batchRequestedLabel: 'Anforderungen',
        rejectedLabel: 'Ablehnen',
        expiredLabel: 'Gültig bis',
        draftLabel: 'Entwurf',
        liveLabel: 'Aktiv',
        actionsDropdownLabel: 'Aktionen',
        unassignedPlaceholder: 'Nicht zugewiesen',
        availabilityText: 'Verfügbarkeit',
        movePendingFTELabel: 'Ausstehende FTEs verschieben',
        removePendingFTELabel: 'Ausstehende FTEs entfernen',
        movePendingResourcesLabel: 'Ausstehende verschieben ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Ausstehende löschen ${resourcePluralLowerAlias}',
        rolerequestLoadingExplanation: 'von ${resourcePluralLowerAlias} Kapazität',
        noRateSetLabel: 'Keine Rate festgelegt',
        resourcesString: '${resourcePluralLowerAlias}',
        potentialConflictsTooltip: 'Mögliche Konflikte mit bestehenden ${booking}',
        addFiltersButtonLabel: 'Filter hinzufügen',
        singleDayUnit: 'Tag',
        daysUnit: 'Tage',
        singleWeekUnit: 'Woche',
        weeksUnit: 'Wochen',
        singleMonthUnit: 'Monat',
        monthsUnit: 'Monate',
        singleYearUnit: 'Jahr',
        yearsUnit: 'Jahre',
        revertToStartAndEndDates: 'Auf Start- und Endtermine zurücksetzen',
        searchToSelect: 'Suche zur Auswahl',
        fieldMandatoryText: 'Das ist ein Pflichtfeld',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        estimatesTooltip: 'Schätzung des ${rolerequestSingularLowerAlias}-Budgets vor der Ressourcenzuweisung.',
        assigneesTotalsTooltip: 'Tatsächliches ${rolerequestSingularLowerAlias}-Budget mit aktuellen Beauftragten.',
        editLabel: 'Schaltfläche „Bearbeiten“',
        closeLabel: 'Schaltfläche „Schließen“',
        deleteLabel: 'Schaltfläche „Löschen“',
        addLabel: 'Schaltfläche „Hinzufügen“',
        quantityAria: 'Eingabe ${fieldAlias}',
        criteriaRoleBudgetDescription: 'Für die ${rolerequestgroupSingularLowerAlias}-Budget-Gesamtbeträge werden Schätzwerte verwendet, bis die ${rolerequestSingularLowerAlias} vollständig besetzt ist',
        noResourcesAssigned: 'Kein ${resourcePluralLowerAlias} zugewiesen',
        noRoleGroupSetLabel: 'Kein ${rolerequestgroupSingularLowerAlias} festgelegt',
        avatarAltText: 'Profilbild für ${resourceName}',
        copyOfPrefix: 'Kopie von',
        ascendingSort: 'Aufsteigend',
        descendingSort: 'Absteigend',
        selectionListSort: 'Auswahlliste sortieren',
        deleteLabelText: 'Löschen',
        deleteLabelItemText: 'Löschen ${item}',
        scrollDownText: 'Nach unten scrollen',
        scrollBarText: 'Rollbalken',
        scrollUpText: 'Nach oben scrollen',
        cancelChangesLabel: 'Änderungen abbrechen',
        saveChangesLabel: 'Änderungen speichern',
        fullScreenButtonLabel: 'Ganze Seite anzeigen',
        clearNameLabel: 'Feld ${fieldName} leeren',
        collapseLeftNavigation: 'Linke Navigation einklappen',
        expandLeftNavigation: 'Linke Navigation ausklappen',
        fieldAriaLabel: 'Eingeben ${fieldAlias}',
        selectAllRolesLabel: 'Alle Rollen auswählen',
        expandRowLabel: 'Zeile ausklappen',
        collapseRowLabel: 'Zeile einklappen',
        floatingActionBarLabel: 'DE_${entity} ${fieldName} may be different for this date range_DE',
        floatingActionBarButtonLabel: 'DE_Update sort order_DE'
    },
    tableOptions: {
        displayDensityOptionTitle: 'Dichte anzeigen',
        chooseDetailsOptionTitle: 'Details auswählen',
        compactDensityOptionTitle: 'Kompakt',
        defaultDensityOptionTitle: 'Standard',
        expandedDensityOptionTitle: 'Erweitert',
        searchLabel: 'Suche',
        applyButtonText: 'Anwenden',
        notLoadedLabel: 'nicht geladen',
        notLoadedValueLabel: 'nicht geladener Wert',
        tableOptionsLabel: 'Tabellenoptionen'
    },
    dataGrid: {
        sortLabel: 'Sortieren',
        sortyByLabel: 'Sortieren nach',
        pageOptionSuffix: 'pro Seite',
        noItemsMessage: 'Sie haben keine Aufträge',
        noRolesItemsMessage: 'Sie haben keine Positionen',
        newJob: 'Neuer Auftrag',
        noMatchingItemsMessage: 'Keine zu den Filtern passenden Aufträge gefunden',
        noMatchingResourceItemsMessage: 'DE_No resources found matching your filters_DE',
        noMatchingResourceItemsContentMessage: 'DE_Try changing your filters or view settings_DE',
        noMatchingRolesItemsMessage: 'Keine zu den Filtern passenden Positionen gefunden',
        noMatchingItemsContent: 'Versuchen Sie es mit einer Änderung Ihrer Filter.',
        noChargetypeSet: 'Keine Gebührenart festgelegt',
        noRoleGroupItemsMessage: 'Keine Szenarien',
        noRoleGroupItemsContent: 'Szenarien für diesen Auftrag erstellen und vergleichen',
        noResourceFoundMessage: 'Kein ${resourceEntityAlias} gefunden, der zu den Kriterien passt',
        noMarketplaceRolesPublished: 'Es wurden keine ${rolerequestPluralLowerAlias} veröffentlicht',
        showingCaption: 'Angezeigt werden ${pageRolesCount} von ${totalRolesCount} ${rolerequestAlias}',
        entityPageOptionSuffix: '${rolerequestPluralLowerAlias} pro Seite',
        noResultsMessage: 'Keine Ergebnisse gefunden',
        tryAdjustingFiltersMessage: 'Versuchen Sie, Ihre Suche oder Ihren Filter anzupassen',
        sortLabelButton: 'Sortieren ${order}',
        sortAscending: 'Aufsteigend',
        sortDescending: 'Absteigend',
        operationLogEmptyMessage: 'Bedienprotokoll ist leer',
        operationLogEmptyContent: 'Dieses Protokoll ist derzeit leer ohne aufgezeichnete Vorgänge.',
        operationLogSuccess: 'Vorgang abgeschlossen',
        operationLogIncomplete: 'Vorgang mit Ausnahmen abgeschlossen',
        operationLogCancelled: 'Vorgang abgebrochen von',
        operationLogFailed: 'Vorgang fehlgeschlagen',
        cancelOperation: 'Vorgang abbrechen',
        undoOperation: 'Vorgang rückgängig machen'
    },
    filterPane: {
        anyLevelLabel: 'Jede Ebene',
        selectLabel: 'Auswählen',
        filterSuffix: 'anzeigen',
        headingTitle: 'Filter',
        applyButtonText: 'Anwenden',
        discardChangesText: 'Filter zurücksetzen',
        applyButtonTooltipText: 'Bitte zur Aktivierung Filter hinzufügen und alle Eingaben ausfüllen.',
        resetButtonTooltipText: 'Filter in ihren Ursprungszustand zurücksetzen',
        showMoreButtonText: 'Mehr anzeigen',
        maxDateRangeMessage: 'Maximal 5 Datumsbereiche',
        startDateLabel: 'Start',
        endDateLabel: 'Ende',
        fromDateLabel: 'Von',
        toDateLabel: 'Bis',
        searchLabel: 'Suchen',
        searchToSelectLabel: 'Zur Auswahl suchen',
        textOperatorLabel: 'Ähnlich wie',
        loadingLabel: 'lädt',
        betweenLabel: 'zwischen',
        clearFiltersText: 'Filter zurücksetzen',
        removeFilterText: 'Filter löschen',
        notLoadedLabel: 'nicht geladen',
        notLoadedValueLabel: 'nicht geladener Wert',
        levelText: 'Niveau',
        levelsText: 'Niveaus',
        resetFilterText: 'Alle zurücksetzen',
        clearAllFiltersText: 'Alle leeren',
        numberResults: '${rowCount} Ergebnisse',
        allLabel: 'Alle',
        yesLabel: 'Ja',
        noLabel: 'Nein',
        addLabel: 'Hinzufügen ${fieldAlias}',
        removeLabel: 'Entfernen ${fieldAlias}',
        removeFilterButtonLabel: 'Datumsbereich entfernen von ${startDate} bis ${endDate}',
        typeHereLabel: 'Hier eingeben',
        hiddenFiltersMessage: 'Einige Filter konnten nicht angewendet werden. Dies kann an Ihren Benutzerberechtigungen oder an einem ungültigen Feld/Wert liegen. Das Speichern dieses Arbeitsbereichs',
        hiddenFiltersBoldMessage: 'entfernt alle verborgenen Filter.',
        operators: {
            DB_OPERATORS: {
                LESS_THAN: 'KleinerAls',
                LESS_THAN_OR_EQUAL: 'KleinerAlsOderGleich',
                EQUALS: 'Gleich',
                GREATER_THAN_OR_EQUAL: 'GrößerAlsOderGleich',
                GREATER_THAN: 'GrößerAls',
                LIKE: 'Ähnlich wie',
                CONTAINS: 'Enthält'
            },
            NUMERIC_OPERATORS_ALIAS: {
                LessThan: 'wenige als',
                LessThanOrEqual: 'höchstens',
                Equals: 'gleich',
                GreaterThanOrEqual: 'mindestens',
                GreaterThan: 'mehr als'
            },
            NUMERIC_PARAMETER_OPERATORS_ALIAS: {
                LessThan: 'weniger als',
                LessThanOrEqual: 'höchstens',
                Equals: 'gleich',
                GreaterThanOrEqual: 'mindestens',
                GreaterThan: 'mehr als'
            },
            TEXT_OPERATORS_ALIAS: {
                Like: 'enthält'
            },
            MULTY_VALUES_OPERATORS_ALIAS: {
                Contains: 'ist eins von'
            },
            DATE_OPERATORS_ALIAS: {
                LessThanOrEqual: 'bis zu',
                GreaterThanOrEqual: 'von'
            },
            DATE_SENSITIVE_OPERATORS_ALIAS: {
                GreaterThanOrEqual: 'mindestens',
                LessThanOrEqual: 'höchstens'
            },
            SKILL_OPERATORS_ALIAS: {
                Equals: 'ist'
            },
            CHECKBOX_OPERATORS_ALIAS: {
                Equals: 'am'
            },
            BOOLEAN_OPERATORS_ALIAS: {
                Equals: 'ist'
            }
        },
        advancedFilterOperators: {
            TEXT: {
                Contains: 'Enthält',
                IsBlank: 'Ist leer',
                IsNotBlank: 'Ist nicht leer'
            },
            NUMERIC: {
                Is: 'Ist',
                IsNot: 'Ist nicht',
                IsMoreThan: 'Ist größer als',
                IsLessThan: 'Ist kleiner als',
                IsBlank: 'Ist leer',
                IsNotBlank: 'Ist nicht leer'
            },
            BOOLEAN: {
                Is: 'Ist',
                IsNot: 'Ist nicht'
            },
            SKILL: {
                Is: 'Ist',
                IsNot: 'Ist nicht',
                IsBlank: 'Ist leer',
                IsNotBlank: 'Ist nicht leer'
            }
        },
        logicalOperators: {
            and: 'Und',
            or: 'Oder'
        },
        uiFilterOperators: {
            Is: 'ist',
            IsNot: 'ist nicht',
            IsMoreThan: 'ist größer als',
            IsLessThan: 'ist kleiner als',
            Contains: 'enthält',
            UpTo: 'ist kleiner oder gleich',
            From: 'ist größer oder gleich',
            IsNotBlank: 'ist nicht leer',
            IsBlank: 'ist leer',
            IsMoreOrEqual: 'ist größer oder gleich',
            IsLessOrEqual: 'ist kleiner oder gleich'
        },
        filterFieldMessages: {
            resource_has_skill_resource_skill_levelAlias: 'Qualifikationen',
            resource_guidAlias: 'Ressourcenbezeichnung',
            availabilityAlias: 'Verfügbarkeit',
            utilisationAlias: 'Auslastung',
            departmentAlias: 'resource_currentpartment_guidDepartment',
            resource_manager_resource_guidAlias: 'Vorgesetzter',
            resource_location_guidAlias: 'Standort',
            resource_localgraguidAlias: 'Qualifikation',
            resource_resourcetype_guidAlias: 'Beschäftigungsart',
            resource_rolenameAlias: 'Titel',
            resource_booking_countAlias: 'Anzahl der Buchungen',
            'Charge RateAlias': 'Gebührensatz',
            job_guidAlias: 'Positionsbezeichnung',
            job_startAlias: 'Auftragsbeginn',
            job_endAlias: 'Auftragsende',
            job_client_guidAlias: 'Kunde',
            job_engagementlead_resource_guidAlias: 'Vorgesetzter',
            job_location_guidAlias: 'Auftragsort',
            job_jobstatus_guidAlias: 'Auftragsstatus',
            job_codeAlias: 'Auftrags-Referenzcode',
            job_chargetype_guidAlias: 'Gebührenart',
            booking_is_assignedAlias: 'Nicht zugewiesen',
            booking_startAlias: 'Buchungsbeginn',
            booking_endAlias: 'Buchungsende',
            booking_bookingtype_guidAlias: 'Buchungsstatus',
            booking_notesAlias: 'Anmerkungen zur Buchung',
            booking_workactivity_guidAlias: 'Arbeitsaktivitäten',
            updatedonAlias: 'aktualisiert am',
            updatedbyAlias: 'von Ressource aktualisiert',
            createdonAlias: 'erstellt am',
            createdbyAlias: 'von Ressource erstellt'
        }
    },
    talentProfilePage: {
        profileTitle: 'Mein Profil',
        shareProfileCaption: 'DE_Share profile_DE',
        uploadLabel: 'Dokumente hochladen',
        changeProfielPictureText: 'Profilbild ändern',
        viewOtherProfile: 'Anderes Profil anzeigen',
        viewMyProfile: 'Mein Profil anzeigen',
        cMeProfileTitle: 'C-Me-Profil',
        editDetailsLabel: 'DE_Edit details_DE',
        updateAvatarWindowMessages: {
            headerTitle: 'Profilbild aktualisieren',
            applyBtnTitle: 'Anwenden',
            removeBtnTitle: 'Bild entfernen',
            cancelBtnTitle: 'Abbrechen',
            uploadAreaText: 'Klicken oder ziehen Sie eine Datei zum Hochladen in diesen Bereich',
            fileTypesString: 'Dateitypen: ',
            dragControlLabel: 'Ziehen',
            zoomControlLabel: 'Vergrößern'
        },
        recommendationTitle: 'Empfehlungen',
        recommendationAlertHeader: 'Aktualisieren Sie Ihre Kompetenzen und erhalten Sie mehr Aufmerksamkeit',
        recommendationAlertDescription: 'Hier sind Kompetenzen , die Sie Ihrem Profil hinzufügen können',
        skillApproval: 'DE_Your changes will be sent to manager for approval. Changes to skill preferences do not require approval._DE',
        additionalDetailsSectionTitle: 'Zusätzliche Details',
        messages: {
            fileTooLargeLabel: 'Hochladen des Dokuments fehlgeschlagen: Datei ist zu groß',
            fileTypeForbiddenLabel: 'Hochladen des Dokuments fehlgeschlagen: Dateityp ist nicht zulässig',
            noFilesUploadedLabel: 'Sie haben keine Dokumente hochgeladen',
            uploadsLimitReachedLabel: 'Dokumentenlimit erreicht: Löschen Sie einige Dokumente, um weitere hochzuladen'
        },
        uploadDocumentsWindowMessages: {
            headerTitle: 'DE_Upload document_DE',
            uploadBtnTitle: 'DE_Upload_DE',
            cancelBtnTitle: 'DE_Cancel_DE',
            uploadAreaText: 'DE_Click or drag file to this area to upload_DE',
            fileTypesString: 'DE_Accepted formats: _DE',
            documentType: 'DE_Type of document_DE',
            expiryDate: 'DE_Expiry date_DE',
            maxFileSize: 'DE_Max ${maxFileSize} MB per file_DE'
        }
    },
    prompts: {
        createOrSaveAsNewPlanPrompt: {
            title: 'Plan erstellen',
            placeholder: 'Planbezeichnung einfügen',
            helpMessage: 'Bitte geben Sie eine Bezeichnung für Ihren Plan ein',
            okText: 'Plan erstellen',
            cancelText: 'Abbrechen',
            name: 'Name',
            type: 'Typ',
            access: 'Zugriff',
            editAccess: 'Zugriff bearbeiten',
            readOnlyAccess: 'Nur Lesezugriff',
            subHeading: 'Als neuen privaten oder öffentlichen Plan speichern',
            privatePlan: 'Privater Plan',
            publicPlan: 'Öffentlicher Plan',
            newPlanLabel: 'Neuer Plan',
            maxLengthValidationMessage: 'Maximal ${maxNameLength}-Symbole zulässig',
            switchOnLabel: 'Anschalten'
        },
        createOrSaveAsNewWorkspacePrompt: {
            title: 'DE_New workspace_DE',
            placeholder: 'DE_Insert workspace name_DE',
            helpMessage: 'DE_Please provide a name for your workspace_DE',
            okText: 'Plan erstellen',
            cancelText: 'Abbrechen',
            name: 'Name',
            type: 'Typ',
            access: 'Zugriff',
            editAccess: 'Zugriff bearbeiten',
            readOnlyAccess: 'Nur Lesezugriff',
            subHeading: 'DE_Save as a new private or public workspace_DE',
            privatePlan: 'DE_Private workspace_DE',
            publicPlan: 'DE_Public workspace_DE',
            maxLengthValidationMessage: 'Maximal ${maxNameLength}-Symbole zulässig',
            newPlanLabel: 'DE_New Workspace_DE',
            switchOnLabel: 'Anschalten'
        },
        extendJobRangePrompt: {
            okText: 'Ja, Auftragsdaten ändern',
            cancelText: 'Nein, verschieben abbrechen',
            title: 'Den Auftrag verlängern?',
            message: 'Möchten Sie den Auftragszeitraum verlängern, um die Verschiebung dieser Buchung zu ermöglichen?',
            detailsText: 'Neue Auftragsdaten für'
        },
        deleteBookingPrompt: {
            title: 'Löschen',
            message: 'Möchten Sie diese(s)(n) ... löschen?',
            okText: 'Ja, ... löschen',
            cancelText: 'Nein, ... übernehmen',
            noEntityDescriptionPrefix: 'Nein',
            noEntityDescriptionSuffix: 'Bezeichnung'
        },
        makePlanPrivatePrompt: {
            okText: 'Plan privatisieren',
            cancelText: 'Plan weiterhin öffentlich halten',
            makeString: 'MACHEN',
            privatePlanString: 'Einen privaten Plan generieren',
            planTypeMessageStart: 'Dieser Plan ist aktuell',
            messageFirstPart: 'Wenn Sie diesen Plan privatisieren,',
            messageBold: 'können andere nicht mehr auf ihn zugreifen.'
        },
        makeWorkspacePrivatePrompt: {
            okText: 'DE_Make workspace private_DE',
            cancelText: 'DE_Keep workspace public_DE',
            makeString: 'MACHEN',
            privatePlanString: 'DE_a private workspace_DE',
            planTypeMessageStart: 'DE_This workspace is currently_DE',
            messageFirstPart: 'DE_If you make this workspace private_DE,',
            messageBold: 'können andere nicht mehr auf ihn zugreifen.'
        },
        deletePlanPrompt: {
            okText: 'Ja, Plan löschen',
            cancelText: 'Nein, Plan übernehmen',
            deleteString: 'Löschen',
            planString: 'löschen',
            workspaceTypeMessage: 'Dieser Plan ist aktuell',
            publicDeleteMessageStart: 'Wenn Sie diesen Plan löschen,',
            publicDeleteMessageEnd: 'können andere nicht mehr auf ihn zugreifen.',
            question: 'Möchten Sie diesen Plan dauerhaft löschen?'
        },
        deleteWorkspacePrompt: {
            okText: 'DE_Yes, delete workspace_DE',
            cancelText: 'DE_No, keep workspace_DE',
            deleteString: 'Löschen',
            planString: 'DE_workspace_DE',
            workspaceTypeMessage: 'DE_This workspace is currently_DE',
            publicDeleteMessageStart: 'DE_If you delete this workspace_DE',
            publicDeleteMessageEnd: 'können andere nicht mehr auf ihn zugreifen.',
            question: 'DE_Do you wish to permanently delete this workspace?_DE'
        },
        deleteRoleTemplatePrompt: {
            okText: 'Ja, Vorlage löschen',
            cancelText: 'Nein, Vorlage übernehmen',
            warning: 'Das kann nicht rückgängig gemacht werden.',
            question: 'Möchten Sie diese ${rolerequestSingularLowerAlias} Vorlage dauerhaft löschen?',
            title: '${roleTemplateDescription} Vorlage löschen?'
        },
        renameRoleTemplatePrompt: {
            okText: 'Vorlage umbenennen',
            cancelText: 'Alte Bezeichnung übernehmen',
            question: 'Sie haben die Vorlage umbenannt. Möchten Sie die Änderungen speichern?',
            title: 'Vorlage umbenennen',
            fromText: 'von ',
            toText: 'in '
        },
        deleteClientPrompt: {
            title: 'Kunde löschen?',
            message: 'Das Löschen dieses Kunden entfernt ihn und alle damit verbundenen Aufträge dauerhaft aus dem gesamten System. Der Kunde kann zukünftigen Aufträgen nicht mehr zugewiesen werden. \n Möchten Sie fortfahren?',
            okText: 'Ja, Kunde löschen',
            cancelText: 'Nein, Kunde übernehmen'
        },
        deleteJobPrompt: {
            title: 'Löschen',
            okText: 'Löschen',
            cancelText: 'Übernehmen',
            thereString: 'Dort',
            isString: 'ist',
            areString: 'sind',
            andString: 'und',
            onString: 'am',
            thisString: 'diese(n)(s)',
            theseString: 'diese',
            betweenString: 'zwischen',
            onThisJobString: 'bei diesem Auftrag',
            withString: 'mit',
            deletingThisJobWillAlsoDeleteString: 'Mit dem Löschen dieses Auftrags wird auch ... gelöscht',
            doYouWishToPermanentlyDeleteString: 'Möchten Sie ... Dauerhaft löschen?',
            messages: {
                deleteJobTitle: '${jobDescription} löschen?',
                deleteJobLabel: '${jobAlias}, ${bookingAlias}, ${roleGroupAlias} und ${roleRequestAlias} löschen?'
            }
        },
        deleteRolePrompt: {
            title: 'Löschen',
            message: 'Möchten Sie diese(s)(n) ... löschen?',
            okText: 'Ja, ... löschen',
            cancelText: 'Nein, ... übernehmen',
            noEntityDescriptionPrefix: 'Nein',
            noEntityDescriptionSuffix: 'Bezeichnung',
            defaultRoleName: 'Neue Rolle',
            noRoleGroupSetLabel: 'Kein ${rolerequestgroupSingularLowerAlias} festgelegt'
        },
        removeRolePublicationPrompt: {
            title: '${rolerequestSingularLowerAlias} entfernen aus ${roleBoardPageAlias}',
            message: 'Es werden ${countOfResources}${resourcePluralLowerAlias} auf diese(n)(s) ${rolerequestSingularLowerAlias} angewendet Durch das Entfernen von ${rolerequestSingularLowerAlias} aus ${roleBoardPageAlias} werden auch die entsprechenden Anwendungen entfernt.',
            question: 'Möchten Sie ${rolePublicationDescription} aus ${roleBoardPageAlias} entfernen?',
            confirmation: '${resourcePluralLowerAlias} Anwendungen und ${rolerequestSingularLowerAlias} aus ${roleBoardPageAlias} entfernen?',
            okText: 'Entfernen ${rolerequestSingularLowerAlias}',
            cancelText: 'Übernehmen ${rolerequestSingularLowerAlias}',
            defaultRoleName: 'Neue Rolle'
        },
        unsavedChangesPrompt: {
            message: 'Diese Seite enthält noch nicht gespeicherte Änderungen. Trotzdem die Seite verlassen?',
            saveLabel: 'Änderungen speichern',
            discardLabel: 'Änderungen verwerfen',
            cancelLabel: 'Abbrechen'
        },
        renamePlanPrompt: {
            okText: 'Plan umbenennen',
            cancelText: 'Alte Bezeichnung übernehmen',
            title: 'Plan umbenennen',
            message: 'Sie haben den Plan umbenannt. \'Möchten Sie die Änderungen speichern?',
            oldNamePrefix: 'von',
            newNamePrefix: 'bis'
        },
        renameWorkspacePrompt: {
            okText: 'DE_Rename workspace_DE',
            cancelText: 'Alte Bezeichnung übernehmen',
            title: 'DE_Rename workspace_DE',
            message: 'DE_You have renamed the workspace. Do you wish to save this change?_DE',
            oldNamePrefix: 'von',
            newNamePrefix: 'bis'
        },
        saveChangesPrompt: {
            okText: 'Plan ohne Speichern verlassen',
            cancelText: 'Zum Plan zurückkehren',
            title: 'Änderungen speichern unter',
            message: 'ist ein',
            planSuffix: 'Plan',
            saveAsPrivatePlanButtonLabel: 'Als neuen privaten Plan speichern',
            saveChangesToPublicButtonLabel: 'Änderungen am öffentlichen Plan speichern'
        },
        saveWorkspaceChangesPrompt: {
            okText: 'Plan ohne Speichern verlassen',
            cancelText: 'Zum Plan zurückkehren',
            title: 'Änderungen speichern unter',
            message: 'ist ein',
            planSuffix: 'DE_Workspace_DE',
            saveAsPrivatePlanButtonLabel: 'Als neuen privaten Plan speichern',
            saveChangesToPublicButtonLabel: 'Änderungen am öffentlichen Plan speichern'
        },
        deleteResourceSkillPrompt: {
            okText: 'Ja, Qualifikation entfernen',
            cancelText: 'Nein, Qualifikation übernehmen',
            selectedSkill: 'Ausgewählte Qualifikation',
            title: 'Qualifikation entfernen?',
            messagePrefix: 'Möchten Sie ...',
            messageSuffix: 'aus dem Profil entfernen?'
        },
        deleteCommentPrompt: {
            okText: 'Kommentar löschen',
            cancelText: 'Kommentar übernehmen',
            title: 'Kommentar löschen?'
        },
        singleCreateBookingErrorPrompt: {
            title: 'Fehler beim Erstellen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Erstellen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Erstellen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleCreateJobErrorPrompt: {
            title: 'Fehler beim Erstellen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Erstellen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Erstellen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleCreateClientErrorPrompt: {
            title: 'Fehler beim Erstellen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Erstellen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Erstellen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleCreateRolegroupErrorPrompt: {
            title: 'Fehler beim Erstellen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Erstellen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Erstellen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleEditBookingErrorPrompt: {
            title: 'Fehler beim Bearbeiten von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Bearbeiten der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleEditJobErrorPrompt: {
            title: 'Fehler beim Bearbeiten von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Bearbeiten der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleEditClientErrorPrompt: {
            title: 'Fehler beim Bearbeiten von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Bearbeiten der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleEditResourceErrorPrompt: {
            title: 'Fehler beim Bearbeiten des Profils',
            message: 'Es ist ein Fehler beim Bearbeiten des folgenden Profils aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieses Profils',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: 'Profil bearbeiten',
            discardButtonLabel: 'Profil verwerfen'
        },
        singleSaveTemplateErrorPrompt: {
            title: 'Fehler beim Erstellen der ${entitySingularLower} Vorlage',
            message: 'Es ist ein Fehler beim Erstellen der folgenden ${entitySingularLower} Vorlage aufgetreten:',
            insufficientPermissionsMessage: 'Unzureichende Berechtigungen zum Erstellen der ${entitySingularLower} Vorlage',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: '${entitySingularLower} Vorlage verwerfen',
            defaultRoleTemplateName: 'Neu ${entitySingularUpper}'
        },
        singleDeleteErrorPrompt: {
            title: 'Fehler beim Löschen der ${entitySingularLower} Vorlage',
            message: 'Es ist ein Fehler beim Löschen der folgenden ${entitySingularLower} Vorlage aufgetreten:',
            insufficientPermissionsMessage: 'Unzureichende Berechtigungen zum Löschen der ${entitySingularLower} Vorlage',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: '${entitySingularLower} Vorlage verwerfen',
            defaultRoleTemplateName: 'Neu ${entitySingularUpper}'
        },
        singleEditErrorPrompt: {
            title: 'Fehler beim Bearbeiten der ${entitySingularLower} Vorlage',
            message: 'Es ist ein Fehler beim Bearbeiten der folgenden ${entitySingularLower} Vorlage aufgetreten:',
            insufficientPermissionsMessage: 'Unzureichende Berechtigungen zum Bearbeiten der ${entitySingularLower} Vorlage',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: '${entitySingularLower} Vorlage verwerfen',
            defaultRoleTemplateName: 'Neu ${entitySingularUpper}'
        },
        singleEditRolegroupErrorPrompt: {
            title: 'Fehler beim Bearbeiten von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Bearbeiten der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleDeleteBookingErrorPrompt: {
            title: 'Fehler beim Löschen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Löschen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Löschen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleDeleteJobErrorPrompt: {
            title: 'Fehler beim Löschen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Löschen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Löschen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleDeleteClientErrorPrompt: {
            title: 'Fehler beim Löschen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Löschen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Löschen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        singleDeleteRolegroupErrorPrompt: {
            title: 'Fehler beim Löschen von ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Löschen der folgenden ${entitySingularLower} aufgetreten',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Löschen dieser ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: '${entitySingularLower} bearbeiten',
            discardButtonLabel: '${entitySingularLower} verwerfen'
        },
        carouselCreateErrorPrompt: {
            title: 'Fehler beim Bearbeiten von ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} von ${attemptedCount} ${entityString} erfolgreich bearbeitet.',
            permissionsErrorMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieser ${entityString}}',
            retry: 'Erneut versuchen',
            errorSectionMessage: 'Es ist ein Fehler beim Bearbeiten der folgenden ${entityString} aufgetreten',
            edit: '${entityString} bearbeiten',
            cancel: '${entityString} verwerfen',
            close: 'Schließen'
        },
        carouselRollForwardCreateErrorPrompt: {
            title: 'Fehler beim Erstellen von ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} von ${attemptedCount} ${entityString} erfolgreich erstellt.',
            permissionsErrorMessage: 'Keine entsprechenden Berechtigungen zum Erstellen dieser ${entityString}}',
            retry: 'Erneut versuchen',
            errorSectionMessage: 'Es ist ein Fehler beim Erstellen der folgenden ${entityString} aufgetreten',
            edit: 'Zurück zu den Optionen \'RollForward\'',
            cancel: 'Fehlgeschlagene ${entityString} verwerfen',
            close: 'Schließen'
        },
        carouselEditErrorPrompt: {
            title: 'Fehler beim Bearbeiten von ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} von ${attemptedCount} ${entityString} erfolgreich bearbeitet.',
            permissionsErrorMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieser ${entityString}}',
            retry: 'Erneut versuchen',
            errorSectionMessage: 'Es ist ein Fehler beim Bearbeiten der folgenden ${entityString} aufgetreten',
            edit: '${entityString} bearbeiten',
            cancel: '${entityString} verwerfen',
            close: 'Schließen'
        },
        carouselDeleteErrorPrompt: {
            title: 'Fehler beim Bearbeiten von ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} von ${attemptedCount} ${entityString} erfolgreich gelöscht.',
            permissionsErrorMessage: 'Keine entsprechenden Berechtigungen zum Löschen dieser ${entityString}}',
            retry: 'Erneut versuchen',
            errorSectionMessage: 'Es ist ein Fehler beim Löschen der folgenden ${entityString} aufgetreten',
            edit: '${entityString} bearbeiten',
            cancel: '${entityString} verwerfen',
            close: 'Schließen'
        },
        batchedCreateEntityErrorPrompt: {
            successfulCountMessage: '${succeededCount} von ${attemptedCount} ${entityString} erfolgreich erstellt.',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Erstellen dieser ${entityString}}',
            title: 'Fehler beim Erstellen von ${entityString}',
            errorSectionMessage: 'Es ist ein Fehler beim Erstellen der folgenden ${entityString} aufgetreten',
            retryButtonLabel: 'Erneut versuchen',
            cancelButtonLabel: 'Abbrechen',
            editEntitiesButtonLabel: '${entityString} bearbeiten',
            discardEntitiesButtonLabel: '${entityString} verwerfen',
            closeDialogButtonLabel: 'Schließen',
            tryAgainMessage: 'Möchten Sie es erneut versuchen?',
            requests: 'Anfragen'
        },
        deleteMultipleBookingsPrompt: {
            title: '${bookingsAliasUpper} löschen? (${bookingsCount})',
            message: 'Möchten Sie ${pronounString} ${bookingsAliasLower} löschen?',
            okText: 'Ja, ${bookingsAliasUpper} löschen',
            cancelText: 'Nein, ${bookingsAliasUpper} übernehmen',
            close: 'Schließen',
            theseString: 'diese',
            thatString: 'das'
        },
        cantPasteBarPrompt: {
            message: 'Markieren Sie eine Zelle, in die Sie Ihren ${barAliasLower} einfügen möchten'
        },
        deleteMultipleRolerequestsPrompt: {
            title: '${rolerequestsAliasUpper} löschen? (${rolerequestsCount})',
            message: 'Möchten Sie ${pronounString} ${rolerequestsAliasLower} löschen?',
            okText: 'Ja, ${rolerequestsAliasLower} löschen',
            cancelText: 'Nein, ${rolerequestsAliasLower} übernehmen',
            close: 'Schließen',
            theseString: 'diese',
            thatString: 'das'
        },
        moveRolerequestTimeAllocationPrompt: {
            title: 'Ausstehenden ${pluralFieldName} verschieben von ${rolerequestDescription}',
            message: 'Alle ausstehenden ${pluralFieldName}, die angefordert wurden, werden in einen neuen „Entwurf“  ${rolerequestsSingularLower} verschoben und dieser ${rolerequestsSingularLower} wird auf „Aktiv“ gesetzt. Zugeordnete Personen, die „Angefordert“ sind, bleiben unzugeordnet.',
            warningMessage: 'Dies ist nicht rückgängig zu machen.',
            okText: 'Ausstehende verschieben ${pluralFieldName}',
            cancelText: 'Anfrage offen halten',
            close: 'Schließen',
            FTEs: 'FTEs'
        },
        removeRolerequestTimeAllocationPrompt: {
            title: 'Ausstehende ${pluralFieldName} entfernen von ${rolerequestDescription}',
            message: 'Alle ausstehenden ${pluralFieldName}, die angefragt wurden, werden entfernt. Der ${rolerequestsSingularLower} wird auf „aktiv“ gesetzt. Zugeordnete Personen, die „Angefordert“ sind, bleiben unzugeordnet.',
            warningMessage: 'Dies ist nicht rückgängig zu machen.',
            okText: 'Ausstehende löschen${pluralFieldName}',
            cancelText: 'Anfrage offen halten',
            close: 'Schließen',
            FTEs: 'FTEs'
        },
        createRolegroupModal: {
            placeholder: 'Neue ${roleGroupAlias}',
            title: '${roleGroupAlias} erstellen',
            nameDescriptor: 'Bezeichnung',
            createLabel: 'Erstellen',
            cancelLabel: 'Abbrechen',
            currentValue: '${jobDescription} ${roleGroupAlias} ${currentSubsequentNumber}',
            helpMessage: 'Bitte geben Sie eine Bezeichnung für Ihre ${roleGroupAlias} ein',
            maxLengthValidationMessage: 'Maximal ${maxNameLength}-Symbole zulässig'
        },
        saveAsTemplateModal: {
            placeholder: 'Neue ${roleAlias} Vorlage',
            title: 'Neue Vorlage',
            headerTitle: 'Die neue ${roleAlias} Vorlage benennen und speichern.',
            nameDescriptor: 'Name',
            createLabel: 'Speichern',
            cancelLabel: 'Abbrechen',
            currentValue: '${rolerequestDescription}',
            defaultNewRole: 'Neu ${roleAlias}',
            helpMessage: 'Bitte geben Sie eine Bezeichnung für Ihre ${rolerequestDescription} Vorlage ein',
            maxLengthValidationMessage: 'Maximal ${maxNameLength} Symbole zulässig'
        },
        progressRolesWindow: {
            title: 'Fehler bei der Verarbeitung von ${roleAlias}'
        },
        deleteRoleGroupPrompt: {
            title: '${roleGroupDescription} löschen?',
            roleGroupInfoMessage: 'Es gibt <bold>${rolesNumber}</bold> ${roleAliasPlural} einschließlich <bold>${roleRequests}-</bold> Anfagen in dieser ${roleGroupAliasSingular} zwischen <bold>${roleStartDate}</bold> - <bold>${roleEndDate}</bold>. Das Löschen dieser ${roleGroupAliasSingular} löscht ebenfalls diese ${roleAliasPlural} und Anfragen.',
            shouldDeleteQuestion: 'Möchten Sie ${roleGroupDescription} Dauerhaft löschen? <bold>${roleGroupDescription}</bold>?',
            checkboxText: '${roleGroupAliasSingular}, ${roleAliasPlural} und Anfragen löschen.',
            cancelMessage: '${roleGroupAliasSingular} übernehmen',
            confirmMessage: '${roleGroupAliasSingular} löschen'
        },
        extendJobRangeDetailsPagePrompt: {
            okText: 'Ja, Auftragsdaten ändern',
            cancelText: 'Nein, Terminplanung abbrechen',
            title: 'Den Auftrag verlängern?',
            message: 'Möchten Sie den Auftragszeitraum verlängern, um die Einplanung',
            roleTailMessage: 'dieser Position zuzulassen?',
            rolesTailMessage: 'dieser Positionen zuzulassen?',
            detailsText: 'Neue Auftragsdaten für'
        },
        singleMoveResourceRolegroupErrorPrompt: {
            title: 'Fehler beim verschieben eines ausstehenden Punkts ${resourcePluralLower}',
            message: 'Beim Verschieben des folgenden Punkts ist ein Fehler aufgetreten ${entitySingularLower}',
            insufficientPermissionsMessage: 'Unzureichende Genehmigungen für die Bewegung ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: 'Verwerfen ${entitySingularLower}'
        },
        singleRemoveResourceRolegroupErrorPrompt: {
            title: 'Fehler beim Entfernen eines ausstehenden Punkts ${resourcePluralLower}',
            message: 'Beim Entfernen des folgenden Punkts ist ein Fehler aufgetreten ${entitySingularLower}',
            insufficientPermissionsMessage: 'Unzureichende Genehmigungen für die Bewegung ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: 'Verwerfen ${entitySingularLower}'
        },
        singleMoveFTERolegroupErrorPrompt: {
            title: 'Fehler beim Verschieben ausstehender FTEs',
            message: 'Beim Verschieben der ausstehenden FTEs für den folgenden ${entitySingularLower} ist ein Fehler aufgetreten:',
            insufficientPermissionsMessage: 'Unzureichende Genehmigungen für die Bewegung ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: 'Verwerfen ${entitySingularLower}'
        },
        singleRemoveFTERolegroupErrorPrompt: {
            title: 'Fehler beim Entfernen ausstehender FTEs',
            message: 'Beim Entfernen der ausstehenden FTEs für den folgenden ${entitySingularLower} ist ein Fehler aufgetreten:',
            insufficientPermissionsMessage: 'Unzureichende Genehmigungen für die Bewegung ${entitySingularLower}',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: 'Verwerfen ${entitySingularLower}'
        },
        singlePublishRoleErrorPrompt: {
            title: 'Fehler beim Veröffentlichen ${entitySingularLower}',
            message: 'Es ist ein Fehler beim Löschen der folgenden ${entitySingularLower} aufgetreten:',
            insufficientPermissionsMessage: 'Unzureichende Berechtigungen zum Veröffentlichen dieser ${entitySingularLower} in ${marketplaceAlias}',
            retryButtonLabel: 'Erneut versuchen',
            editButtonLabel: 'Veröffentlichungsdetails bearbeiten',
            discardButtonLabel: 'Verwerfen ${entitySingularLower}'

        },
        singleEditRolePublicationErrorPrompt: {
            title: 'Fehler beim Bearbeiten der ${entitySingularLower} Veröffentlichung',
            message: 'Es ist ein Fehler beim Veröffentlichen der folgenden ${entitySingularLower} Veröffentlichung aufgetreten:',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Bearbeiten dieser ${entitySingularLower} Veröffentlichung',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: 'Verwerfen ${entitySingularLower}'
        },
        singleRemoveRolePublicationErrorPrompt: {
            title: 'Fehler beim Entfernen der ${entitySingularLower} Veröffentlichung',
            message: 'Es ist ein Fehler beim Entfernen der folgenden ${entitySingularLower} Veröffentlichung aufgetreten:',
            insufficientPermissionsMessage: 'Keine entsprechenden Berechtigungen zum Entfernen dieser ${entitySingularLower} Veröffentlichung aus ${marketplaceAlias}',
            retryButtonLabel: 'Erneut versuchen',
            discardButtonLabel: 'Verwerfen ${entitySingularLower}'
        },
        updateRolerequestStatusWindow: {
            title: 'Fehler beim Aktualisieren des Status von ${roleAlias}'
        },
        publishRoleErrorPrompt: {
            title: 'Fehler beim Veröffentlichen ${roleAlias}'
        },
        editRolePublicationPrompt: {
            title: 'Fehler beim Bearbeiten der ${roleAlias} Veröffentlichung'
        },
        withdrawRoleApplicationPrompt: {
            title: 'Zurückziehen bestätigen',
            question: 'Sie werden für diese(s)(en) ... nicht mehr berücksichtigt ${entitySingularLower}.',
            warning: 'Sind Sie sicher, dass Sie Ihre Bewerbung zurückziehen möchten?',
            okText: 'Ja, meine Bewerbung zurückziehen',
            cancelText: 'Nein, meine Bewerbung übernehmen'
        },
        tableViewHoursValidationPrompt: {
            title: 'Ungültige Stunden',
            message: 'Sie können zwischen 0 und 168 Stunden pro Woche buchen.',
            okText: 'OK',
            tooltipText: 'Zum Schließen Escape-Taste drücken'
        },
        tableViewCellEditErrornPrompt: {
            title: 'Fehler beim Speichern von Änderungen',
            errorMessage: 'Aktualisierungen auf der ${tableViewPageAlias}-Seite erfordern möglicherweise eine Kombination aus Erstellen, Bearbeiten und Löschen von ${bookingPluralForm} im Hintergrund.',
            contactMessage: 'Wenden Sie sich bitte an Ihren Administrator und vergewissern Sie sich, dass Sie die erforderlichen Berechtigungen haben.',
            discardText: 'Änderungen verwerfen',
            retryText: 'Erneut versuchen'
        },
        splitBookingsErrorPrompt: {
            title: 'Fehler beim Speichern von Änderungen',
            question: 'Die ausgewählte ${bookingSingularLowerAlias} wurde möglicherweise von einer anderen Person gelöscht oder bearbeitet, oder Sie haben keine ausreichenden Berechtigungen. Die Aufteilung von ${bookingPluralLowerAlias} erfordert eine Kombination aus Erstellung und Bearbeitung von im ${bookingPluralLowerAlias} Hintergrund.',
            warning: 'Bitte aktualisieren Sie die Seite und versuchen Sie es erneut, oder wenden Sie sich an Ihren Administrator.',
            cancelText: 'OK'
        },
        setPasswordConfirmationPrompt: {
            title: 'Bestätigung erforderlich',
            question: 'Wenn das Kennwort geändert wird, verlieren bestehende Integrationen ihre Gültigkeit. Sind Sie sicher?',
            okText: 'Ja, bin ich sicher',
            cancelText: 'Nein, zurück'
        },
        duplicateJobErrorPrompt: {
            title: 'Fehler beim Speichern der Änderungen',
            errorMessage: 'Das kann an einem oder mehreren der folgenden Gründe liegen:',
            suggestedActions: [
                'Diese ${jobSingularLowerAlias} hat entweder keine ${bookingPluralLowerAlias} im ausgewählten Datumsbereich',
                'Die ${jobSingularLowerAlias} Duplikationswarteschlange ist voll',
                'Sie haben nicht genügende Rechte für die ausgewählten ${bookingPluralLowerAlias}, da das Duplizieren eines ${jobSingularLowerAlias} das Erstellen von ${bookingPluralLowerAlias} im Hintergrund erfordert'
            ],
            contactMessage: 'Aktualisieren Sie die Seite und versuchen Sie es erneut oder wenden Sie sich an Ihren Administrator.',
            okText: 'OK'
        },
        duplicateRoleGroupErrorPrompt: {
            title: 'Fehler bei der Erstellung ${rolerequestPluralLowerAlias}',
            errorMessage: 'Ihnen fehlen die Berechtigungen zur Erstellung dieser ${rolerequestPluralLowerAlias}.',
            contactMessage: 'Aktualisieren Sie die Seite und versuchen Sie es erneut oder wenden Sie sich an Ihren Administrator.E',
            okText: 'Wiederholen',
            cancelText: 'Abbrechen'
        },
        deleteOperationLogPrompt: {
            title: 'In diesem Vorgang angelegte Elemente löschen?',
            shouldDeleteQuestion: 'Sollen die in diesem Vorgang angelegten Elemente dauerhaft gelöscht werden? Falls neue Jobs angelegt wurden, werden alle für diese Jobs erstellten Buchungen, Szenarien oder Funktionen ebenfalls gelöscht.',
            checkboxText: 'In diesem Vorgang angelegte Elemente löschen',
            cancelMessage: 'Elemente beibehalten',
            confirmMessage: 'Elemente löschen'
        },
        confirmMassDuplicatePrompt: {
            title: 'Duplizieren',
            message: 'Ihr Vorgang wurde in die Warteschlange gestellt. Sie können den Status im Vorgangsprotokoll verfolgen.',
            closeText: 'OK'
        },
        updateSeriesBookingPrompt: {
            message: 'Diese Buchungen werden gelöscht und mit aktualisierten Informationen neu erstellt.',
            okText: 'Fortfahren',
            cancelText: 'Abbrechen'
        }
    },
    comments: {
        editedFlag: 'bearbeitet',
        editButtonLabel: 'Bearbeiten',
        deleteButtonLabel: 'Löschen',
        confirmEditButtonLabel: 'Aktualisieren',
        cancelButtonLabel: 'Abbrechen',
        createButtonLabel: 'Kommentar hinzufügen',
        createPlaceholder: 'Mit der Eingabe eines Kommentars beginnen',
        showMoreButtonLabel: 'Weitere Kommentare anzeigen'
    },
    navigation: {
        title: 'Hilfe',
        contactSupport: 'Support kontaktieren',
        helpPageLink: 'Hilfe-Dokumentation',
        keyboardShortcuts: 'Tastaturkürzel',
        legend: 'Legende',
        operationLogButtonLabel: 'Vorgangsprotokoll',
        notifications: 'Benachrichtigungen',
        settings: 'Einstellungen',
        logout: 'Abmelden'
    },
    skills: {
        noAddedSkills: 'Keine Kompetenzen hinzugefügt',
        noRecommendations: 'Keine neuen Empfehlungen',
        recommendationTitle: 'Empfehlungen',
        mySkillsLabel: 'DE_My skills_DE',
        approvalRequestsLabel: 'DE_Approval requests_DE',
        approvalRequestSent: 'DE_Approval request sent_DE',
        noSkillPendingRequestsLabel: 'DE_No skill update requests_DE',
        noSkillApprovalHistoryLabel: 'DE_No skill approval historic requests_DE',
        skillApprovalHistoryLabel: 'DE_Historic requests are automatically removed after 1 year_DE',
        skillTagLabel: 'Schlagwort',
        skillCategoryLabel: 'Kategorien',
        defaultSelectedSection: 'Alle Qualifikationstypen übernehmen',
        noMatchingSkillsText: 'Keine passenden Qualifikationen gefunden.',
        searchPlaceholder: 'Nach einer Qualifikation suchen',
        saveButtonLabel: 'Qualifikationen hinzufügen',
        cancelButtonLabel: 'Abbrechen',
        headerTitle: 'Qualifikationen hinzufügen',
        primarySaveButtonLabel: 'Speichern',
        skillsToAddSectionName: 'Hinzuzufügende Qualifikationen',
        addSkillsButtonLabel: 'Qualifikationen hinzufügen',
        editSkillsButtonLabel: 'Qualifikationen bearbeiten',
        skillsSectionTitle: 'Qualifikationen',
        expandAllCaption: 'Alle erweitern',
        collapseAllCaption: 'Alle komprimieren',
        singularSkillString: 'Qualifikation',
        pluralSkillsString: 'Qualifikationen',
        markDeletedMessage: 'Zum Löschen markiert. Wird nach Bestätigung der Änderungen gelöscht.',
        cancelDeletionMessage: 'Löschen abbrechen',
        skillLevelDeletedMessage: 'Das Qualifikationsniveau wurde für diese Qualifikation gelöscht. Es muss ein neues Niveau festgelegt werden.',
        validationRequiredText: 'ist erforderlich',
        validationLessThanText: 'darf nicht kleiner sein als',
        validationGreaterThanText: 'darf nicht größer sein als',
        validationIntegerNumberText: 'sollte eine ganze Zahl sein',
        maxCharactersPrefix: 'Maximale ',
        maxCharactersSuffix: 'Anzahl an Zeichen',
        tagsPrefixText: 'Schlagwörter',
        markedForDeletionMessage: 'Zum Löschen markiert. Wird nach Bestätigung der Änderungen gelöscht.',
        deleteLabel: 'Löschen ${skillName}',
        cancelDeletionSkillLabel: 'Aufheben der Löschung von ${skillName}',
        noValueMessage: 'Kein ${fieldInfoAlias} festgelegt',
        insufficientPermissionsToEditSuffix: 'Keine ausreichenden Berechtigungen zum Bearbeiten',
        searchSkillFilterCascaderPlaceholder: 'DE_Select skills and levels_DE',
        noManagerToApproveMessage:'DE_You do not have a manager to approve your skill updates_DE'
    },
    pages: {
        plannerPage: 'Pläne',
        adminSettings: 'Einstellungen',
        jobsPage: 'Aufträge',
        talentProfile: 'Talentprofil',
        translation: 'Mein Profil',
        report: 'Bericht',
        logout: 'Abmelden',
        collapseText: 'Komprimieren',
        expandText: 'Erweitern',
        errorMessages: {
            goBackText: 'Zur letzten Seite zurückgehen',
            goToText: 'Gehe zu',
            errorCodeMessagePrefix: 'Fehler',
            defaultHeaderText: 'Etwas ist schief gelaufen',
            closeText: 'Schließen',
            '401': {
                headerText: 'Nicht autorisiert.',
                message: 'Ihre Sitzung ist abgelaufen. Bitte melden Sie sich erneut an.'
            },
            '403': {
                headerText: 'Zugriff verweigert.',
                message: 'Keine entsprechenden Berechtigungen.'
            },
            '404': {
                headerText: 'Es tut uns leid.',
                message: 'Wir können die Seite, die Sie suchen, nicht finden.'
            },
            error: {
                headerText: 'Es tut uns leid.',
                message: 'Da scheint etwas schief gelaufen zu sein.',
                errorCodeMessage: 'Es gab ein Problem. Versuchen Sie, die Seite erneut zu laden.'
            },
            calculateFteError: {
                headerText: 'FTE konnte nicht berechnet werden',
                message: 'Bitte ein gültiges FTE-Referenztagebuch für diese Aufgabe auswählen.'
            }
        }
    },
    manageEntityLookupWindow: {
        searchForLabel: 'Suchen nach ...',
        showLessLabel: 'Weniger anzeigen',
        showMoreLabels: {
            prefix: 'Mehr',
            suffix: 'anzeigen'
        },
        noResultsMessagePrefix: 'Nein',
        noResultsMessageSuffix: 'wurde mit dieser Bezeichnung gefunden.'
    },
    contextualMenu: {
        createEntity: 'Erstellen',
        editEntity: 'Bearbeiten',
        deleteEntity: 'Löschen',
        copyEntity: 'Kopieren',
        rollForwardEntity: 'Duplizieren',
        cutEntity: 'Ausschneiden',
        pasteEntity: 'Einfügen',
        clearEntity: 'Löschen',
        setDateRange: 'Datumsbereich festlegen',
        loadingCaption: '... lädt',
        restart: 'Neustarten',
        archive: 'Archivieren',
        reject: 'Ablehnen',
        makeLive: 'Aktivieren',
        submitRequest: 'Anfrage absenden',
        rollForwardTooltipText: 'Ausgewählten ${bookingEntityAlias} in einen anderen ${jobEntityAlias} oder ein anderes Datum kopieren',
        unassignResource: 'Zuweisung trennen für ${rolerequestSingularLowerAlias}',
        createCriteriaRole: '${rolerequestSingularCapitalAlias} nach Anforderungen erstellen',
        createRoleByName: '${rolerequestSingularCapitalAlias} nach Bezeichnung erstellen',
        movePendingResources: 'Ausstehende verschieben ${resourcePluralLowerAlias}',
        removePendingResources: 'Ausstehende löschen ${resourcePluralLowerAlias}',
        movePendingFTEs: 'Ausstehende FTEs verschieben',
        removePendingFTEs: 'Ausstehende FTEs entfernen',
        manageBudget: 'Budget verwalten',
        saveAsTemplate: 'Save as template',
        showInViewLabel: 'In der Ansicht ${pluralViewNameAlias} anzeigen'
    },
    detailsPane: {
        paneLabel: 'Bereich',
        inModalLabel: 'Bereich in Modal',
        showPanePrefixLabel: 'Mehr',
        showPaneSuffixLabel: 'anzeigen',
        suggestionsSingular: 'Vorschlag',
        suggestionsPlural: 'Vorschläge'
    },
    blankValues: {
        notFoundString: 'nicht gefunden',
        dateNotFoundString: 'Datum nicht gefunden',
        noChargeTypeSetString: 'Keine Gebührenart festgelegt',
        unspecifiedString: 'nicht spezifiziert',
        noString: 'Nein',
        setString: 'festgelegt'
    },
    hotKeysHelpWindow: {
        generalLabel: 'Allgemeines',
        helpWindowTitle: 'Tastaturkürzel',
        bookingsLabel: '${bookingPluralCapitalAlias}',
        jobsLabel: '${jobPluralCapitalAlias}',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        rolesLabel: '${rolerequestPluralCapitalAlias}',
        dateManipulationLabel: 'Datumsmanipulation',
        menusLabel: 'Menüs',
        filtersLabel: 'Filter',
        viewOptionsLabel: 'Optionen anzeigen',
        createLabel: 'Erstellen',
        helpLabel: 'Hilfe',
        helpDocumentationLabel: 'Hilfe-Dokumentation',
        orLabel: 'ODER',
        plannerPage: {
            addLabel: 'Hinzufügen',
            editLabel: 'Bearbeiten',
            cutLabel: 'Ausschneiden',
            copyLabel: 'Kopieren',
            pasteLabel: 'Einfügen',
            deleteLabel: 'Löschen',
            restartLabel: 'Neustarten',
            archiveLabel: 'Archivieren',
            rejectLabel: 'Ablehnen',
            makeLiveLabel: 'Live schalten',
            submitRequestLabel: 'Anfrage einreichen',
            requestLabel: '${bookingSingularLowerAlias} anfragen',
            createBookingFromRequestLabel: '${bookingSingularLowerAlias} erstellen',
            expandDateRangeLabel: 'Datumsbereich vergrößern',
            reduceDateRangeLabel: 'Datumsbereich verringern',
            setRangeTo5DayLabel: 'Datumsbereich auf 5 Tage festlegen',
            setRangeTo7DaysLabel: 'Datumsbereich auf 7 Tage festlegen',
            setRangeTo10DaysLabel: 'Datumsbereich auf 10 Tage festlegen',
            setRangeTo2WeekLabel: 'Datumsbereich auf 2 Wochen festlegen',
            setRangeTo4WeeksLabel: 'Datumsbereich auf 4 Wochen festlegen',
            setRangeTo6WeeksLabel: 'Datumsbereich auf 6 Wochen festlegen',
            setRangeTo2MonthsLabel: 'Datumsbereich auf 2 Monate festlegen',
            setRangeTo3MonthsLabel: 'Datumsbereich auf 3 Monate festlegen',
            setRangeTo6MonthsLabel: 'Datumsbereich auf 6 Monate festlegen',
            setRangeTo1YearLabel: 'Datumsbereich auf 1 Jahr festlegen',
            goToTodayLabel: 'Gehe zu heute',
            addMenuLabel: 'Menü hinzufügen',
            editMenuLabel: 'Menü bearbeiten',
            viewMenuLabel: 'Menü anzeigen',
            addJobLabel: '${jobSingularLowerAlias} hinzufügen',
            findResourcesLabel: 'Suche ${resourcePluralLowerAlias}',
            defaultDensityLabel: 'Dichteanzeige auf Standard setzen',
            mediumDensityLabel: 'Dichteanzeige auf Medium setzen',
            expandedDensityLabel: 'Dichteanzeige auf Erweitert setzen',
            helpWindowLabel: 'Hilfefenster',
            openLegendLabel: 'Farbschema-Legende öffnen',
            showHideWeekendsLabel: 'Wochenenden anzeigen / verbergen',
            showHidePotentialConflictsLabel: 'Mögliche Konflikte ein-/ausblenden',
            addRoleByName: '${rolerequestSingularCapitalAlias} nach Bezeichnung hinzufügen',
            addRoleByRequirements: '${rolerequestSingularCapitalAlias} nach Anforderungen hinzufügen',
            editRoleByNameLabel: '${rolerequestSingularCapitalAlias} nach Bezeichnung bearbeiten',
            editRoleByCriteriaLabel: '${rolerequestSingularCapitalAlias} nach Anforderungen bearbeiten',
            rollForwardLabel: 'Duplizieren',
            movePendingResources: 'Ausstehende Ressourcen verschieben',
            removePendingResources: 'Ausstehende Ressourcen entfernen',
            movePendingFTEs: 'Ausstehende FTEs verschieben',
            removePendingFTEs: 'Ausstehende FTEs entfernen',
            splitBookingLabel: 'Teilen ${bookingSingularLowerAlias}',
            showInViewLabel: 'In der Ansicht ${jobPluralLowerAlias}/${resourcePluralLowerAlias} anzeigen',
            dragToSelect: 'Zum Auswählen wischen'
        },
        jobsPage: {
            addLabel: 'Hinzufügen',
            editLabel: 'Bearbeiten',
            deleteLabel: 'Löschen',
            addMenuLabel: 'Menü hinzufügen',
            editMenuLabel: 'Menü bearbeiten',
            compactDensityLabel: 'Dichteanzeige auf Kompakt setzen',
            defaultDensityLabel: 'Dichteanzeige auf Standard setzen',
            expandedDensityLabel: 'Dichteanzeige auf Erweitert setzen',
            helpWindowLabel: 'Hilfefenster'
        },
        roleInboxPage: {
            movePendingResources: 'Ausstehende Ressourcen verschieben',
            removePendingResources: 'Ausstehende Ressourcen entfernen',
            makeLiveLabel: 'Live schalten',
            submitRequestLabel: 'Anforderung übermitteln',
            restartLabel: 'Neustart',
            rejectLabel: 'Ablehnen',
            deleteLabel: 'Löschen',
            archiveLabel: 'Archiv',
            addRoleByName: '${rolerequestSingularLowerAlias} nach Bezeichnung hinzufügen',
            addRoleByRequirements: '${rolerequestSingularLowerAlias} nach Anforderungen hinzufügen',
            editRoleByNameLabel: '${rolerequestSingularCapitalAlias} nach Name bearbeiten',
            editRoleByCriteriaLabel: '${rolerequestSingularCapitalAlias} nach Anforderungen bearbeiten',
            helpWindowLabel: 'Hilfefenster',
            publishToMarketplaceLabel: 'Auf ${marketplaceAlias} veröffentlichen',
            saveAsTemplate: 'Als Vorlage speichern'
        },
        adminSettingsPage: {
            addNewItemLabel: 'Neuen Punkt hinzufügen',
            helpWindowLabel: 'Hilfefenster'
        }
    },
    validationMessages: {
        unableToSaveChanges: 'Die Änderungen können nicht gespeichert werden. Bitte überprüfen Sie die hervorgehobenen Fehler.',
        mandatoryFieldsNotCompleted: 'Pflichtfelder müssen ausgefüllt werden',
        formHasErrors: 'Dieses Formular enthält Fehler',
        activeUserText: 'ist inaktiv. Auf eine aktive oder nicht vergebene Ressource setzen, um Änderungen zu speichern',
        fieldMandatoryText: 'Das ist ein Pflichtfeld',
        mandatoryText: 'ist obligatorisch',
        minimumText: 'Mindestens',
        maximiumText: 'Maximal',
        maxCharactersPrefix: 'Maximal',
        maxCharactersSuffix: 'Zeichen',
        selectValidText: 'Bitte wählen Sie ein(e)(en) gültig(e)(en)',
        invalidNamePrefix: 'Ungültige',
        invalidNameSuffix: 'Bezeichnung',
        integerTypeText: 'Typ ist eine ganze Zahl',
        maxSelectedYearText: 'Ausgewähltes Jahr sollte vor 10000 liegen',
        minSelectedYearText: 'Ausgewähltes Jahr sollte nach 0000 liegen',
        startDateInvalid: 'Ungültiges Startdatum eingegeben',
        jobEndBeforeJobStart: 'Ende kann nicht vor dem Startdatum liegen',
        fteMaxValidationText: 'Gesamt-VZÄ übersteigt angeforderte Menge',
        fteString: 'VZÄ'
    },
    attachmentsMessages: {
        uploadButtonLabel: 'Dokumente hochladen',
        deleteButtonLabel: 'Löschen',
        fileTooLargeLabel: 'Hochladen des Dokuments fehlgeschlagen: Datei ist zu groß',
        fileTypeForbiddenLabel: 'Hochladen des Dokuments fehlgeschlagen: Dateityp ist nicht zulässig',
        noFilesUploadedLabel: 'Sie haben keine Dokumente hochgeladen',
        uploadsLimitReachedLabel: 'Dokumentenlimit erreicht: Löschen Sie einige Dokumente, um weitere hochzuladen',
        allowedFormatsLabel: 'Dokumente können in folgenden Formaten vorliegen ${formattedAcceptedFileTypes}',
        maxFileSizeLabel: 'Es gilt eine maximale Dateigröße von ${defaultMaxFileSizeMb}MB pro Dokument',
        maxUploadsAllowed: 'Es können maximal ${defaultMaxUploadsAllowed} Dokumente hochgeladen werden'
    },
    treeSelectionMessages: {
        chargeMode: 'Abrechnungsmodus',
        revenue: 'Umsatz',
        cost: 'Kosten',
        profit: 'Gewinn',
        dateRange: 'Datumsbereich',
        timeAllocation: 'Zeitkontingent',
        selectFieldsCaption: 'Felder auswählen',
        addButtonCaption: 'Hinzufügen',
        historyFieldsSuffix: '(Überschreiben von Datum)'
    },
    contextualDropdown: {
        detailsLabel: 'Details',
        editLabel: 'Bearbeiten',
        duplicateLabel: 'Duplizieren',
        viewLabel: 'Anzeigen',
        newLabel: 'Neu',
        deleteLabel: 'Löschen',
        roleByName: '${roleSingularCapitalAlias} nach Bezeichnung',
        roleByRequirements: '${roleSingularCapitalAlias} nach Anforderungen',
        newRoleLabel: 'Neue ${roleSingularLowerAlias}',
        editDetailsLabel: 'Details bearbeiten',
        archiveLabel: 'Archivieren',
        restartLabel: 'Neustarten',
        rejectLabel: 'Ablehnen',
        makeLiveLabel: 'Aktivieren',
        submitRequestLabel: 'Anfrage absenden',
        createLabel: 'Erstellen',
        unassignLabel: 'Zuweisung trennen für',
        createBookingEllipsisLabel: 'erstellen ...${bookingSingularCapitalAlias}',
        createRoleByNameEllipsisLabel: 'nach Namen erstellen...${rolerequestSingularCapitalAlias}',
        editEllipsisLabel: 'Bearbeiten...',
        goToProfileEllipsisLabel: 'Auf Profil gehen',
        copyProfileUrlEllipsisLabel: 'Profil-URL kopieren',
        movePendingFTE: 'Ausstehende FTEs verschieben',
        removePendingFTE: 'Ausstehende FTEs entfernen',
        movePendingResourcesLabel: 'Ausstehende verschieben ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'Ausstehende löschen ${resourcePluralLowerAlias}',
        manageBudgetLabel: 'Budget verwalten',
        saveAsTemplateLabel: 'Als Vorlage speichern',
        publishToMarketplaceLabel: 'Veröffentlichen in ${marketplaceAlias}',
        editRolePublicationButtonLabel: 'Veröffentlichung ${rolerequestSingularLowerAlias} bearbeiten',
        removeRolePublicationButtonLabel: 'Veröffentlichung ${rolerequestSingularLowerAlias} entfernen',
        detailsJobLabel: '${jobSingularCapitalAlias} Details',
        editJobLabel: 'Bearbeiten ${jobSingularLowerAlias}',
        duplicateJobLabel: 'Duplizieren ${jobSingularLowerAlias}',
        viewRoleRequestGroupLabel: 'Vergleichen ${rolerequestgroupSingularLowerAlias}',
        newRoleRequestGroupLabel: 'Erstellen ${rolerequestgroupSingularLowerAlias}',
        detailsResourceLabel: '${resourceSingularCapitalAlias} details',
        openLabel: 'Öffnen',
        moreOptionsButtonLabel: 'Weitere Optionen'
    },
    progressRolesWindow: {
        totalText: 'Gesamtänderung an Auftrag',
        cancelText: 'Abbrechen',
        progressRoleLabel: 'Fortschrittsrolle',
        jobLabel: 'Aufgabe',
        roleLabel: 'Rolle',
        dateRangeLabel: 'Datumsbereich',
        budgetLabel: 'Budget',
        makeLive: {
            selectMessage: '${rolePluralAlias} zum Fortfahren mit ${bookingPluralAlias} wählen',
            title: 'Live schalten',
            submitText: 'Live schalten'
        },
        submitRequest: {
            selectMessage: '${rolePluralAlias} für Anfrage als ${bookingPluralAlias} wählen',
            title: 'Anforderung übermitteln',
            submitText: 'Anforderung übermitteln'
        }
    },
    progressRoleErrors: {
        alreadyLiveMsg: 'Bereits aktiv',
        noPermissionsMsg: 'Keine entsprechenden Berechtigungen'
    },
    rejectRolesWindow: {
        title: 'Ablehnen',
        submitText: 'Anfrage ablehnen',
        cancelText: 'Abbrechen',
        rejectErrorMessage: '„Es gab ein Problem. Die Aktion konnte nicht abgeschlossen werden',
        buttonLabel: 'Schließen',
        rejectReasonText: 'Wählen Sie den Grund für die Ablehnung dieser Positionsanfrage',
        errorDialogTitle: 'Fehler beim der Übertragung der Position',
        customReasonPlaceholderText: 'Verfassen eines benutzerdefinierten Grundes für die Ablehnung dieser Position',
        jobLabel: 'Aufgabe',
        roleLabel: 'Rolle',
        dateRangeLabel: 'Datumsbereich',
        budgetLabel: 'Budget',
        statusLabel: 'Status'
    },
    carousel: {
        defaultRoleName: 'Neues ${rolerequestSingularCapitalAlias}',
        ungrouped: 'Kein ${rolerequestgroupSingularLowerAlias} festgelegt'
    },
    rollForwardDialog: {
        title: 'Duplizieren ${bookingEntityAlias}',
        submitText: 'Erstellen ${bookingEntityAlias}',
        cancelText: 'Abbrechen',
        duplicateBooking: '${bookingSingularCapitalAlias} dupliziert',
        duplicateBookings: '${bookingPluralLowerAlias} dupliziert',
        forwardOptions: {
            alertMessage: 'Kopieren ${noOfBooking} ${bookingEntityAlias}',
            destinationStartDateLabel: 'Zielstartdatum',
            destinationStartDateLabelError: 'Das Zielstartdatum ist ein Pflichtfeld',
            destinationStartDateLabelErrorDescription: 'Die relativen Positionen der ausgewählten ${bookingEntityAlias} bleiben nach der Duplizierung erhalten',
            destinationJobLabel: 'Ziel ${jobSingularAlias}',
            destinationJobError: 'Ziel ist obligatorisch ${jobSingularAlias}',
            destinationBookingTypeLabel: 'Ziel ${bookingEntityAlias}',
            destinationBookingTypeError: 'Ziel -Typ ist obligatorisch ${bookingEntityAlias}',
            destinaltionJobExplanation: 'Die Zielbereiche für ${jobSingularLowerAlias} werden entsprechend geändert.',
            offsetExplanation: 'Ab dem Beginn der Erstauswahl von ${bookingEntityAlias}',
            editBookingLabel: 'Nach ${bookingEntityAlias} Duplizierung bearbeiten',
            editBookingDescription: 'Öffnet den Bearbeitungsdialog, um weitere Änderungen an dem neuen Eintrag vorzunehmen ${bookingEntityAlias}',
            valuePostfix: '${bookingEntityAlias}',
            keepBookingTypeText: 'Typ ${bookingEntityAlias} so belassen',
            onPrefix: 'Am',
            inPrefix: 'In'
        }
    },
    repeatBookingDialog: {
        createRepeatBooking: {
            title: 'Wiederholung festlegen',
            submitText: 'Speichern',
            cancelText: 'Abbrechen',
            repeatEvery: 'Wiederholung alle',
            repeatUntil: 'Bis',
            noRepeatText: 'Keine Wiederholung',
            positiveWholeNumberErrorMessage: 'Bitte geben Sie eine positive ganze Zahl ein'
        },
        editRepeatBooking: {
            title: 'Welche sich wiederholenden Buchungen sollen bearbeitet werden?',
            selectedOnly: 'Nur ausgewählte Buchung',
            selectedAndFuture: 'Ausgewählte und folgende Buchungen',
            allBookings: 'Alle Buchungen in der Serie',
            actionLabel: 'Serie bearbeiten',
            singleBookingMessage: 'Sie bearbeiten eine einzelne Buchung in einer Serie.',
            singleAndFutureBookingsMessage: 'Sie bearbeiten diese und alle folgenden Buchungen in einer wiederkehrenden Serie.',
            allBookingsMessage: 'Sie bearbeiten alle Buchungen in einer sich wiederholenden Serie.',
            partOfSeriesMessage: 'Diese Buchung ist Teil einer sich wiederholenden Serie.',
            updateFailureMessage: 'Fehler beim Aktualisieren der Buchungsserie.',
            bulkBookingMessage: 'Sie bearbeiten einzelne Vorkommen von Buchungen in einer sich wiederholenden Serie.',
            editedSingleBookingMessage: 'Diese Buchung ist Teil einer sich wiederholenden Serie, wurde aber separat bearbeitet. Einige Informationen sind möglicherweise unterschiedlich.'
        },
        deleteRepeatBooking: {
            title: 'Welche sich wiederholenden Buchungen sollen gelöscht werden?',
            cannotBeUndone: 'Diese Aktion kann nicht rückgängig gemacht werden.',
            selectedOnly: 'Nur ausgewählte Buchung',
            selectedAndFuture: 'Ausgewählte und folgende Buchungen',
            allBookings: 'Alle Buchungen in der Serie'
        },
        doesNotRepeatText: 'wiederholt sich nicht',
        repeatsEveryText: 'wiederholt sich alle',
        on: 'am',
        starting: 'Beginn',
        until: 'bis',
        intervalText: {
            day: 'Tag',
            days: 'Tage',
            week: 'Woche',
            weeks: 'Wochen',
            month: 'Monat',
            months: 'Monate'
        },
        dayOfWeekText: {
            0: 'Sonntag',
            1: 'Montag',
            2: 'Dienstag',
            3: 'Mittwoch',
            4: 'Donnerstag',
            5: 'Freitag',
            6: 'Samstag'
        },
        dayText: 'Tag',
        confirmRepeatBookingPrompt: {
            title: 'Erstellen von sich wiederholenden Buchungen',
            message: 'Ihr Vorgang wurde in die Warteschlange eingereiht. Sie können seinen Status im Vorgangsprotokoll nachverfolgen.',
            closeText: 'OK'
        },
        auditTrail: {
            recurringIntervalCreated: 'Wiederholungsserie erstellt zur Wiederholung alle',
            recurringIntervalEdited: 'Wiederholungsintervall bearbeitet zur Wiederholung alle',
            recurrentSeries: 'Sich wiederholende Serie'
        },
        seriesText: 'Serie'
    },
    jobDuplicateDialog: {
        title: 'Duplizieren ${jobSingularLowerAlias}',
        submitText: 'Erstellen ${bookingPluralLowerAlias}',
        cancelText: 'Abbrechen',
        newPrefix: 'Neu',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        searchToSelect: 'Suche zur Auswahl',
        changeRangeToIncludeBookingsString: 'Bereich ändern zur Einschließung aller ${bookingPluralLowerAlias}?',
        forwardOptions: {
            destinationStartDateLabel: 'Ziel-Startdatum',
            destinationStartDateLabelError: 'DDas Ziel-Startdatum ist unbedingt erforderlich',
            destinationStartDateLabelErrorDescription: 'Die relativen Positionen der ausgewählten ${bookingPluralLowerAlias} bleiben erhalten',
            outOfRangeEntityExplanation: 'Offenbar liegen einige ${bookingPluralLowerAlias} außerhalb der Start- und Endtermine dieses ${jobSingularLowerAlias}.',
            rolesPositionWarning: 'Vorhandene ${rolerequestPluralLowerAlias} verbleiben bei der ausgewählten ${jobSingularLowerAlias} und werden nicht dupliziert.',
            jobRangeLabelError: 'Der Datumsbereich des ${jobSingularLowerAlias} ist obligatorisch',
            maximumJobRangeMessage: 'Der ${jobSingularCapitalAlias} muss innerhalb von 24 Monaten liegen',
            dateRangeValueMandatory: 'Das ist ein Pflichtfeld',
            destinationJobLabel: 'Ziel ${jobSingularLowerAlias}',
            destinationJobError: 'Ziel ist unbedingt ${jobSingularLowerAlias} erforderlich',
            destinationBookingTypeLabel: 'Ziel-${bookingSingularLowerAlias} Typ',
            destinaltionJobExplanation: 'Die Zielbereiche für ${jobSingularLowerAlias} werden entsprechend geändert.',
            offsetExplanation: 'Ab dem Beginn des ersten ${bookingSingularLowerAlias} im Datumsbereich',
            dateRangeForJobLabel: 'Datumsbereich für den ausgewählten ${jobSingularLowerAlias}',
            selectedJobLabel: 'Ausgewählter ${jobSingularLowerAlias}',
            destinationBookingTypeError: 'Ziel-${bookingSingularLowerAlias} Typ ist unbedingt erforderlich',
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Typ ${bookingEntityAlias} so belassen',
            onPrefix: 'Am',
            inPrefix: 'In'
        }
    },
    roleGroupDuplicateDialog: {
        title: 'Duplizieren ${rolerequestgroupSingularLowerAlias}',
        submitText: 'Duplizieren ${rolerequestgroupSingularLowerAlias}',
        cancelText: 'Abbrechen',
        forwardOptions: {
            scenarioNameLabel: '${rolerequestgroupSingularCapitalAlias} Name',
            destinationJobLabel: 'Ziel ${jobSingularLowerAlias}',
            destinationJobError: 'Ziel-${jobSingularLowerAlias} ist obligatorisch',
            destinationStartDateLabel: 'Ziel-Startdatum',
            destinationStartDateLabelError: 'Ziel-Startdatum ist obligatorisch',
            destinationStartDateLabelErrorDescription: 'Die relativen Positionen der ${rolerequestPluralLowerAlias} werden nach der Duplizierung beibehalten.',
            destinaltionStartDateExplanation: 'ab dem Beginn der ersten ${rolerequestSingularLowerAlias} im ${rolerequestgroupSingularLowerAlias}',
            newRoleGroupDescriptionLabel: 'Beschreibung',
            onPrefix: 'Am',
            inPrefix: 'In',
            scenarioNameError: 'Das ist ein Pflichtfeld'
        }
    },
    peopleFinderDialog: {
        createBookingText: '${bookingEntityAlias} erstellen',
        createRoleText: '${roleEntityAlias} nach Bezeichnung erstellen',
        closeText: 'Schließen',
        filterTitle: 'Filter hinzufügen',
        infoToolTipText: 'Aktiver Datumsbereich im Plan',
        refreshButtonLabel: 'Aktualisieren',
        profileUrlCopied: 'Profil-URL kopiert',
        plannerPage: {
            title: 'Suche ${resourceEntityAlias}',
            emptyFinderText: 'Suchkriterien auswählen ${resourceEntityAlias}',
            resourceFoundInRangeText: '${resourcePluralCapitalAlias} bei <bold>${startDate} - ${endDate}</bold> gefundene werden hier angezeigt.',
            summaryText: '<bold>${resourceCount} Ergebnisse</bold> für Range'
        },
        profilePage: {
            title: 'Anderes Profil anzeigen',
            emptyFinderText: 'Kriterien für die Profilsuche auswählen',
            summaryText: '<bold>${resourceCount} Ergebnisse</bold>'
        }
    },
    jobFilterDialog: {
        filterTitle: 'Filter hinzufügen',
        placeholderLabel: 'Nach ${jobAlias}-Namen suchen...',
        modalTitle: 'Wählen Sie ein ${jobAlias}',
        resultSingular: '${rowCount} Ergebnis',
        resultPlural: '${rowCount} Ergebnisse',
        upToResults: 'Bis zu ${rowCount} Ergebnisse'
    },
    //mass duplicate settings
    massDuplicateJobs: {
        filterTitle: 'Filter hinzufügen',
        placeholderLabel: 'Nach ${jobAlias}-Name suchen...',
        modalTitle: 'Auswählen: ${jobAlias}',
        resultSingular: '${rowCount} Ergebnis',
        resultPlural: '${rowCount} Ergebnisse',
        upToResults: 'Bis zu ${rowCount} Ergebnisse',
        massDuplicateJobsTitle: 'Daten duplizieren',
        massDuplicateJobsFieldTitle: 'Jobs zur Massenduplizierung auswählen',
        massDuplicateJobsSubLabel: 'Testen',
        saveButtonLabel: 'Jobs duplizieren',
        cancelButtonLabel: 'Löschen',
        formHasErrorsMessage: 'Dieses Formular enthält Fehler',
        massDuplicateJobsInfoText: 'Filtern Sie die Jobs heraus, die dupliziert werden sollen.',
        massDuplicateJobsInfoTips1: '<b>Funktionen</b> in den ausgewählten Jobs werden nicht dupliziert',
        massDuplicateJobsInfoTips2: 'Relative Termine von Jobs und Buchungen werden beibehalten, z. B. eine Buchung, die 3 Tage nach \ndem Jobbeginn beginnt, wird 3 Tage nach dem Datum des Beginns des neuen Jobs erstellt.',
        massDuplicateJobsInfoTips3: 'Jobs mit einem gültigen Wert für <i>Nächster dazugehöriger Job</i>, <b>werden nicht dupliziert</b>. Nur seine Buchungen werden zum \nzugehörigen Job herüberkopiert.',
        massDuplicateJobsInfoTips4: 'Buchungen, die <b>inaktiven Ressourcen</b> zugewiesen sind, werden dupliziert und bleiben ihnen zugewiesen',
        massDuplicateJobsTextNewBooking: 'Neue Buchungen auf folgender Grundlage erstellen:',
        massDuplicateJobsNewBookingTextBookings: 'Buchungen',
        massDuplicateJobsNewBookingTextActuals: 'Istwerte',
        massDuplicateJobsNewBookingTipMessage: 'Erzeugt wöchentliche Buchungen aus Zeiterfassungsdaten',
        massDuplicateJobsTextDestinationBooking: 'Destinationsbuchungstyp',
        massDuplicateJobsDestinationOption1: 'Geplante Buchungen',
        massDuplicateJobsDestinationOption2: 'Unbestätigte Buchungen',
        massDuplicateJobsDestinationOption3: 'Buchungstyp beibehalten',
        massDuplicateJobsDaterangeText: 'Jobs und Buchungen im folgenden Zeitfenster auswählen',
        massDuplicateJobsNewJobsText: ' Neue Jobs erstellen',
        forwardOptions: {
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Typ ${bookingEntityAlias} so belassen',
            chooseHowJobsCreatedTitle: 'Auswählen, wie Jobs und Buchungen erstellt werden',
            chooseHowJobsCreatedTitleExtra: 'Bei Jobs mit dem Wert <i>Nächster dazugehöriger Job</i> verschieben sich die neuen Termine für den Buchungsbeginn ab \ndem Datum des Jobbeginns um denselben Terminversatz wie beim ursprünglichen Job',
            dateRangeForJobLabel: 'Jobs mit einem Beginn zwischen',
            fieldMandatoryText: 'Pflichtfeld',
            createNewJobsLabel: 'Neue Jobs erstellen',
            inPrefix: 'in',
            onPrefix: 'am',
            inPrefixExtra: 'Ab dem Beginn der ersten Buchung im \nausgewählten Zeitfenster',
            onPrefixExtra: 'Neue Jobs beginnen an diesem Datum. Buchungspositionen \nwerden relativ zum Datum des Beginns beibehalten.',
            createNewBookingsLabel: 'Neue Buchungen auf folgender Grundlage erstellen:',
            bookings: 'Buchungen',
            actuals: 'Istwerte',
            destinationBookingTypeLabel: 'Destinationsbuchungstyp',
            plannedBookings: 'Geplante Buchungen',
            unconfirmedBookings: 'Unbestätigte Buchungen',
            keepBookingType: 'Buchungstyp beibehalten',
            nextRelatedJobsLabel: 'Nur duplizierte Jobs mit gültigem Wert „Nächster zugehöriger Job“',
            newJobNamesLabel: 'Namen neuer Jobs',
            explanationNewJobNames: 'Falls für den ursprünglichen Job „Nächster zugehöriger Job“ ausgewählt ist, wird der Job nicht umbenannt',
            newJobsNamesDefaultOption: 'Standardwert',
            newJobsNamesDefaultOptionExample: 'Beispiel: Kopie von Aqua Audit 2023',
            newJobsNamesOriginalOption: 'Ursprünglichen Namen verwenden',
            newJobsNamesReplaceOption: 'Ersetzen',
            newJobsNamesReplaceWithOption: 'durch',
            newJobsNamesReplaceOptionExample: 'Beispiel: Aqua Audit 2023 wird zu Aqua Audit 2024',
            newJobsNamesCantFindTextToReplaceLabel: 'Wenn der zu ersetzende Text nicht zu finden ist, wird der Standardname verwendet (Kopie von...)'
        },
        massDuplicateJobsReviewTitle: 'Zusammenfassung: ${totalNumberOfJobs} zur Fortschreibung ausgewählte Jobs',
        massDuplicateJobsReviewPoint1: '${totalNumberOfBookings} Gesamtzahl ausgewählter Buchungen',
        massDuplicateJobsReviewPoint2: '${totalNumberOfConfirmedHours} bestätigte Stunden',
        massDuplicateJobsReviewPoint3: '${totalNumberOfUnconfirmedHours} unbestätigte Stunden',
        massDuplicateJobsReviewPoint4: '${totalNumberOfJobsWithoutNextJob} es werden neue Jobs erstellt',
        massDuplicateJobsReviewPoint5: '${totalNumberOfJobsWithNextJob} Bei Jobs mit dem Wert „Nächster zugehöriger Job“ werden nur deren Buchungen oder Istwerte fortgeschrieben',
        massDuplicateJobsReviewSummaryButtonLabel: 'Zusammenfassung aktualisieren'
    },
    operationsLogDialog: {
        heading: 'Vorgangsprotokoll',
        dataGridExplanations: 'Zeichnet die auf unserer Website durchgeführten Vorgänge auf'
    },
    expandedOperationsLogDialog: {
        heading: 'Job-Duplizierung',
        jobsRollForwarded: 'fortgeschriebene Jobs',
        bookingsCreated: 'Gesamtzahl angelegter Buchungen',
        newJobsCreated: 'erstellte neue Jobs',
        jobsDuplicated: 'Bei Jobs mit dem Wert „Nächster zugehöriger Job“ wurden nur deren Buchungen oder Istwerte fortgeschrieben'
    },
    actionBarWithFooterButtons: {
        saveButtonLabel: 'Änderungen speichern',
        cancelButtonLabel: 'Abbrechen',
        formHasErrorsMessage: 'Dieses Formular enthält Fehler'
    },
    cMeSection: {
        title: 'C-me traits'
    },
    educationSection: {
        formConfiguration: {
            education: 'Ausbildung',
            dialogConfig: {
                saveText: 'Speichern',
                cancelText: 'Abbrechen'
            },
            institutionLabel: 'Hochschule',
            institutionError: 'Hochschule ist Pflichtfeld',
            fieldLabel: 'Fachrichtung',
            fieldError: 'Fachrichtung ist Pflichtfeld',
            degreeLabel: 'Abschluss',
            noResultsFoundMessage: 'Keine von Ihrem Administator festgelegten Abschlussoptionen',
            startDateLabel: 'Startdatum',
            endDateLabel: 'Enddatum',
            endFieldError: 'Das Enddatum kann nicht vor dem Startdatum liegen.',
            endDateDescription: 'Als Enddatum kann der voraussichtliche Monat/das Jahr des Abschlusses angegeben werden.',
            detailsLabel: 'Details',
            addInstitutionPlaceholder: 'Hochschule hinzufügen',
            addFieldPlaceholder: 'Fachrichtung hinzufügen',
            addDegreePlaceholder: 'Abschluss hinzufügen',
            addDetailsPlaceholder: 'Details hinzufügen',
            maxCharErrorPrefix: 'Maximal',
            maxCharErrorSuffix: 'Zeichen zulässig'
        },
        addEducationButtonLabel: 'Ausbildung hinzufügen',
        editEducationButtonLabel: 'Ausbildung bearbeiten'
    },
    experienceSection: {
        formConfiguration: {
            experience: 'Berufserfahrung',
            companyLabel: 'Unternehmen',
            roleLabel: 'Position',
            locationLabel: 'Standort',
            startDateLabel: 'Startdatum',
            endDateLabel: 'Enddatum',
            detailsLabel: 'Details',
            endDateFieldError: 'Das Enddatum kann nicht vor dem Startdatum liegen.',
            roleError: 'Position ist Pflichtfeld',
            companynameError: 'Name des Unternehmens ist Pflichtfeld',
            maxCharErrorPrefix: 'Maximal',
            maxCharErrorSuffix: 'Zeichen zulässig',
            addDetailsPlaceholder: 'Details hinzufügen',
            addCompanyPlaceholder: 'Unternehmen hinzufügen',
            addRolePlaceholder: 'Position hinzufügen',
            addLocationPlaceholder: 'Standort hinzufügen',
            dialogConfig: {
                saveText: 'Speichern',
                cancelText: 'Abbrechen'
            }
        },
        addExperienceButtonLabel: 'Berufserfahrung hinzufügen',
        editExperienceButtonLabel: 'Berufserfahrung bearbeiten'
    },
    cookieConsentBanner: {
        accept: 'Akzeptieren',
        title: 'Diese Website verwendet Cookies',
        info: 'Wir nutzen Cookies, um Ihr Erlebnis zu verbessern, sowie zu Analysezwecken. Durch Klicken auf „Akzeptieren“ stimmen Sie der Verwendung dieser Cookies zu. Für weitere Informationen, wie wir Cookies nutzen und welche Datenschutzrechte Sie haben, lesen Sie bitte unsere <cookie>Cookie-Richtlinie</cookie> und <privacy>Datenschutzrichtlinie</privacy>.'
    },
    banners: {
        maintenanceStatusBanner: {
            dismissLabel: 'Ablehnen',
            singleDayInfo: '<bold>Geplante Wartungsarbeiten am ${startDate}, von ${startTime} bis Uhr${endTime}</bold>. Das Beibehalten ist vorübergehend nicht verfügbar, da wir unsere Website verbessern.',
            multiDayInfo: '<bold>Geplante Wartungsarbeiten vom ${startDate} um ${startTime} Uhr bis ${endDate} um  Uhr${endTime}</bold>. Das Beibehalten ist vorübergehend nicht verfügbar, da wir unsere Website verbessern.'
        },
        jobsPageBookmarkBanner: {
            dismissLabel: 'DE_Dismiss_DE',
            description: 'DE_We have changed the URL of this page. Update your bookmark if needed_DE'
        }
    },
    lastLoginSection: {
        minutesAgo: '${timeAgo} Vor m',
        hoursAgo: '${timeAgo} Vor h',
        daysAgo: '${timeAgo} Vor t',
        now: 'Jetzt',
        lastLoginLabel: 'Letzte Anmeldung'
    },
    longRunningTaskBanners: {
        duplicateJob: {
            processing: {
                title: 'Duplizierung von \'${jobDescription}\' zu ${progress}% abgeschlossen ...',
                content: {
                    message: 'Wir benachrichtigen Sie, sobald wir fertig sind.',
                    progressSeparator: 'von'
                }
            },
            completed: {
                title: 'Es ist ${jobSingularLowerAlias} bereit',
                content: {
                    message: '\'${jobDescription}\' erfolgreich dupliziert.'
                }
            },
            failed: {
                title: 'Fehler bei Duplizierung ${jobSingularLowerAlias}',
                content: {
                    message: 'Duplizieren von  \'${jobDescription}\' fehlgeschlagen.',
                    retry: 'Wiederholen'
                }
            },
            queued: {
                title: 'Ihre duplizierte ${jobSingularLowerAlias} wurde in die Warteschlange aufgenommen',
                content: {
                    message: 'Wir benachrichtigen Sie, sobald wir fertig sind.',
                    progressSeparator: 'von'
                }
            },
            multiple: {
                title: 'Ihre dupliziertes ${jobSingularLowerAlias} wurde in die Warteschlange gestellt',
                content: {
                    message: 'Mehrfachvorgänge sind in der Warteschlange.',
                    button: 'Vorgangsprotokoll anzeigen.'
                }
            }
        },
        longRunningjobIndicatorTooltipMessage: 'Für diese ${entityAlias} wird gerade ein lang andauernder Vorgang ausgeführt.'
    },
    cMeProfiling: {
        aboutcMeColours: 'About C-me colours',
        about: 'DE_About_DE',
        cMeColourProfilingDialog: {
            dialogTitle: 'About C-me colour profiling',
            topMessageLine1: 'Human behaviours can be complicated to describe. We\'ve partnered with C-me, a behaviour profiling service that associates behaviours with colours. A resource\'s C-me data tells you about their preferred ways of doing things, expressed in the language of four colours.',
            topMessageLine2: 'We automatically update a resource\'s skills with traits from their most dominant colour.',
            redBox: {
                title: 'Red',
                boxList: [
                    'Action oriented',
                    'Assertive',
                    'Competitive',
                    'Decisive',
                    'Determined',
                    'Fast paced',
                    'Strategic'
                ]

            },
            yellowBox: {
                title: 'Yellow',
                boxList: [
                    'Dynamic presenter',
                    'Energetic',
                    'Flexible',
                    'Imaginative',
                    'Inspirational',
                    'Optimistic',
                    'Spontaneous'
                ]
            },
            greenBox: {
                title: 'Green',
                boxList: [
                    'Collaborative',
                    'Democratic',
                    'Diplomatic',
                    'Empathetic',
                    'Non-judgemental',
                    'Patient',
                    'Values driven'
                ]
            },
            blueBox: {
                title: 'Blue',
                boxList: [
                    'Analytical',
                    'Disciplined',
                    'Methodical',
                    'Organised',
                    'Precise',
                    'Systematic',
                    'Thorough'
                ]
            }
        }
    },
    summaryPage: {
        personalGreeting: 'Hallo',
        customiseButtonLabel: 'Anpassen',
        doneButtonLabel: 'Fertig',
        arrangeWidgetsText: 'Klicken und ziehen Sie Widgets, um sie neu anzuordnen.',
        welcomeGreeting: 'Willkommen bei Retain',
        summaryDurationOptionLabels: {
            4: 'Nächste 4 Wochen',
            6: 'Nächste 6 Wochen',
            12: 'Nächste 12 Wochen'
        },
        widgets: {
            yourRequests: {
                title: 'Ihre Anfragen',
                emptyStateMessage: 'Sie haben in diesem Zeitraum keine Anfragen eingereicht'
            },
            plannedHours: {
                title: 'Geplante Stunden',
                emptyStateMessage: ''
            },
            ongoingJobs: {
                title: 'Fortlaufend ${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            actionRequired: {
                title: '${rolerequestPluralCapitalAlias} ausführen',
                emptyStateMessage: 'Keine auszuführenden ${rolerequestPluralLowerAlias} in diesem Zeitraum'
            },
            jobsOverBudgetDetails: {
                title: '${jobPluralCapitalAlias} über Budget',
                emptyStateMessage: 'Keine laufenden ${jobPluralLowerAlias} über Budget'
            },
            chargeableUtilisation: {
                title: 'Gebührenpflichtige Nutzung',
                emptyStateMessage: ''
            },
            utilisation: {
                title: 'Nutzung',
                emptyStateMessage: ''
            },
            pendingRequests: {
                title: '${rolerequestPluralCapitalAlias} ausführen',
                subTitleUnit: 'Stunden',
                emptyStateMessage: ''
            },
            unassignedBookingsDetails: {
                title: 'Nicht zugewiesen ${bookingPluralLowerAlias}',
                emptyStateMessage: 'Keine nicht zugewiesenen ${bookingPluralLowerAlias} in diesem Zeitraum'
            },
            unassignedBookings: {
                title: 'Nicht zugewiesen ${bookingPluralLowerAlias}',
                subTitleUnit: '${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            upcomingBookingsDetails: {
                title: 'Ihre bevorstehenden ${bookingPluralLowerAlias}',
                emptyStateMessage: 'Ihnen sind keine ${bookingPluralLowerAlias} in diesem Zeitraum zugewiesen.'
            }
        },
        widgetDetailsTotalsUnit: {
            bookings: '${bookingPluralLowerAlias}',
            jobs: '${jobPluralLowerAlias}',
            hours: 'Stunden',
            planned: 'geplant',
            unconfirmed: 'unbestätigt',
            availability: 'Verfügbarkeit',
            requests: 'Anfragen',
            rejected: 'Abgelehnt',
            requested: 'Angefragt',
            draft: 'Entwurf',
            live: 'Live'
        },
        budgetConsumedText: 'Verbrauchtes Budget',
        pageTitle: 'Zusammenfassung',
        configurationPane: {
            arrangeButtonLabel: 'Anordnen',
            searchPlaceholder: 'Suchen',
            openOnLoginLabel: 'Diese Seite bei der Anmeldung öffnen',
            emptySearchMessage: 'Es wurden keine Ergebnisse gefunden.',
            sectionTitles: {
                personal: 'Persönlich',
                bookings: '${bookingPluralCapitalAlias}',
                jobs: '${jobPluralCapitalAlias}',
                resources: '${resourcePluralCapitalAlias}',
                roles: '${rolerequestPluralCapitalAlias}'
            }
        },
        explainSummaryPageTextSecurity: 'Widgets wählen, die Benutzer ihrer Seite „Zusammenfassung“ hinzufügen können Persönliche Widgets sind für alle Benutzer verfügbar. \n\nWidgets zeigen keine für dieses Sicherheitsprofil ausgeblendeten Datensätze und Felder an.'
    },
    listPage: {
        pageTitle: 'DE_Lists_DE',
        workspacesMessages: {
            defaultWorkspaceLabel: 'DE_Default workspace_DE',
            newWorkspaceLabel: 'DE_New workspace_DE',
            saveChangesToPublicLabel: 'DE_Save changes to public_DE',
            saveAsNewWorkspaceLabel: 'DE_Save as a new workspace_DE',
            manageMyWorkspacesLabel: 'DE_Manage my workspaces_DE',
            privateWorkspacesLabel: 'DE_Private workspaces_DE',
            publicWorkspacesLabel: 'DE_Public workspaces_DE',
            noPublicWorkspacesCreatedLabel: 'DE_No public workspaces have been created_DE',
            noPrivateWorkspacesCreatedLabel: 'DE_No private workspaces have been created_DE'
        }
    }
};
