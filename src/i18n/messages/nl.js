﻿// Any string wrapped in ${ } or { } is a placeholder string and should not be translated literally.
export default {
    //common validation errors
    emailValidationError: 'Voer een geldig e-mailadres in.',
    //commonly used text
    fieldValue: 'Veldwaarde',
    confirmation: 'Bevestiging',
    invalid: 'Ongeldig',
    current: '{index} (huidig)',
    dismissText: 'Annuleren',
    mandatoryValidation: '* Verplicht',
    //common component
    selectionListLicenseWarning: '{contactUs} om uw limiet te verhogen.',
    selectionListLicenseError: '{contactUs} om uw limiet te verhogen.',
    selectionListDiaryCustomMsg: '{activeCount} van {licensedCount} agenda’s gebruikt',
    selectionListSkillsCustomMsg: '{activeCount} van {licensedCount} vaardigheden gebruikt',
    commonComponentConfirmationModalConsequence: 'Ik begrijp de consequenties van deze wijziging.',
    commonComponentConfirmationModalEmptyMsg: ' ',
    noOptionsAvailable: 'Geen opties beschikbaar',
    actionBarSaveButtonLabel: 'Wijzigingen opslaan',
    actionBarConfirmChangesLabel: 'Wijzigingen bevestigen',
    actionBarCancelButtonLabel: 'Annuleren',
    actionBarDiscardChangesLabel: 'Wijzigingen annuleren',
    actionBarLeavePageMessage: 'Weet u zeker dat u de pagina wilt verlaten?',
    actionBarUnsavedChanges: 'U heeft niet-opgeslagen wijzigingen op deze pagina. Wilt u de pagina nog steeds verlaten?',
    actionBarFormErrorMessage: 'Dit formulier bevat fouten.',
    deleteModalCancelButtonLabel: 'Nee, beveiligingsprofiel behouden.',
    deleteModalSubmitButtonLabel: 'Ja, beveiligingsprofiel verwijderen.',
    securityProfileDeleteModalTitle: 'Beveiligingsprofiel {profileName} verwijderen?',
    securityProfileDeleteModalText: 'Wilt u dit beveiligingsprofiel permanent verwijderen?',
    cannotBeUndoneText: 'Dit kan niet ongedaan worden gemaakt.',
    addAnItemText: 'Een item toevoegen',
    selectionListSortText: 'Indelen op alfabetische volgorde (A-Z)',
    deleteSectionConfirmation: 'Weet u zeker dat u {sectionName} wilt verwijderen?',
    deleteSectionConfirmationTitle: 'Bevestiging sectie verwijderen',
    CreateContextMenuLabel: 'Aanmaken',
    RenameContextMenuLabel: 'Naam wijzigen',
    DuplicateContextMenuLabel: 'Dupliceren',
    DeleteContextMenuLabel: 'Verwijderen',
    DUPLICATE_USING_APIContextMenuLabel: 'DUPLICATE_USING_API',
    toasterDefaultSuccessMessage: 'Wijzigingen opgeslagen',
    toasterDefaultErrorMessage: 'Opslaan mislukt, probeer het opnieuw',
    toasterDefaultWarningMessage: 'Sommige wijzigingen zijn niet opgeslagen, vernieuw de pagina en probeer het opnieuw',
    toasterDefaultUnsavedChangesMessage: 'Kan wijzigingen niet opslaan, los fouten op en probeer het opnieuw.',
    actionCannotBeUndoneMessage: 'Deze actie kan niet ongedaan worden gemaakt.',
    noDataAvailableText: 'Geen gegevens',
    licensedCountLabel: '{activeCount} van {licensedCount} gebruikt ',
    defaultLimitationInfo: 'De beperking is gebaseerd op uw huidige licentieplan.',
    contactUsText: 'Neem contact met ons op',
    importText: 'Importeren',
    noResultsFound: 'Geen resultaten gevonden',
    reorderText: 'Opnieuw indelen',
    editText: 'Bewerken',
    resetText: 'Resetten',
    searchText: 'Zoeken',
    nameText: 'naam',
    //skill filter cascader component
    skillFilterResetButtonDisabledMessage: 'Kan niet resetten: Geen vaardigheden geselecteerd.',
    skillFilterApplyButtonDisabledMessage: 'Kan niet toepassen: Geen vaardigheden geselecteerd.',
    skillFilterApplyButtonDisabledForMaxCountMessage: 'Kan niet toepassen: Maximaal {maxSkillSelection} selecties toegestaan.',
    //Import data
    entityImportSelectValuePlaceholder: 'Waarde selecteren',
    ImportSuccessfulToasterMessage: 'Importeren gelukt',
    ImportUnsuccessfulToasterMessage: 'Importeren mislukt',
    operationLogChangesSaved: 'Wijzigingen activiteitenlog opgeslagen',
    //userManagement Messages
    userManagementLicenseWarning: 'U heeft de door uw licentie toegestane limiet bijna bereikt. Stel bestaande gebruikers in op \'Inactief\' of {contactUs} om de limiet van uw plan te verhogen.',
    userManagementLicenseError: 'U heeft de door uw licentie toegestane limiet bereikt. Stel bestaande gebruikers in op \'Inactief\' of {contactUs} om de limiet van uw plan te verhogen.',
    userManagementLicenseErrorHeading: 'U mag niet meer dan {number} actieve gebruikers hebben.',
    userManagementLicenseActiveUsers: 'U heeft {number} gebruikers ingesteld op ‘Actief’.',
    userManagementLicenseUserLimit: 'Uw huidige licentie ondersteunt maximaal {number} actieve gebruikers.',
    userManagementLicenseContactUS: 'Stel een aantal gebruikers in op \'Inactief\' {contactUs} om uw limiet te verhogen.',
    userManagementInactiveUserHeading: '{number} gebruikers instellen op \'Inactief\'?',
    userManagementSetInactiveUserPopup: 'Wilt u {number} gebruikers instellen op \'Inactief\'?',
    userManagementUnAssignBooks: 'Ze kunnen niet meer inloggen en worden uitgeschreven voor huidige boekingen. Het is niet mogelijk om toekomstige boekingen aan ze toe te wijzen.',
    userManagementReportingArchive: 'Als ze aan eerdere boekingen zijn toegewezen, worden deze gegevens bewaard voor rapportage/het archief.',
    userManagementInactiveConfirmation: '{number} gebruikers instellen op \'Inactief\' en ze verwijderen uit huidige boekingen',
    userManagementInactivePopupPrimaryButton: 'Gebruikers inactief maken',
    userManagementInactivePopupSecondaryButton: 'Annuleren',
    //for confirmation modal popup need to change later
    userManagementDeletePopupMessage: 'Geselecteerde gebruikers verwijderen?',
    userManagementDeleteWarningMessage: 'Wilt u {number} gebruikers permanent verwijderen?',
    userManagementDeleteBookingsWarning: 'Al hun details worden uit het systeem verwijderd. Eerdere aan hen toegewezen boekingen worden geannuleerd en dit is van invloed op alle rapporten voor een dergelijke boeking.',
    userManagementDeleteHistoricData: 'Als u historische gegevens wilt opslaan, stelt u de gebruikersstatus in op \'Inactief\'.',
    userManagementDeleteConfirmation: 'De geselecteerde gebruikers verwijderen en ze verwijderen uit alle boekingen of rapporten in het systeem',
    userManagementDeletePrimaryButton: 'Gebruikers verwijderen',
    userManagementDeleteSecondaryButton: 'Geselecteerde gebruikers behouden',
    userManagementDescription: 'Voeg hier gebruikers toe of bewerk of verwijder ze. Ga naar hun profiel om verdere wijzigingen door te voeren.',
    userManagementSubTitle: 'Actieve gebruikers  ',
    userManagementNotifyUserCountDescription: '{activeUser} van uw {totalUser} zijn actief. Uw licentie ondersteunt maximaal {licencedActiveUser} actieve gebruikers.',
    userManagementAddUserButtonFroCommandBar: 'Een gebruiker toevoegen',
    userManagementToNavigateImportFeatureMessage: 'Voor bulkupdates gebruikt u wellicht liever de functie {Import}.',
    userManagementFormLabelsSecurityProfile: 'Beveiligingsprofiel',
    userManagementFormLabelsUserStatusDescription: 'Een gebruiker op actief instellen zorgt dat hij in kan loggen op zijn account.',
    userManagementFormLabelsName: 'Naam',
    userManagementFormLabelsEmail: 'E-mailadres',
    userManagementFormLabelsUserStatus: 'Actieve gebruiker',
    userManagementFormLabelsUserLastLogin: 'Laatste keer ingelogd',
    userManagementValidationFirstName: 'Voer voornaam in',
    userManagementValidationLastName: 'Voer achternaam in',
    userManagementValidationEmailReqd: 'Voer uw e-mailadres in',
    userManagementValidationEmailMsg: 'Dit is geen geldig e-mailadres',
    userManagementValidationSecurityProfile: 'Een beveiligingsprofiel selecteren is verplicht',
    userManagementTooltipGoToProfile: 'Ga naar profiel',
    userManagementTooltipResetPass: 'Wachtwoord resetten',
    userManagementTooltipResendEmail: 'Uitnodigingsmail verzenden',
    userManagementTooltipMarkDelete: 'Markeren voor verwijdering',
    userManagementFirstNamePlaceholder: 'Voornaam',
    userManagementLastNamePlaceholder: 'Achternaam',
    userManagementEmailPlaceholder: 'E-mailadres invoeren',
    userManagementSecurityProfilePlaceholder: 'Selecteer een beveiligingsprofiel',
    userManagementTitle: 'Gebruikersbeheer',
    userManagementStatusActive: 'Actief',
    userManagementStatusInactive: 'Inactief',
    userManagementDuplicateEmail: 'Zorg dat de gebruikers unieke e-mail-ID’s hebben.',
    userManagementUserAccess: 'Onvoldoende machtigingen om deze resources te bewerken',
    //comopanyInfo Messages
    companyInfoNameLabel: 'Bedrijfsnaam',
    companyInfoLogoLabel: 'Logo',
    companyUploadLogoLabel: 'Logo uploaden',
    companyInfoLogoThumbnailLabel: 'Miniatuur van logo',
    companyUploadLogoThumbnailLabel: 'Miniatuur van logo uploaden',
    companyInfoSupportPhoneNumberLabel: 'Telefoonnummer voor back-up',
    companyInfoApplicationLanguage: 'Taal van de applicatie',
    companyInfoSupportEmailLabel: 'E-mailadres voor back-up',
    companyInfoInstanceOwnerLabel: 'Eigenaar instantie',
    companyInfoLogoControlMessage: 'Het logo moet 80px X 24px en in .png formaat zijn',
    companyInfoUploadControlText: 'Klik op of sleep bestand naar dit gedeelte om te uploaden',
    companyInfoLogoThumbnailControlMessage: 'De miniatuur van het logo moet 24px X 24px en in .png formaat zijn',
    companyInfoPhoneNumberErrorMessage: 'Voer een geldig telefoonnummer in',
    companyInfoEmailErrorMessage: 'Voer een geldig e-mailadres in',
    companyInfoLogoErrorMessage: 'Upload een geldig logo',
    companyInfoLogoThumbnailErrorMessage: 'Upload een geldige miniatuur van het logo',
    companyInfoLookUpNoMatches: 'Geen overeenkomsten',
    companyInfoSelectValuePlaceholder: 'Waarde selecteren',
    companyInfoHelpSupportMessage: 'Neem voor ondersteuning contact op met',
    companyInfoVersion: 'Versie {version}',
    companyInfoTitle: 'Bedrijfsinformatie',
    companyInfoFileUploadFailed: '{fileName} niet geüpload.',
    //Currency Messages
    currencyHeaderText: 'Valuta',
    currencyDefaultMessage: 'Systeemvaluta',
    currencyDescription: 'Selecteer de valuta die in uw applicatie moet worden gebruikt.',
    baseCurrencyLabel: 'Standaardvaluta',
    //diary calendar messages
    diaryCalendarHeading: 'Agenda',
    diaryCalendarSubHeading: 'Pas uw jaar aan door een standaard werkpatroon en vrije dagen zoals nationale feestdagen en dagen dat het kantoor is gesloten te definiëren.',
    diaryCalendarCustomDayTitle: 'Een aangepaste dag toevoegen',
    diaryCalendarCustomPeriodTitle: 'Een aangepaste periode toevoegen',
    diaryCalendarSelectDateRangeRequiredMsg: 'Selecteer een datumbereik',
    diaryCalendarCustomPeriodRequiredMsg: 'Naam voor aangepaste periode is vereist',
    diaryCalendarWorkPatternRequiredMsg: 'Selecteer een werkpatroon',
    diaryCalendarSelectDateRequiredMsg: 'Selecteer een datum',
    diaryCalendarCustomDayRequiredMsg: 'Naam voor aangepaste dag is vereist',
    diaryCalendarDayTypeRequiredMsg: 'Selecteer een type dag',
    diaryCalendarReplaceCustomPeriodMsg: 'Wilt u "{overlappingPeriods}" vervangen door een nieuwe aangepaste periode?',
    diaryCalendarReplaceCustomDayMsg: 'Wilt u "{overlappingDayName}" vervangen door een nieuwe aangepaste dag?',
    //for confirmation modal popup need to change later
    diaryCalendarSaveHistoricalDataAlert: 'Weet u zeker dat u wijzigingen aan historische gegevens op wilt slaan?',
    diaryCalendarSavePastDayChangesMsg: 'Uw wijzigingen kunnen van invloed zijn op berekeningen voor eerdere boekingen. Dit kan niet ongedaan worden gemaakt.',
    diaryCalendarStandardWorkPatternLabel: 'Standaard werkpatroon',
    diaryCalendarCustomDaysGridHeading: 'Aangepaste dagen',
    diaryCalendarCustomPeriodsGridHeading: 'Aangepaste periodes',
    diaryCalendarCustomDaysAddBtn: 'Aangepaste dag toevoegen',
    diaryCalendarCustomPeriodsAddBtn: 'Aangepaste periode toevoegen',
    diaryCalendarCustomDayNamePlaceholder: 'Naam van aangepaste dag',
    diaryCalendarCustomPeriodNamePlaceholder: 'Naam van aangepaste periode',
    diaryCalendarCustomDayTypePlaceholder: 'Type dag voor aangepaste dag',
    diaryCalendarCustomWorkPatternPlaceholder: 'Werkpatroon voor aangepaste periode',
    diaryCalendarCustomDayIsInUseMessage: 'Dit is tegelijk met een bestaande aangepaste dag',
    diaryCalendarCustomPeriodIsInUseMessage: 'Dit is tegelijk met een bestaande aangepaste periode',
    diaryCalendarCustomGridDateRequiredMessage: 'Voer een datum in',
    diaryCalendarCustomGridRangeRequiredMessage: 'Voer een datumbereik in',
    diaryCalendarCustomGridNameRequiredMessage: 'Voer een naam in',
    diaryCalendarCustomGridDayTypesRequiredMessage: 'Voer een type dag in',
    diaryCalendarCustomGridWorkPatternsRequiredMessage: 'Voer een werkpatroon in',
    diaryCalendarTotalHourSumErrorMessage: 'Het totale aantal uren moet 24 uur of minder zijn',
    diaryCalendarHoursAginstDurationError: 'Begin- en en eindtijd moeten het toegewezen aantal uren bevatten',
    diaryCalendarCustomDayPlaceholder: 'Voer naam aangepaste dag in',
    diaryCalendarDayTypePlaceholder: 'Een type dag selecteren',
    diaryCalendarCustomPeriodPlaceholder: 'Voer naam aangepaste periode in',
    diaryCalendarWorkPatternPlaceholder: 'Werkpatroon selecteren',
    diaryCalendarRangeDateLabel: 'Begin- en einddatum',
    diaryCalendarPatternLabel: 'Patroon',
    diaryNameRequiredMessage: 'Voer een agendanaam in',
    uniqueDiaryNameMessage: 'Voer een unieke agendanaam in',
    diaryWarningTitle: 'Kan agenda niet verwijderen',
    diaryWarningMessage: 'Kan agenda "{diaryNames}" niet verwijderen. Deze agenda is toegewezen aan resource.',
    //button labels
    confirmButtonLabel: 'Bevestigen',
    cancelButtonLabel: 'Annuleren',
    saveButtonLabel: 'Opslaan',
    okButtonLabel: 'OK',
    //table headings
    diaryCalendarCustomDaysDateTableHeading: 'Datum',
    //same value need to check
    diaryCalendarCustomDaysNameTableHeading: 'Naam',
    diaryCalendarCustomPeriodsRangeTableHeading: 'Begin- en einddatum',
    diaryCalendarCustomPeriodsWorkPatternTableHeading: 'Werkpatroon',
    //work pattern
    workPatternHeading: 'Werkpatronen',
    workPatternCommandBarFieldName: 'Naam werkpatroon bewerken',
    workPatternSubHeading: 'Er kan een werkpatroon worden aangemaakt door een begindatum en individuele typen dagen te selecteren. Voeg hier werkpatronen toe of bewerk of verwijder ze.',
    //for confirmation modal popup need to change later
    workPatternSaveInUseAlert: 'Weet u zeker dat u wijzigingen aan gebruikt werkpatroon op wilt slaan?',
    workPatternSaveInUseChangesMessage: 'Uw wijzigingen kunnen van invloed zijn op de agenda. Dit kan niet ongedaan worden gemaakt.',
    workPatternAddButton: 'Een dag toevoegen',
    workPatternReqValidation: 'Selecteer een type dag',
    workPatternUniqueMsg: 'Voer een unieke naam voor het werkpatroon in',
    workPatternReqdMsg: 'Voer een naam voor het werkpatroon in',
    workPatternStartDayLabel: 'Dag van aanvang',
    workPatternDayTableHead: 'Dag',
    workPatternWarningMessage: 'Verwijder uit agenda’s om te verwijderen.',
    workPatternWarningTitle: 'U kunt {selection} werkpatroon niet verwijderen, omdat het wordt gebruikt.',
    //Day Types
    dayTypePageHeading: 'Typen dag',
    dayTypeHeading: 'Type dag',
    dayTypeCommandBarFieldName: 'Naam type dag bewerken',
    dayTypeSubHeading: 'Stel het type in (werkdag of vrije dag) om de standaard kantooruren van uw organisatie weer te geven.',
    //for confirmation modal popup need to change later
    dayTypeSaveInUseAlert: 'Weet u zeker dat u wijzigingen aan gebruikt type dag op wilt slaan?',
    dayTypeSaveInUseChangesMessage: 'Uw wijzigingen kunnen van invloed zijn op de agenda. Dit kan niet ongedaan worden gemaakt.',
    dayTypeWorkingHoursRequiredMessage: 'Voer werktijd in',
    dayTypeContingencyTimeRequiredMessage: 'Voer extra werktijd in',
    dayTypeWorkingHoursCannotZeroMessage: 'Werktijd mag niet 00:00 zijn',
    dayTypeWorkDayLabel: 'Werkdag',
    dayTypeNonWorkDayLabel: 'Vrije dag',
    dayTypeWorkTimeLabel: 'Werktijd',
    dayTypeContingencyTimeHours: 'Extra werktijd',
    dayTypeTitleRequiredMessage: 'Voer naam type dag in',
    dayTypeTitleUniqueMessage: 'Voer een unieke type dag in',
    dayTypeWarningTitle: 'U kunt {selection} type dag niet verwijderen, omdat het wordt gebruikt.',
    dayTypesWarningMessage: 'Verwijder uit werkpatroon om het te verwijderen.',
    //entity import
    entityImportPageHeader: 'Gegevens importeren',
    entityImportPageSummaryText: 'Importeer gegevens in uw applicatie in twee stappen:<ol><li>Download het relevante sjabloon, vul er gegevens op in en zorg dat de verplichte velden zijn ingevuld</li><li>Upload het door u ingevulde sjabloon</li></ol>De door u op het sjabloon ingevulde gegevens worden in de applicatie geïmporteerd.',
    entityImportDownloadTemplatesHeader: 'Een sjabloon downloaden',
    entityImportDownloadTemplatesNumber: '1',
    entityImportUploadDataHeader: 'Upload de sjabloon die u heeft ingevuld',
    entityImportUploadDataNumber: '2',
    entityImportJobLabel: 'Taken',
    entityImportClientLabel: 'Klanten',
    entityImportResourceLabel: 'Resources',
    entityImportSkillLabel: 'Vaardigheden',
    entityImportUploadControlText: 'Klik op of sleep een bestand naar dit gedeelte om te uploaden',
    entityImportSelectUploadFileLabel: 'Selecteer een te uploaden bestand',
    entityImportImportSuccessful: 'Importeren gelukt',
    entityImportImportUnsuccessful: 'Importeren mislukt',
    entityImportTemplateDownloadFailed: 'Kan sjabloon niet downloaden',
    entityImportUploadControlError: 'Selecteer te uploaden bestand',
    entityImportUploadDropDownError: 'Selecteer een type',
    entityImportUploadDropDownPlaceholder: 'Kies het type gegevens',
    entityImportFileUploadFailed: 'Bestand niet geüpload.',
    entityImportTypeOfData: 'Type gegevens',
    entityImportUploadAndVerify: 'Uploaden en bevestigen',
    entityImportCancelBtn: 'Annuleren',
    entityImportConfirmImport: 'Importeren bevestigen',
    entityImportFormValidateMsg: 'Klik om voorafgaand aan importeren al te valideren',
    entityImportmailSubject: 'Limiet Retain Cloud-licenties',
    entityImportUploadProcessed: '{EntriesProcessedCnt} {currentEntityType} invoer verwerkt.',
    entityImportTemplateFileName: 'EntityImportTemplate',
    processFormErrorCorrectionText: 'Om het importeren te voltooien, lost u de volgende fouten op.',
    processFormAlternateOption: 'U kunt er ook voor kiezen ze te verwijderen of bij te werken, werk het Microsoft Excel-bestand bij en upload het weer.',
    processFormRowNoFromExcelMsg: 'Het bijbehorende rijnummer van het Microsoft Excel-bestand wordt hieronder weergegeven.',
    processFormRequiredClientNameField: 'Het veld Klantnaam is vereist.',
    processFormRequiredClientCodeField: 'Het veld Klantcode is vereist.',
    processFormRequiredJobTitleField: 'Het veld Functie is vereist.',
    processFormRequiredSkillNameField: 'Het veld Naam vaardigheid is vereist.',
    processFormRequiredSkillInfoField: 'Het veld Informatie vaardigheid is vereist.',
    processFormRequiredSkillSectionField: 'Het veld Sectie vaardigheid is vereist.',
    processFormRequiredFirstNameField: 'Het veld Voornaam is vereist.',
    processFormRequiredLastNameField: 'Het veld Achternaam is vereist.',
    processFormRequiredEmailField: 'Het veld E-mailadres is vereist.',
    processFormProcessed: ' verwerkt.',
    processFormProcessedWithError: ' verwerkt met fouten.',
    processFormProcessedWithNoError: '  verwerkt zonder fouten.',
    processFormWithError: ' met fouten.',
    processFormWithNoError: ' zonder fouten.',
    processFormLicenseUserContError: 'Deze {currentEntityType} invoer kan niet worden geïmporteerd.',
    processFormLicenseUserDivLine1: 'Uw licentie ondersteunt maximaal <b>{allowedActiveUsersCount}</b> actieve gebruikers.',
    processFormLicenseUserDivLine2_1: 'Deze import resulteert in {totalRecordsProcessed} actieve gebruikers.',
    processFormContactUs: '{contactUs}',
    processFormLicenseUserDivLine2_2: 'om uw limiet te verhogen.',
    processFormLicenseUserContErrorAlert: 'Er is iets fout gegaan. Import kan niet worden verwerkt',
    processFormContactUsText: 'Neem contact met ons op',
    //color scheme
    colourSchemeHeader: 'Kleurthema',
    colourSchemeFieldName: 'Naam kleurthema bewerken',
    colourSchemeHeaderAddButtonText: 'Een kleurregel toevoegen',
    colourSchemeSummaryText: 'Geef alle type boekingen weer in een verschillende kleur door uw gewenste kleur te selecteren voor ieder relevante veld.',
    colourSchemeRolesSummaryText: 'Geef rollen weer in een verschillende kleur door uw gewenste kleur te selecteren voor ieder relevante veld.',
    colourSchemeConfirmModalText: 'Als u het veld wijzigt, worden alle bestaande regels voor kleurthema’s verwijderd. Doorgaan?',
    colourSchemeTableRequired: 'Selecteer tabel',
    colourSchemeFieldRequired: 'Selecteer veld',
    colourSchemeGridEmptyText: 'Er zijn geen kleurregels aangemaakt',
    colourSchemeGridAddButtonText: 'Een regel toevoegen',
    colourSchemePreviewText: 'Voorbeeldtekst',
    colourSchemeFieldValueRequired: 'Selecteer veldwaarde uit de lijst',
    colourSchemeFieldValueUnique: 'De veldwaarde moet uniek zijn',
    colourSchemeLookUpNoMatches: 'Geen overeenkomsten',
    colourSchemeSelectValuePlaceholder: 'Waarde selecteren',
    colourSchemeResourceLookupPlaceholder: 'Toewijzing geannuleerd',
    colourSchemeColourSchemeAddButton: 'Een kleurregel toevoegen',
    colourSchemeTable: 'Tabel',
    colourSchemeField: 'Veld',
    colourSchemePreviewTextTitle: 'Voorbeeld',
    colourSchemeColourCodeTextTitle: 'Kleur',
    colourSchemeCreateColorTheme: 'Kleurthema aanmaken',
    colourSchemeFieldDropdownPlaceholder: 'Veld kiezen',
    colourSchemeTableDropdownPlaceholder: 'Geen',
    colorSchemeUniqueTitle: 'Voer een unieke naam voor het kleurthema in',
    colorSchemeRequiredTitle: 'Voer een naam voor het kleurthema in',
    colourThemeTabTitle_Bookings: 'Boekingen',
    colourThemeTabTitle_Roles: 'Rollen',
    colourSchemeDescriptionPlaceholder: 'Kleurbeschrijving',
    //conflicts
    conflictPageHeader: 'Conflicten',
    conflictsMsgsSubHeaderLabel: 'Wanneer conflicten weergeven.',
    conflictsMsgsSubLabel_line1: 'Kies of conflicten moeten worden weergegeven. Selecteer de drempel waarop en waarboven één of meer aan een resource toegewezen boeking(en) als een conflict worden weergegeven.',
    conflictsMsgsResourceLoadingControlLabel: 'Wanneer Resource laden ten minste',
    conflictsMsgsShowConflictsLabel: 'Conflicten weergeven',
    conflictsMsgsConfermationModelHeader: 'Voer een geldige drempel voor laden in.',
    conflictsMsgsConfermationModelSubHeader: 'De drempel voor laden moet tussen {minValue} en {maxValue}% liggen.',
    conflictsMsgsShowResourceLoadingNote: 'Geaccepteerde drempels voor laden zijn {minValue} tot {maxValue}%.',
    conflictsMsgsOkBtn: 'OK',
    conflictsMsgsYesText: 'Ja',
    conflictsMsgsNoText: 'Nee',

    //Service accounts
    serviceAccounts: {
        maximumFieldLengthValidationMessage: 'Maximaal ${maximumFieldSize} tekens',
        pageHeader: 'Service-accountbeheer',
        addEntity: 'Een service-account toevoegen',
        saveButtonLabel: 'Wijzigingen opslaan',
        cancelButtonLabel: 'Annuleren',
        markedForDeletionMessage: 'Gemarkeerd voor verwijdering. Wordt verwijderd wanneer u de wijzigingen bevestigt. ',
        cancelDeletion: 'Verwijdering annuleren',
        serviceAccountManagementTitle: 'Service-accountbeheer',
        serviceAccountsSubTitle: 'Actieve service-accounts',
        serviceAccountDescription: 'Wilt u iets bouwen dat integreert met Retain en het uitbreidt? Voeg hier service-accounts toe of bewerk of verwijder ze.',
        serviceAccountNavigateToRetainApiDocumentationMessage: 'Voor meer informatie over onze <linkText>Retain Cloud API’s</linkText> kunt u ook onze helpdocumentatie raadplegen.',
        serviceAccountManagementUsedAccountsWarning: 'U nadert de limiet van 5 toe te voegen service-accounts. Beheer bestaande accounts of verwijder niet gebruikte accounts.',
        serviceAccountManagementUsedAccountsError: 'U heeft de limiet bereikt van 5 toe te voegen service-accounts. Beheer bestaande accounts of verwijder niet gebruikte accounts.',
        emptyStateMessage: 'Geen resultaten',
        setPassword: 'Wachtwoord instellen',
        password: 'Wachtwoord',
        name: 'Naam',
        tenant: 'Tenant',
        securityProfile: 'Beveiligingsprofiel',
        email: 'E-mailadres',
        savePasswordTooltip: 'Sla wijzigingen op voordat u een wachtwoord opslaat',
        nameValidationMessage: 'Voer naam in',
        emailValidationMessage: 'Voer uw e-mailadres in',
        typeHerePlaceholder: 'Hier invoeren',
        nameColumnTitle: 'Naam',
        emailColumnTitle: 'E-mailadres',
        securityProfileColumnTitle: 'Beveiligingsprofiel',
        actionsColumnTitle: 'Acties',
        emailExplanation: 'Voer een uniek e-mailadres in dat niet voor een bestaand account wordt gebruikt',
        formHasErrorsMessage: 'Dit formulier bevat fouten'
    },

    //workflows
    workflowsSettings: {
        roleByNameWorkflowPageHeader: 'Rollen per naam',
        roleByRequirementsWorkflowPageHeader: 'Rollen per vereisten',
        rolesByNamePageDescriptionLabel: 'Rollen kunnen worden gebruikt om een resource voor een taak in te dienen. Rollen per naam worden gebruikt als u de resource die u wilt kent.',
        rolesByRequirementsPageDescriptionLabel: 'Rollen kunnen worden gebruikt om een resource voor een taak in te dienen. Met rollen per vereisten kunt u vereisten indienen om een geschikte resource te vinden.',
        draftStateDescription: 'Rollen kunnen worden opgeslagen als concept als ze nog niet als verzoek kunnen worden ingediend.',
        requestedStateDescription: 'Rollen die live kunnen gaan als boekingen, of kunnen worden afgewezen.',
        liveStateDescription: 'Rollen die zijn geblokkeerd.',
        rejectedStateDescription: 'Rollen die zijn afgewezen.',
        archivedStateDescription: 'Rollen die niet langer actie vereisen.',
        statesLegendTitle: 'Statussen van een workflow',
        actorsSectionTitle: 'Deelnemers aan workflow',
        actorsSectionDescription: 'Definieer wat iedere deelnemer kan doen in een workflow.',
        requesterActorTitle: 'Aanvrager',
        roleByNameRequesterActorDescription: 'Maakt rollen aan om aanvragen voor een taak in te dienen.',
        roleByRequirementsRequesterActorDescription: 'Maakt rollen aan om aanvragen in te dienen met een aantal vereisten.',
        whoCanCreateRolesLabel: 'Iedereen kan rollen aanmaken voor',
        roleByRequirementsWhoCanCreateRolesLabel: 'Iedereen kan rollen aanmaken op',
        whoCanCreateRolesForThemselvesLabel: 'Iedereen kan rollen aanmaken voor zichzelf op',
        creatorActionsInfoBannerLabel: 'Onderstaande acties zijn alleen beschikbaar voor de maker van de rol.',
        deleteRolesWithAssigneesInfoBannerLabel: 'Om rollen met meerdere toegewezen personen te verwijderen, moet de gebruiker voldoen aan deze voorwaarde voor alle toegewezen personen.',
        draftRolesActionsTitle: 'Concept',
        requesterCanEditDeleteLabel: 'Conceptrollen bewerken en verwijderen',
        requesterCanSubmitLabel: 'Aanvraag indienen',
        requesterCanArchiveLabel: 'Archiveren',
        requestedRolesActionsTitle: 'Aangevraagd',
        requesterCanRestartRequestedLabel: 'Opnieuw starten',
        requesterCanDeleteRequestedLabel: 'Aangevraagde rollen verwijderen',
        restartingActionsTitle: 'Wordt opnieuw gestart',
        requesterCanRestartRejectedLabel: 'Afgewezen rollen opnieuw starten',
        requesterCanRestartArchivedLabel: 'Gearchiveerde rollen opnieuw starten',
        completedRolesActionsTitle: 'Voltooide rollen',
        requesterCanDeleteLiveLabel: 'Live rollen verwijderen',
        requesterCanDeleteRejectedLabel: 'Afgewezen rollen verwijderen',
        requesterCanDeleteArchivedLabel: 'Gearchiveerde rollen verwijderen',
        assignerActorTitle: 'Toewijzer',
        assignerActorDescription: 'Wijst resources toe aan aangevraagde rollen op basis van de vereisten.',
        assignerWhoCanRespondLabel: 'kan rollen toewijzen',
        assignerCanAssignResourcesLabel: 'Resources toewijzen aan rollen',
        approverActorTitle: 'Goedkeurder',
        approverActorDescription: 'Reageert op aanvragen.',
        appproverWhoCanRespondLabel: 'kan reageren op aanvragen voor',
        approverCanMakeLiveLabel: 'Live gaan',
        criteriaRoleCanMakeLiveLabel: 'Maak een rol of toegewezen persoon live',
        approverCanRejectLabel: 'Afwijzen',
        criteriaRoleCanRejectLabel: 'Wijs een rol of toegewezen persoon af',
        approverCanRestartLabel: 'Opnieuw starten',
        approverCanDeleteRequestedLabel: 'Aangevraagde rollen verwijderen',
        approverCanDeleteLiveLabel: 'Live rollen verwijderen',
        approverCanDeleteRejectedLabel: 'Afgewezen rollen verwijderen',
        approverCanDeleteArchivedLabel: 'Gearchiveerde rollen verwijderen',
        approverSelectedResourcesInvalidValue: 'Maximaal ${maxLimitCount} resources. Als er meer goedkeurders nodig zijn, overweeg dan \'Geselecteerde beveiligingsprofielen’ te gebruiken.',
        assignerSelectedResourcesInvalidValue: 'Maximaal ${maxLimitCount} resources. Als er meer toegewezen personen nodig zijn, overweeg dan \'Geselecteerde beveiligingsprofielen’ te gebruiken.',
        requesterSelectedResourcesInvalidValue: 'Maximaal ${maxLimitCount} resources. Als er meer aanvragers nodig zijn, overweeg dan \'Geselecteerde beveiligingsprofielen’ te gebruiken.',
        selectedSecurityProfilesInvalidValue: 'Maximaal ${maxLimitCount} beveiligingsprofielen',
        addAnotherPrefix: 'Voeg een ... toe',
        noResultsMessagePrefix: 'Nee',
        noResultsMessageSuffix: 'is gevonden met deze naam.',
        multiValueFieldErrorMessagePrefix: 'Selecteer ten minste één',
        saveButtonLabel: 'Wijzigingen opslaan',
        cancelButtonLabel: 'Annuleren',
        formHasErrorsMessage: 'Bestand bevat fouten',
        noResultsFoundMessage: 'Geen resultaten gevonden',
        roleCreatorLabel: 'De rol maker en',
        whoCanActAsRequesterLabel: 'kunnen optreden als een aanvrager van een rol.'
    },
    //report settings
    reportSettingsSubLabel: 'Doelen stellen voor uw organisatie',
    reportSettingsErrorTitle: 'Onjuiste doelwaarde',
    reportSettingsBillabillityLabel: 'Algemeen factureerbaar gebruiksdoel',
    reportSettingsBillabilityRule: 'Stel een algemeen doel in om factureerbare actuele gebruiksgegevens te vergelijken met',
    reportSettingsJobOpportunityLabel: 'Drempel taakpercentage',
    reportSettingsJobOpportunityRule: 'Minimaal percentage om een taak als factureerbaar te rekenen',
    reportSettingsFieldTitle: 'Factureerbaar gebruik',

    //Colour schemes
    deleteColourSchemeTitle: '"{itemTobeDeleted}" kleurthema verwijderen?',
    deleteColourSchemeInformationMessage: 'Alle plannen die dit thema gebruiken keren terug naar het standaardthema. ',
    deleteColourSchemeWarningMessage: 'Deze actie kan niet ongedaan worden gemaakt.',
    colourSchemeCheckMessage: 'Ik begrijp de impact van het verwijderen van dit kleurthema.',
    deleteColourSchemePrimaryButton: 'Kleurthema verwijderen',
    deleteColourSchemeSecondaryButton: 'Kleurthema behouden',

    colorPickerText: 'Kleurkiezer',

    //security Profile
    securityProfilePageHeader: 'Beveiligingsprofielen',
    securityProfileFieldName: 'Naam beveiligingsprofiel bewerken',
    functionalAccessHeading: 'Algemeen',
    functionalAccessSubHeading: 'Bepaalt tot welke pagina’s en functies dit beveiligingsprofiel toegang heeft. U kunt bijvoorbeeld een regel voor Functionele toegang gebruiken om toegang tot de Beheerinstellingen te blokkeren.',
    yesLabel: 'Ja',
    noLabel: 'Nee',
    entityAccessSubHeading: 'Bepaal het toegangsniveau dat dit beveiligingsprofiel heeft tot {entityName}‘s.',
    skillEntitySubHeading: 'Bepaal het toegangsniveau dat dit beveiligingsprofiel heeft tot {entityName}.',
    entityAccessSubHeadingRemaining: 'Schakel toegang in of uit of stel toegangsniveaus in voor specifiekere controle.',
    readEntityResource: '{entityName}‘s zijn altijd zichtbaar voor gebruikers, maar u kunt de zichtbaarheid van bepaalde {entityName}‘s beperken door de controles hieronder te gebruiken. {lineBreak} Lees-beveiliging vervangt alle andere beveiligingsregels.',
    readEntityJob: '{entityName}‘s zijn altijd zichtbaar voor gebruikers, maar u kunt de zichtbaarheid van bepaalde {entityName}‘s beperken door de controles hieronder te gebruiken. {lineBreak} Gebruikers kunnen de taken waar ze voor zijn geboekt altijd bekijken. Lees-beveiliging vervangt alle andere beveiligingsregels.',
    readEntityBooking: '{entityName}‘s zijn zichtbaar afhankelijk van de leesvoorwaarden ingesteld voor de taak en resource. Lees-beveiliging vervangt alle andere beveiligingsregels.',
    readEntityRole: '{entityName}‘s zijn zichtbaar afhankelijk van de leesvoorwaarden ingesteld voor de taak en resource. Lees-beveiliging vervangt alle andere beveiligingsregels.',
    readEntityScenario: '{entityName}‘s zijn altijd zichtbaar voor alle gebruikers. Lees-beveiliging vervangt alle andere beveiligingsregels.',
    readEntityRoleRequest: '{entityName}‘s zijn altijd zichtbaar voor alle gebruikers. Lees-beveiliging vervangt alle andere beveiligingsregels.',
    readEntityClient: '{entityName}‘s zijn altijd zichtbaar voor alle gebruikers. Lees-beveiliging vervangt alle andere beveiligingsregels.',
    readEntitySkill: 'Vaardigheden en certificeringen zijn altijd zichtbaar voor gebruikers, maar u kunt beperken welke vaardigheden en certificeringen ze aan hun eigen profiel toe kunnen voegen door de bedieningselementen hieronder te gebruiken.',
    createEntity: 'Als u dit uitschakelt, worden opties van de interface verborgen en wordt het aanmaken van {entityName}‘s via dit beveiligingsprofiel geblokkeerd.',
    editEntity: 'Als u dit uitschakelt, worden opties van de interface verborgen en wordt het bewerken van {entityName}‘s via dit beveiligingsprofiel geblokkeerd.',
    deleteEntity: 'Als u dit uitschakelt, worden opties van de interface verborgen en wordt het verwijderen van {entityName}‘s via dit beveiligingsprofiel geblokkeerd.',
    customConditionsAreaHeader: '{entityName}‘s die...',
    liveRoleSubHeading: 'Live rollen',
    liveRoleBookingMessage: 'Live boekingen aanmaken via rollen wordt gecontroleerd door boekingsmachtigingen, zie de ',
    workflowCardSubHeading: 'Workflow van de rol',
    workflowCardMessage: 'Beheer wie kan aanvragen, goedkeuren en andere acties kan ondernemen via de instellingen voor de workflow van de rol.',
    workflowCardBtnText: 'Ga naar workflow van de rol',
    workflowTurnedOff: 'Schakel \'Workflows\' in op het tabblad Algemeen van uw beveiligingsprofiel om deze pagina te openen.',
    subRuleCreateRequest: 'Als u dit uitschakelt, worden opties van de interface verborgen en wordt het aanmaken van aanvragen via dit beveiligingsprofiel geblokkeerd.',
    subRuleRejectRequest: 'Als u dit uitschakelt, worden opties van de interface verborgen en wordt het afwijzen van aanvragen via dit beveiligingsprofiel geblokkeerd..',
    subRuleAssignCriteriaRoles: 'Als u dit uitschakelt, worden opties van de interface verborgen en wordt het toewijzen van voorgestelde resources aan criteriarollen via dit beveiligingsprofiel geblokkeerd.',
    readRuleCondition: 'Welke {entityName}‘s kunnen ze zien?',
    createRuleCondition: 'Welke {entityName}‘s kunnen ze aanmaken?',
    editRuleCondition: 'Welke {entityName}‘s kunnen ze bewerken?',
    deleteRuleCondition: 'Welke {entityName}‘s kunnen ze verwijderen?',
    skillReadRuleCondition: 'Welke vaardigheden en certificeringen kunnen ze toevoegen aan hun eigen profiel?',
    securityProfileNameRequiredMsg: 'Voer een naam voor het beveiligingsprofiel in',
    uniqueSecurityProfileNameMsg: 'Voer een unieke naam voor het beveiligingsprofiel in',
    delete_failureWarningTitle: 'Kan beveiligingsprofiel niet verwijderen.',
    delete_failureWarningMessage: 'Kan beveiligingsprofiel {profileName} niet verwijderen. Dit beveiligingsprofiel wordt al gebruikt.',
    delete_failureButtonLabel: 'OK',
    fieldSecurityHeading: '{entityName} velden',
    fieldSecuritySubHeading: 'Stel in met welk {entityName} veld dit beveiligingsprofiel kan communiceren. Velden zijn standaard te bewerken, maar ze kunnen ook worden ingesteld op alleen-lezen of verborgen.',
    fieldSecurityInfoForCondition: 'Voor',
    fieldSecurityInfoThisFieldIsCondition: 'Dit veld is',
    fieldSecurityInfoOtherwiseCondition: 'Anders is het veld',
    mandatoryNotification: ' Dit is een verplicht veld. Het beperken van een verplicht veld kan onverwachte resultaten opleveren.',
    editAccessNotification: ' Bewerken is uitgeschakeld voor {entityName}‘s. \'Bewerkbaar\' selecteren heeft geen invloed op de toegang tot het veld.',
    readOnlyNotification: ' Dit is een systeem alleen-lezen veld dat niet bewerkbaar kan worden gemaakt',
    externalIdNotificationMessage: ' Dit veld is alleen bewerkbaar via het API-portaal',
    note: 'Opmerking',
    important: 'Belangrijk',
    emptyFieldSecurityViewMessage: 'Er zijn nog geen velden toegevoegd',
    accessLevel: 'Toegangsniveau',
    tabMessage: 'Tabblad {tabName}',
    skillCategoriesLabel: 'Vaardigheidscategorieën',
    departmentLabel: 'Afdelingen',
    divisionLabel: 'Divisies',
    skillEntityTypeLabel: 'Typen vaardigheden',
    serviceLineLabel: 'Servicelijnen',
    skillsCertificationLabel: 'Vaardigheden en certificeringen',
    viewbudgetEntity: 'NL_Allow users to view estimated budget values on roles and the actual values once resources are assigned_NL',
    managerApprovalAlert: 'NL_Users must have a manager set in \'Reports to\' field or they will not be able to update their skills_NL',
    managerApprovalSubHeading: 'NL_For users with this security profile any changes they make to their own skills will need to be approved by their manager. Skill preferences can be changed without approval_NL',
    customConditions: {
        operators: {
            Int: {
                LessThan: 'Minder dan',
                LessThanOrEqual: 'Minder dan of gelijk aan',
                Equals: 'Gelijk aan',
                GreaterThanOrEqual: 'Meer dan of gelijk aan',
                GreaterThan: 'Meer dan',
                NOT_EQUALS_OPERATOR: 'Is niet gelijk aan'
            },
            DateTime: {
                LessThanOrEqual: 'Voor',
                GreaterThanOrEqual: 'Na'
            },
            ID: {
                IN_OPERATOR: 'Maakt onderdeel uit van',
                NOT_IN_OPERATOR: 'Maakt geen onderdeel uit van'
            },
            Bool: {
                Equals: 'Gelijk aan'
            }
        },
        valueTypes: {
            relativeToToday: 'Ten opzichte van vandaag',
            blank: 'Leeg',
            selectedValues: 'Geselecteerde waarden',
            loggedInUserValue: 'Waarde ingelogde gebruiker',
            existingValue: 'Bestaande waarde',
            customValue: 'Aangepaste waarde'
        },
        relativeDateValues: {
            PLUS_180: 'Vandaag +180 dagen',
            PLUS_90: 'Vandaag +90 dagen',
            PLUS_28: 'Vandaag +28 dagen',
            PLUS_7: 'Vandaag +7 dagen',
            PLUS_1: 'Morgen',
            TODAY: 'Vandaag',
            MINUS_1: 'Gisteren',
            MINUS_7: 'Vandaag -7 dagen',
            MINUS_28: 'Vandaag -28 dagen',
            MINUS_90: 'Vandaag -90 dagen',
            MINUS_180: 'Vandaag -180 dagen'
        },
        noConditionOperatorError: 'Selecteer operator',
        noConditionValueTypeError: 'Selecteer waardetype',
        addConditionsListButtonLabel: '+ EN-voorwaarde toevoegen',
        andOperatorLabel: 'EN',
        maxConditionsCountLabel: ' Maximaal 3',
        addConditionRowButtonLabel: '+ OF-voorwaarde toevoegen',
        orOperatorLabel: 'OF',
        fieldHeaderLabel: 'Veld',
        operatorHeaderLabel: 'Operator',
        valueTypeHeaderLabel: 'Waardetype',
        valueHeaderLabel: 'Waarde',
        noResultsFoundMessage: 'Geen resultaten gevonden',
        pleaseEnterFieldLabel: 'Voer veld in',
        conditionFieldNamePlaceholder: 'Veld selecteren...',
        yesLabel: 'Ja',
        noLabel: 'Nee',
        wholeNumberInputError: 'Voer een geldige waarde in',
        commonPredefinedConditionsLabel: 'Vooraf gedefinieerde voorwaarde gebruiken',
        inheritReadPredefinedConditionsLabel: 'Gebruik voorwaarden voor alleen-lezen ingesteld voor Taak en Resource',
        commonCustomConditionLabel: 'Aangepaste voorwaarde aanmaken',
        inheritReadCustomConditionLabel: 'Voeg aangepaste voorwaarde toe naast leesvoorwaarden ingesteld voor Taak en Resource',
        addAnotherPrefix: 'Toevoegen',
        deleteRowLabel: 'Rij verwijderen',
        pleaseEnterValueLabel: 'Voer een waarde in',
        noResultsMessagePrefix: 'Nee',
        noResultsMessageSuffix: 'is gevonden met deze naam.',
        jsonConditionLabel: 'Aangepaste JSON-regel specificeren',
        jsonConditionWarningBannerText: 'Aangepaste JSON-regels moeten voorzichtig worden gebruikt, aangezien ze tot prestatieproblemen kunnen leiden.\nVerkeerde configuratie van JSON-regels kan tot stabiliteitsproblemen leiden.',
        invalidJsonErrorMessage: 'Zorg dat de JSON geldig is.',
        apiPortalInfoText: 'Valideer JSON-regels automatisch door buiten het tekstgedeelte te klikken',
        apiPortalLabel: 'API-portaal.',
        inheritReadJsonConditionLabel: 'Specificeer de aangepaste JSON-regel naast leesvoorwaarden ingesteld voor Taak en Resource.',
        //Skills
        addSkillHeader: 'Vaardigheden en certificeringen aan profielpagina toevoegen',
        allSkillsLabel: 'Alle vaardigheden en certificeringen',
        onlyTheseSkillsLabel: 'Alleen deze',
        onlyRelatedSkillsLabel: 'Alleen vaardigheden en certificeringen gerelateerd aan deze',
        emptySkillErrorMessage: 'Selecteer ten minste één waarde',
        emptyJsonConditionErrorMessage: 'Selecteer ten minste één van de opties'
    },
    //entitiesConfiguration
    planningDataAliasUseCaseInfo: 'Een alias is een bekendere term die in uw applicatie kan worden gebruikt. Voeg aliassen toe om onbekende items herkenbaarder te maken.',
    aliasHeading: 'Alias',
    singularAliasLabel: 'Enkelvoudig alias',
    pluralAliasLabel: 'Meervoudig alias',
    BookingDescription: 'Een boeking vertegenwoordigt een toewijzing van een resource aan een taak voor een specifiek datumbereik en bestaat uit een meetbaar aantal uren. Boekingen kunnen worden aangemaakt voor taken als Projectmanagement of Testen en ook voor taken als vakantie of ziekteverzuim. Iedere boeking moet worden doorgevoerd ten opzichte van een taak.',
    JobDescription: 'Een taak is een project, taak of opdracht die een resource vereist. Een taak kan worden aangemaakt voor activiteiten als Projectmanagement of Testen en ook voor activiteiten als vakantie of ziekteverzuim.',
    ResourceDescription: 'Resource verwijst naar materialen, medewerkers of andere activa die kunnen worden gebruikt door een organisatie om effectief te functioneren. Ieder item met een oneindige capaciteit dat een taak kan uitvoeren kan als een resource worden beschouwd. Medewerkers, trainingsruimtes en apparatuur zijn voorbeelden van resources.',
    ClientDescription: 'Klanten verwijst naar cliënten of klanten van uw organisatie. Een klant kan meerdere taken hebben.',
    rolerequestgroupDescription: 'Een scenario is een verzameling rollen voor een bepaalde taak.',
    rolerequestDescription: 'Een rol is een voorloper voor één of meer boekingen. Ze kunnen worden aangemaakt met een resource of bepaalde criteria in gedachten.',
    DepartmentDescription: 'Een afdeling is een onderdeel van een organisatie dat gespecialiseerd is in een bepaald bedrijfsproces en bestaat vaak uit resources met vergelijkbare vaardigheden en verantwoordelijkheden.',
    DivisionDescription: 'Een divisie is een bedrijfsunit uit het bovenste segment of een onderdeel binnen een organisatie en bestaat uit meerdere afdelingen.',
    //Skill Types Message
    //Levels
    skillPageHeader: 'Vaardigheden en certificeringen',
    skillTypeLevelsTabTitle: 'Niveaus',
    skillTypeSkillsTabTitle: 'Vaardigheden',
    fieldsTabTitle: 'Velden',
    addSkill: 'Een vaardigheid toevoegen',
    addRetainSkillLibrary: 'Toevoegen uit de Retain-bibliotheek',
    levelNameDescriptionText: 'Niveaus voor deze vaardigheid staan bekend als',
    levelNameInfoText: '{levelName} 1 is het laagste niveau.',
    addLevels: '{levelName} toevoegen',
    skillTypeLevelNameRequiredMessage: 'Voer een niveaunaam in',
    whiteSpaceValidation: 'Leeg laten niet toegestaan voor optie',
    skillLevelRequiredValidation: 'Niveaunaam is vereist',
    skillLevelUniqueValidation: 'Niveaunaam bestaat al',
    skillLevelNamePlaceholder: 'Niveaunaam',
    skillLevelDescriptionPlaceholder: 'Niveaubeschrijving',
    //Skill fields
    skillFieldHeading: 'Aanvullende velden voor deze vaardigheid',
    addFieldButtonLabel: 'Een veld toevoegen',
    skillCommandBarFieldName: 'Naam vaardigheid bewerken',
    //reusable grid
    cancelDeletion: 'Verwijdering annuleren',
    markedForDeletion: 'Gemarkeerd voor verwijdering. Wordt verwijderd wanneer u de wijzigingen bevestigt. ',
    addButtonReusableGrid: 'Een rij toevoegen',
    mandatory: 'Verplicht',
    markDeleteWarningTitle: 'Weet u zeker dat u "{recordName}" wilt markeren voor verwijdering?',
    markDeleteWarningTitleDefault: 'Weet u zeker dat u deze record wilt markeren voor verwijdering?',
    //Field Properties
    fieldFormattingDisabledTooltipText: 'Kan niet worden gewijzigd in ingebouwde velden',
    fieldPropetiesAliasDefinition: 'Een alias is een bekendere term die in uw applicatie kan worden gebruikt.',
    fieldPropetiesChooseAliasMessage: 'Kies een alias voor de geselecteerde tabel en voer labels in voor veldnamen die in uw applicatie kunnen worden gebruikt.',
    noDescriptionMsg: 'Klik hier om een beschrijving toe te voegen',
    configurePageField: '{pageTitle} velden',
    descriptionText: 'Beschrijving',
    builtInTabsTitle: 'Ingebouwde velden',
    customFieldTabsTitle: 'Aangepaste velden',
    builtInTabsDescription: 'Dit zijn de standaard {entity} velden ingebouwd in het systeem. Ze kunnen niet worden gewijzigd, maar wel verborgen als u ze niet nodig heeft.',
    customFieldTabsDesc: 'Dit zijn velden die u aan de applicatie heeft toegevoegd. Ze worden naast de {entity} velden van het systeem weergegeven.',
    builtInFieldTab: 'Ingebouwd',
    customText: 'Aangepast',
    customFieldAddButton: 'Aangepast veld toevoegen',
    emptyCustomViewMessage: 'Er zijn nog geen aangepaste velden toegevoegd',
    emptyBuiltInViewMessage: 'Er zijn nog geen ingebouwde velden toegevoegd',
    fieldLabelReqdValidation: 'Het veldlabel mag niet leeg zijn',
    uniqueFieldNameValidation: 'Het ${label} moet uniek zijn',
    bracketsFieldNameValidation: 'Het ${label} mag geen [ of ] bevatten',
    lookupFieldInputRequired: 'Het zoekveld mag niet leeg zijn',
    noMatchesText: 'Geen overeenkomsten',
    lookUpField: 'Waarden van',
    lookUpLinkText: 'Configuratie zoekvelden',
    maxCharMessage: 'Maximaal {maxChar} tekens',
    maxCharTagMessage: 'Limiet alleen toegepast op door gebruiker gedefinieerde tags. Maximaal {maxChar}',
    incorrectDecimalFormatMsg: 'Onjuist decimaal formaat (voer numerieke waarde in met maximaal 10 decimalen)',
    incorrectTargetBillabilityMessage: 'Minimaal toegestane waarde is 0',
    inputBeyondLimitMsg: 'Invoerwaarde heeft de limiet overschreden',
    noOfChars: 'Aantal tekens',
    typeText: 'Type',
    labelText: 'Label',
    hiddenText: 'Verborgen',
    decimalPlacesText: 'Decimalen',
    exampleText: 'Voorbeeld',
    lookupValuesLabel: 'Zoekwaarden',
    newFieldValuesLabel: 'Naam van nieuwe lijst',
    nextLabel: 'Volgende',
    prevLabel: 'Vorige',
    fieldValuesTitle: 'Veldwaarden',
    fieldValuesSubTitle: 'De lijst met waarden voor dit veld specificeren',
    lookupValuesNotifyMessage: 'Dit kan niet worden gewijzigd als het veld al is aangemaakt.{lineBreak} Waarden in de lijst kunnen later worden bewerkt op de pagina \'Waarden\'.',
    newFieldNameRequiredMessage: 'Naam veldwaarde is vereist',
    fieldAliasRequiredValidation: '{fieldAlias} alias is vereist',
    fieldDescriptionRequiredValidation: '{fieldDescription} beschrijving is vereist',
    newFieldValuesRequiredMessage: 'Lijst met veldwaarden mag niet leeg zijn',
    multiSelectText: 'Meerdere selecteren',
    valuesListLabel: 'Lijst met waarden',
    useExistingValuesText: 'Een bestaande lijst met waarden gebruiken',
    createNewValuesListText: 'Een nieuwe lijst met waarden aanmaken',
    createNewValuesSaveInfo: 'De nieuwe lijst wordt opgeslagen op de pagina \'Waarden\'',
    planningDataSaveInfo: 'Dit kan niet worden gewijzigd als het veld al is aangemaakt',
    formattingLabel: 'Opmaak',
    minimumLabel: 'Min.',
    maximumLabel: 'Max.',
    valuesText: 'Waarden',
    valueFieldsCommandBarFieldName: 'Naam veldwaarde bewerken',
    calculatedMessage: ' Dit veld wordt automatisch berekend op basis van gegevens in uw applicatie.',
    rangeLabel: 'Bereik',
    systemReadonlyMessage: 'Dit veld is alleen-lezen en bevat door de applicatie bijgewerkte informatie',
    systemRequiredMessage: 'Dit veld is vereist door het systeem om {entity} aan te kunnen maken',
    fieldNameLabel: 'Veldnaam',
    deletefieldsHeading: 'Velden verwijderen',
    keepFieldsHeading: 'Velden behouden',
    newText: 'Nieuw',
    fieldText: 'veld',
    warningMessageWithFieldNames: 'U staat op het punt om de {fieldsArray} ',
    warningMessageMoreFields: 'en nog eens {number} velden te verwijderen.Als u deze velden verwijdert, worden alle gegevens in deze velden ook verwijderd. ',
    warningMessageLessFields: 'velden.Als u deze velden verwijdert, worden alle gegevens in deze velden ook verwijderd.',
    deletefieldsHeadingConfirmation: 'Velden en hun gegevens verwijderen',
    deleteSkillsButtonLabel: 'Vaardigheden verwijderen',
    keepSelectedSkillsLabel: 'Geselecteerde vaardigheden behouden',
    fieldPropertiesLicenseWarning: 'U heeft de door uw licentie toegestane limiet bijna bereikt. {contactUs} om de limiet van uw plan te verhogen.',
    fieldPropertieslicenseError: 'U heeft de door uw licentie toegestane limiet bijna bereikt. {contactUs} om de limiet van uw plan te verhogen.',
    showLookupLink: 'Zoekwaarden weergeven',
    lookupValueUniqueValidation: 'Deze waarde is al toegevoegd',
    pressEnterMessage: 'Druk op Enter om waarde toe te voegen',
    defaultValueLabel: 'Standaardwaarde',
    noDefaultValueStaticHeaderText: 'Geen standaard',
    multipleDefaultValueStaticHeaderText: 'Meerdere waarden',
    noDefaultPlaceHolder: 'Geen standaard',
    invalidDefaultValueValidation: 'Ongeldige standaardwaarde',
    defaultValueSelectOwnValueText: 'Waarde selecteren of leeg laten',
    defaultValueInheritValueText: 'Waarde overnemen van ingelogde gebruiker',
    defaultValueInheritSummaryText: 'Overnemen van ... van de gebruiker',
    defaultValueFieldText: 'De {fieldName} van de gebruiker wordt overgenomen',
    defaultValueFieldDisabledText: 'Er komt een nieuwe lijst met zoekwaarden beschikbaar om als standaard in te stellen als u dit veld opslaat',
    inheritUserField: '"{fieldName}" van de gebruiker',
    yesMsg: 'Ja',
    noMsg: 'Nee',
    //Field look up values config
    fieldLookupValueAddButton: 'Een waarde toevoegen',
    noResultsText: 'Geen resultaten',
    fieldLookupValuesDescription: 'Voeg waarden toe die in de lijst worden weergegeven of bewerk of verwijder ze. Een lijst voor Locatie kan bijvoorbeeld waarden weergeven als Londen, Parijs en New York.',
    fieldLookupValuesEntityData: 'Velden die deze waarden momenteel gebruiken',
    saveFieldLookupValueAlert: 'Geselecteerde waarden verwijderen?',
    saveFieldLookupValueWarning: 'Als waarden worden verwijderd, worden ze verwijderd uit vervolgkeuzemenu’s voor zoeken en alle overige locaties waar deze waarden worden weergegeven.',
    fieldLookupCheckMessage: 'U heeft {deleteCount} waarden geselecteerd om te verwijderen.',
    yesDeleteValuesButtonLabel: 'Ja, verwijder de waarden',
    noKeepValuesButtonLabel: 'Nee, behoud de waarden',
    optionIsRequiredMessage: 'Optie is vereist',
    optionAlreadyExist: 'Optie bestaat al',
    uniqueValueNameMessage: 'Voer een unieke waardenaam in',
    requiredValueNameMessage: 'Voer een waardenaam in',
    fieldLookupValueInUseTitle: 'Lijsten met waarden kunnen niet worden verwijderd',
    fieldLookupValueInUseMessage: 'Lijsten met waarden die momenteel worden gebruikt door een veld kunnen niet worden verwijderd.',
    fieldLookupValueInUseSubMessage: 'U moet alle gekoppelde velden verwijderen voordat u de lijsten met waarden verwijdert.',
    fieldLookupValuesSortText: 'Volgorde van waarden',
    fieldLookupValuesDefaultSortModeText: 'Alfabetisch (A-Z)',
    fieldLookupValuesCustomSortModeText: 'Oplopend (aangepaste volgorde)',
    fieldLookupValuesSortModeDescriptionText: 'Menu’s en lijsten volgen deze volgorde om waarden weer te geven',
    fieldLookupValuesCustomSortGridText: 'Plaats de minst belangrijke waarde als eerste in de lijst',
    noneText: 'geen',
    typeNewValue: 'Een nieuwe regel invoeren',
    //System Settings
    fieldPropertiesPageHeader: 'Velden',
    fieldLookupValuesPageHeader: 'Waarden',
    gridColoumnTitle_Option: 'Optie',
    gridColoumnTitle_Target_Billability: 'Factureerbaar gebruik',
    //PageNames
    pageNameHeader: 'Paginanamen',
    pageNamesHeaderSummary: 'Selecteer de pagina’s en hun weergavenamen die in het menu links worden weergegeven.',
    menuListTitle: 'Menu Lijst',
    settingValMsg: 'Voer de instellingswaarde in',
    maxSizeFieldMsg: 'Max. grootte van veld is 40 telkens',
    displayText: 'Weergave',
    enableText: 'Inschakelen',
    summaryPageNameText: 'Logo - geen zichtbaar label',
    Timesheets: 'Urenstaten',
    Report: 'Rapport',
    Role_inbox: 'Rol inbox',
    Roles_board: 'Rollenbord',
    Table_View: 'Tabelweergave',
    //dynamic generated child sections from setting api (System settings)
    Talent_Profile: 'Talentprofiel',
    Jobs: 'Taken',
    Scheduler: 'Planner',
    //Currencies
    currenciesPageHeader: 'Valuta’s',
    //charge codes
    chargeTypePageHeader: 'Factureringstypen',
    whiteSpaceValidationChargeType: 'Leeg laten niet toegestaan voor factureringstype',
    chargeCodeContent: 'Met factureringstypen kunt u verschillende tarieven aanmaken voor verschillende soorten taken. Maak meerdere factureringstypen aan om de verschillende binnen uw bedrijf uitgevoerde werkzaamheden te dekken.',
    chargeRateContent: 'Voeg hier {ChargeRateLinkText} tarieven voor kosten en opbrengsten toe of bewerk of verwijder ze voor verschillende factureringscodes',
    chargeRateRedirection: 'voor specifieke periodes.',
    chargeCodeUsageWarning: 'Dit factureringstype wordt momenteel <strong>gebruikt door {chargeTypeJobCount} taken.</strong>',
    chargeCodeWarning: 'Als u dit factureringstype verwijdert, wordt het verwijderd uit alle records en kan niet meer worden gebruikt voor budgetinformatie en rapportage.',
    chargeTypeDeletionWarning: 'Factureringstype {chargeTypeName} verwijderen',
    deleteChargeTypePrimaryButton: 'Factureringstype verwijderen',
    deleteChargeTypeSecondaryButton: 'Factureringstype behouden',
    chargeCodeCheckMessage: 'Ik begrijp de impact van het verwijderen van dit factureringstype',
    chargeCodeUniqueValidation: 'Voer een uniek factureringstype in',
    chargeCodeReqdValidation: 'Factureringstype is vereist',
    chargeCodeAddButton: 'Een factureringstype toevoegen',
    noChargeCodeAvailable: 'Geen factureringstype beschikbaar',
    waterMarkChargeCode: 'Factureringstype zonder titel',
    waterMarkDescription: 'Beschrijving van het factureringstype',
    chargeTypeHeader: 'Factureringstype',

    //charge rates
    chargeRatePageHeader: 'Factureringstarieven',
    hourlyChargeRatesHeading: 'Uurtarieven per type taak',
    waterMarkRevenue: 'Opbrengst van het factureringstarief',
    waterMarkCost: 'Kosten van het factureringstarief',
    chargeRateDeleteMessage: 'U gaat dit factureringstarief verwijderen voor toekomstig gebruik.',
    chargeRateDeleteSubMessage: 'Dit factureringstarief wordt nog steeds toegepast op bestaande boekingen.',
    cantUndoMessage: 'Dit kan niet ongedaan worden gemaakt.',
    checkMessage: 'Ik begrijp de consequenties van deze verwijdering.',
    deleteChargeRateTitle: 'Factureringstarief "{deletedChargeRate}" verwijderen?',
    deleteChargeCodeSpecificChargeRateTitle: 'Factureringstarief "{deletedChargeCodeName}" van "{deletedChargeRate}" verwijderen?',
    deleteChargeRateHeading: 'Dit factureringstarief wordt momenteel gebruikt door {chargeRateResourceCount} resources.',
    deleteChargeRateWarningMessage: 'Als u dit verwijdert, wordt het verwijderd uit alle records en kan niet meer worden gebruikt voor budgetinformatie en rapportage.',
    deleteChargeRateCheckboxMessage: 'Ik begrijp de impact van het verwijderen van dit factureringstarief',
    deleteChargeRateButtonLabel: 'Factureringstarief verwijderen',
    keepChargeRateButtonLabel: 'Factureringstarief behouden',
    editChargeRateTitle: 'Huidige factureringstarief "{editedChargeRateName}" aanpassen?',
    editChargeRateHeading: 'Dit is het huidige factureringstarief voor het factureringstype "{editedChargeRateName}".',
    editChargeRateSubHeading: 'Als u dit factureringstarief wijzigt, wordt dit aangepast voor alle berekeningen en rapporten voor deze periode.',
    editChargeRateWarningMessage: 'Weet u zeker dat u het huidige factureringstarief voor "{editedChargeRateName}" wilt wijzigen?',
    editChargeRatePrimaryButton: 'Actief tarief aanpassen',
    addCostRevenueButtonLabel: 'Kosten en opbrengsten toevoegen',
    customGridRangeOverlappingMessage: 'Er zijn al kosten en opbrengsten gedefinieerd voor datums binnen dit bereik',
    customGridRangeRequiredMessage: 'Voer een datumbereik in',
    customGridRevenueRequiredMessage: 'Voer opbrengsten in',
    customGridCostRequiredMessage: 'Voer kosten in',
    chargeRateNameField: 'Naam factureringstarief',
    chargeRateTitleRequiredMessage: 'Voer naam factureringstarief in',
    chargeRateTitleUniqueMessage: 'Voer een unieke naam voor het factureringstarief in',
    customGridNegativeMessage: 'Deze waarde mag niet negatief zijn',
    dateRangeLabel: 'Datumbereik',
    revenueLabel: 'Opbrengsten',
    costLabel: 'Kosten',
    //skills
    noRecommendations: 'Geen nieuwe aanbevelingen',
    addSkillsHeading: 'Vaardigheden toevoegen voor deze vaardigheid',
    activeSkillTitle: '\'{activeSkillConfiguration}\' is een van de vaardigheidssecties die op het talentprofiel van de gebruiker wordt weergegeven.',
    skillHeaderText: 'Vaardigheden onder {activeSkillConfiguration}',
    skillHeaderDescription: 'Gebruikers kunnen de volgende vaardigheden kiezen in het gedeelte \'{activeSkillConfiguration}\' op hun profielen.',
    skillImportMessage: 'Voor bulkupdates gebruikt u wellicht liever de functie {Import}',
    addSkillButtonFromCommandBar: 'Een vaardigheid toevoegen',
    skillNameLabel: 'Naam vaardigheid',
    skillType: 'Type',
    addSkillCategoryText: 'Categorie toevoegen',
    skillCategory: 'Categorie',
    skillSubCategory: 'Subcategorie',
    tagsLabel: 'Tags',
    skillTagExplaination: 'Tags zijn labels die aan vaardigheden kunnen worden toegevoegd. Tags maken een vaardigheid zoeken eenvoudiger, met name wanneer de vaardigheid bekend staat onder een andere naam. Druk op Enter na het invoeren van een tag, voordat u deze opslaat.',
    skillNameRequired: 'Voer naam vaardigheid in',
    uniqueSkillNameRequired: 'Voer een unieke naam voor de vaardigheid in',
    uniqueSkillItem: 'Naam voor de vaardigheid moet uniek zijn',
    deleteSkillFieldsPopupHeading: 'Geselecteerde velden verwijderen?',
    deleteSkillFieldsPopupWarningMessage: 'Wilt u de geselecteerde vaardigheidsvelden permanent verwijderen? {number} vaardigheidsvelden worden verwijderd uit de vaardigheidstype(n) en uit resourceprofielen, en alle overige plaatsen waar het vaardigheidsveld wordt weergegeven.',
    deleteSkillFieldsButtonLabel: 'Vaardigheidsvelden verwijderen',
    keepSkillFieldsButtonLabel: 'Vaardigheidsvelden behouden',
    deleteSkillLevelsPopupHeading: 'Geselecteerde niveaus verwijderen?',
    deleteSkillLevelsPopupWarningMessage: 'Wilt u de geselecteerde vaardigheidsniveaus permanent verwijderen? Verwijderde vaardigheidsniveaus worden verwijderd uit resourceprofielen en alle overige plaatsen waar deze vaardigheidsniveaus worden weergegeven.',
    deleteSkillLevelsButtonLabel: 'Vaardigheidsniveaus verwijderen',
    keepSkillLevelsButtonLabel: 'Vaardigheidsniveaus behouden',
    deleteSkillsPopupHeading: 'Geselecteerde vaardigheden verwijderen?',
    deleteSkillsPopupWarningMessage: 'Wilt u de geselecteerde vaardigheden permanent verwijderen? {number} vaardigheden worden verwijderd uit het systeem en uit gebruikersprofielen, en alle overige plaatsen waar vaardigheden worden weergegeven.',
    keepSkillsButtonLabel: 'Vaardigheden behouden',
    deleteSkillsAndLevelsPopupHeading: 'Geselecteerde vaardigheden en vaardigheidsniveaus verwijderen?',
    deleteSkillsAndLevelsPopupWarningMessage: 'Wilt u de geselecteerde vaardigheden en vaardigheidsniveaus permanent verwijderen?',
    deleteSkillsAndFieldsPopupHeading: 'Geselecteerde vaardigheden en vaardigheidsvelden verwijderen?',
    deleteSkillsAndFieldsPopupWarningMessage: 'Wilt u de geselecteerde vaardigheden en vaardigheidsvelden permanent verwijderen?',
    deleteLevelsAndFieldsPopupHeading: 'Geselecteerde vaardigheidsniveaus en vaardigheidsvelden verwijderen?',
    deleteLevelsAndFieldsPopupWarningMessage: 'Wilt u de geselecteerde vaardigheidsniveaus en vaardigheidsvelden permanent verwijderen?',
    deleteSkillsLevelsAndFieldsPopupHeading: 'Geselecteerde vaardigheden, vaardigheidsniveaus en vaardigheidsvelden verwijderen?',
    deleteSkillsLevelsAndFieldsPopupWarningMessage: 'Wilt u de geselecteerde vaardigheden, vaardigheidsniveaus en vaardigheidsvelden permanent verwijderen?',
    deletePopupWarningMessage: 'Items worden verwijderd uit dit vaardigheidstype en uit resourceprofielen en alle overige plaatsen waar ze worden weergegeven.',
    deleteSkillsPopupConfirmation: 'Ik begrijp de consequenties van deze actie.',
    deleteItemsButtonLabel: 'Deze items verwijderen',
    keepItemsButtonLabel: 'Deze items behouden',
    deleteAndModifySkillFieldsPopupHeading: 'Wijzigingen opslaan en geselecteerde velden verwijderen?',
    deleteAndModifySkillFieldsPopupWarningMessage: 'Wilt u wijzigingen opslaan en de geselecteerde vaardigheidsvelden permanent verwijderen? {number} vaardigheidsvelden worden verwijderd uit de vaardigheidstype(n) en uit resourceprofielen, en alle overige plaatsen waar het vaardigheidsveld wordt weergegeven.',
    deleteAndModifySkillLevelsPopupHeading: 'Wijzigingen opslaan en geselecteerde niveaus verwijderen?',
    deleteAndModifySkillLevelsPopupWarningMessage: 'Wilt u wijzigingen opslaan en de geselecteerde vaardigheidsniveaus permanent verwijderen? Verwijderde vaardigheidsniveaus worden verwijderd uit resourceprofielen en alle overige plaatsen waar deze vaardigheidsniveaus worden weergegeven.',
    deleteAndModifySkillsPopupHeading: 'Wijzigingen opslaan en geselecteerde vaardigheden verwijderen?',
    deleteAndModifySkillsPopupWarningMessage: 'Wilt u wijzigingen opslaan en de geselecteerde vaardigheden permanent verwijderen? {number} vaardigheden worden verwijderd uit het systeem en uit gebruikersprofielen, en alle overige plaatsen waar vaardigheden worden weergegeven.',
    deleteAndModifySkillsAndLevelsPopupHeading: 'Wijzigingen opslaan en geselecteerde vaardigheden en vaardigheidsniveaus verwijderen?',
    deleteAndModifySkillsAndLevelsPopupWarningMessage: 'Wilt u wijzigingen opslaan en de geselecteerde vaardigheden en vaardigheidsniveaus permanent verwijderen?',
    deleteAndModifySkillsAndFieldsPopupHeading: 'Wijzigingen opslaan en geselecteerde vaardigheden en vaardigheidsvelden verwijderen?',
    deleteAndModifySkillsAndFieldsPopupWarningMessage: 'Wilt u wijzigingen opslaan en de geselecteerde vaardigheden en vaardigheidsvelden permanent verwijderen?',
    deleteAndModifyLevelsAndFieldsPopupHeading: 'Wijzigingen opslaan en geselecteerde vaardigheidsniveaus en vaardigheidsvelden verwijderen?',
    deleteAndModifyLevelsAndFieldsPopupWarningMessage: 'Wilt u wijzigingen opslaan en de geselecteerde vaardigheidsniveaus en vaardigheidsvelden permanent verwijderen?',
    deleteAndModifySkillsLevelsAndFieldsPopupHeading: 'Wijzigingen opslaan en geselecteerde vaardigheden, vaardigheidsniveaus en vaardigheidsvelden verwijderen?',
    deleteAndModifySkillsLevelsAndFieldsPopupWarningMessage: 'Wilt u wijzigingen opslaan en de geselecteerde vaardigheden, vaardigheidsniveaus en vaardigheidsvelden permanent verwijderen?',
    skillCategoryDeletePopupHeading: 'Vaardigheidscategorie verwijderen?',
    skillCategoryDeletePopupWarningMessage: 'Wilt u deze vaardigheidscategorie verwijderen? De vaardigheidscategorie bevat {number} vaardigheden, die allemaal worden losgekoppeld. De niveaus en velden die aan deze categorie zijn gekoppeld, worden verwijderd uit het systeem en alle profielen waarin ernaar wordt verwezen.',
    deleteSkillCategoryButtonLabel: 'Vaardigheidscategorie verwijderen',
    keepSkillCategory: 'Vaardigheidscategorie behouden',
    skillTypeDeletePopupHeading: 'Vaardigheidstype verwijderen?',
    skillTypeDeletePopupWarningMessage: 'Wilt u dit vaardigheidstype permanent verwijderen? Dit vaardigheidstype bevat {number} vaardigheden, die allemaal worden verwijderd uit het systeem en van alle profielen waar ernaar wordt verwezen.',
    deleteSkillTypeButtonLabel: 'Vaardigheidstype verwijderen',
    keepSkillType: 'Vaardigheidstype behouden',
    fieldAlreadyExistHeader: 'Voer een unieke veldnaam in',
    fieldAlreadyExistDescription: 'Deze veldnaam bestaat al. Gebruik het bestaande veld, of wijzig de naam van dit veld om het uniek te maken.',
    skillNamePlaceholder: 'Naam vaardigheid invoeren',
    skillDescriptionPlaceholder: 'Beschrijving vaardigheid invoeren',
    skillNameHeader: 'Naam vaardigheid',
    fieldTypeHeader: 'Veldtype',
    categoryText: 'Categorie',
    defaultValueText: 'Standaardwaarde',
    newTag: 'Nieuwe tag',
    headerLevels: 'Niveaus',
    licenseCountSkill: '{activeCount} van {licensedCount} vaardigheden gebruikt voor dit vaardigheidstype',
    skillsLicenseWarning: 'U nadert de toegestane limiet voor vaardigheden van uw licentie. {contactUs} om de limiet van uw plan te verhogen.',
    skillsLicenseError: 'U heeft de toegestane limiet voor vaardigheden van uw licentie bereikt. {contactUs} om de limiet van uw abonnement te verhogen.',
    licenseCountSkillLevel: '{activeCount} van {licensedCount} niveaus gebruikt voor dit vaardigheidstype',
    licenseCountSkillField: '{activeCount} van {licensedCount} velden gebruikt voor dit vaardigheidstype',
    skillFieldsLicenseWarning: 'U nadert de toegestane limiet van uw licentie voor velden in dit vaardigheidstype. {contactUs} om de limiet van uw plan te verhogen.',
    skillFieldsLicenseError: 'U heeft de toegestane limiet bereikt van uw licentie voor velden in dit vaardigheidstype. {contactUs} om de limiet van uw plan te verhogen.',
    adminCommandBarActionLabel: 'Acties',
    adminCommandBarEditLabel: 'Bewerken',
    adminCommandBarUserStatusLabel: 'Actieve gebruiker',
    adminCommandBarSetActiveLabel: 'Instellen op actief',
    adminCommandBarSetInactiveLabel: 'Instellen op inactief',
    adminCommandBarResendEmailInviteLabel: 'Uitnodigingsmail verzenden',
    adminCommandBarResetPassphraseLabel: 'Wachtwoord resetten',
    disabledSkillType: 'Sla wijzigingen op of verwijder ze voordat u verdergaat',
    adminCommandBarSendCMeSurveyLabel: 'C-me enquête verzenden',
    // Skill Category
    skillCategoryRequired: 'Voer een categorienaam in',
    uniqueCategoryNameRequired: 'Voer een unieke categorienaam in',
    primarySkillLabel: 'Primaire vaardigheid',
    secondarySkillLabel: 'Secundaire vaardigheid',
    preferredSkillLabel: 'Gewenste vaardigheid',
    skillCategoryLabel: 'Vaardigheidscategorie',
    importInProgressText: 'Vaardigheden worden uit Retain-bibliotheek geïmporteerd',
    subscribed: 'Geabonneerd',
    notSubscribed: 'Niet geabonneerd',
    selectedImportDataLabel: '<bold>${skillCount} gekozen vaardigheden uit ${categoryCount} categorieën</bold>',
    skillCategoryExpiryMandatoryInfo: 'Stel dit in op Ja om Vervaldatum vaardigheid als verplicht veld in te stellen',
    skillExpiryEnabledInfo: 'NL_Set this to Yes to enable Skill expiry fields for this skill category_NL',
    importClickInfo: 'Als u op <bold>Importeren</bold> klikt, wordt deze importeertaak in de wachtrij gezet',
    skillCategorySubscribeInfo: 'Abonneer om een melding te ontvangen over updates van deze vaardigheidscategorie',
    importProgressInfo: 'Raadpleeg de <bold>Activiteitenlog</bold> voor de voortgang van deze taak weer te geven',
    skillExpiryDateLabel: 'NL_Skill expiry is mandatory_NL',
    skillExpiryEnabledLabel: 'NL_Enable skill expiry dates_NL',
    preNotificationLabel: 'Melding voorafgaand aan vervaldatum',
    watcherLabel: 'Kijker vaardigheidscategorie',
    existingSkillsHeader: 'Vaardigheden die eerder zijn toegevoegd, of met dezelfde naam als bestaande vaardigheden binnen uw organisatie, worden verborgen in de lijst',

    //skill Library
    addSkillLibrary: 'Toevoegen uit de Retain-bibliotheek',
    skillLibraryHeader: 'Retain-bibliotheek met vaardigheden',
    importLibraryStep1: {
        title: 'Vaardigheden kiezen',
        description: 'Kies vaardigheden die u wilt importeren'
    },
    importLibraryStep2: {
        title: 'Beoordelen',
        description: 'Beoordeel de vaardigheden die u hebt gekozen'
    },
    importLibraryStep3: {
        title: 'Importeren',
        description: 'Klaar om te importeren'
    },
    searchSkillPlaceholder:'NL_Search skill_NL',
    //Notifications
    notificationsPage: {
        commandBarConfig: {
            pageTitle: 'Meldingen'
        },
        notificationSettingSummary: {
            notificationSettingSubHeading: 'Selecteer of meldingen per type moeten worden weergegeven.'
        },
        notificationSettingConfig: {
            onLabel: 'Aan',
            offLabel: 'Uit'
        },
        notificationEvents: {
            bookingAssignedLabel: 'U bent toegewezen aan ${jobName} vanaf ${startDate} - ${endDate} ${timeAllocation} door ${editorName}',
            bookingUpdateLabel: 'Uw boeking op ${jobName} vanaf ${startDate} - ${endDate} ${timeAllocation} is bewerkt door ${editorName}',
            bookingDeleteLabel: 'U bent niet langer toegewezen aan ${jobName} vanaf ${startDate} - ${endDate} ${timeAllocation}',
            roleRequestCreateLabel: '${resourceName} is aangevraagd op ${jobName} ${startDate} - ${endDate} ${timeAllocation} door ${editorName}',
            roleRequestRejectLabel: 'Uw aanvraag voor ${resourceName} op ${jobName} ${startDate} - ${endDate} ${timeAllocation} is afgewezen door ${editorName}',
            roleRequestLiveLabel: 'Uw aanvraag voor ${resourceName} op ${jobName} ${startDate} - ${endDate} ${timeAllocation} is live gemaakt door ${editorName}',
            repeatBookingAssignedLabel: 'U bent toegewezen aan ${jobName} vanaf ${startDate} - ${endDate} ${timeAllocation} door ${editorName}. Deze boeking wordt elke ${interval} herhaald tot ${untilDate}',
            repeatBookingUpdateLabel: 'Uw boeking op ${jobName} vanaf ${startDate} - ${endDate} ${timeAllocation} is bewerkt door ${editorName}. Deze boeking wordt elke ${interval} herhaald tot ${untilDate}',
            repeatBookingDeleteLabel: 'U bent niet langer toegewezen aan ${jobName} vanaf ${startDate} - ${endDate} ${timeAllocation}. Deze boeking werd elke ${interval} herhaald tot ${untilDate}',
            loadingSectionSuffix: '% van capaciteit resource',
            timeSectionSuffix: 'totaal aantal geboekte uren',
            hoursPerDaySuffix: 'uren per dag',
            resourceSkillExpiryLabel: 'NL_You have skills set to expire in ${expiryDay} days ⏳ Renew it before the expiry date to keep your credentials up to date_NL',
            resourceManagerSkillExpiryLabel: 'NL_${resourceName} has skills set to expire in ${expiryDay} days ⏳ Ensure they renew it before the expiry date to keep your credentials up to date_NL',
            resourceSkillRecommendationLabel: 'NL_Update your skills and get noticed! Here are some skills you could add to your profile_NL',
            skillExpiryLabel: 'NL_${skillName} ${entityType} expires on ${expiryDate}_NL',
            extraSkillExpiryLabel: 'NL_${count} more expiring soon_NL'
        },
        notificationActions: {
            viewBookingLabel: 'Weergeven ${bookingSingularLowerAlias}',
            editBookingLabel: 'Bewerken ${bookingSingularLowerAlias}',
            viewPlansLabel: 'Plannen weergeven',
            viewRoleGroupLabel: 'Weergeven in ${entitySingularLower}',
            makeLiveLabel: 'Live gaan',
            markAsReadLabel: 'Markeren als gelezen',
            markAsUnreadLabel: 'Markeren als ongelezen',
            deleteLabel: 'Verwijderen'
        },
        notificationHistoryPageHeader: {
            informationMessage: 'Alle meldingen worden weergegeven. Gelezen meldingen worden na 30 dagen automatisch verwijderd',
            markAllReadLabel: 'Alles markeren als gelezen',
            loadMoreButtonLabel: 'Meer tonen'
        },
        notificationTabs: {
            notificationHistoryLabel: 'Geschiedenis',
            notificationSettingsLabel: 'Instellingen'
        },
        newNotificationTabs: {
            notificationHistoryLabel: 'NL_All_NL',
            notificationRequestLabel:'NL_Requests_NL',
            notificationSettingsLabel: 'Instellingen'
        },
        notificationsSettings: {
            pageTitle: 'Meldingen',
            notificationSettingsHeader: 'Standaardmeldingen',
            notificationsSettingsAdminPageDescription: 'Stel de instellingen voor standaardmeldingen in voor nieuwe gebruikers.\n\nGebruiker kan deze instellingen naar behoefte overschrijven in Meldingen > Instellingen.',
            notificationsSettingsDescription: 'Kies waar u over op de hoogte wilt worden gehouden en hoe',
            bookingNotifications: '${bookingSingularAlias} meldingen',
            allNotifications: 'Alle meldingen',
            bookingPost: 'Wanneer u toegewezen bent aan een ${bookingSingularAlias}',
            bookingPatch: 'Wanneer een aan u toegewezen ${bookingSingularAlias} is bijgewerkt',
            bookingDelete: 'Wanneer uw toewijzing aan een ${bookingSingularAlias} is ingetrokken of als het is verwijderd',
            emailFrequency: 'E-mailfrequentie',
            roleNotificaitons: '${rolerequestSingularAlias} meldingen',
            roleRequestCreate: 'Wanneer een ${resourceSingularAlias} die u beheert wordt aangevraagd',
            roleRequestReject: 'Wanneer een ${rolerequestSingularAlias} die u heeft aangemaakt wordt afgewezen',
            roleRequestLive: 'Wanneer een ${rolerequestSingularAlias} die u heeft aangemaakt live gaat',
            webApp: 'Web app',
            email: 'E-mailadres',
            globalNotificationSettingsHeaderTitle: 'Algemene meldingsinstellingen',
            globalNotificationSettingsHeaderDescription: 'Beheer meldingsinstellingen voor alle gebruikers',
            globalNotificationSettingsToggleTitle: 'Breng gebruikers op de hoogte van \n\nonbevestigde boekingen',
            globalNotificationSettingsToggleDescription: 'Voeg onbevestigde boekingen toe aan boekingsmeldingen',
            resourceSkillNotifications: 'NL_Skill notifications_NL',
            resourceSkillExpiry: 'NL_When a skill you have is expiring_NL',
            resourceManagerSkillExpiry: 'NL_When a resource you manage has a skill that\'s expiring_NL',
            resourceSkillRecommendation: 'NL_Recommend skills to add to your profile based on your job title or primary skill_NL'
        }
    },

    //Timesheets Page
    timesheetsPage: {
        noTimesheetsAvailable: 'U heeft geen opgeslagen uren',
        jobLabel: 'Taak',
        monday: 'Maandag',
        tuesday: 'Dinsdag',
        wednesday: 'Woensdag',
        thursday: 'Donderdag',
        friday: 'Vrijdag',
        saturday: 'Zaterdag',
        sunday: 'Zondag',
        total: 'Totaal',
        hours_logged: 'opgeslagen uren',
        commandBarConfig: {
            pageTitle: 'Urenstaten'
        },
        hoursRequiredLabel: 'Dit veld is verplicht',
        maximumHourErrorLabel: 'Maximaal 24',
        minimumHourErrorLabel: 'Minimaal 0',
        showTimesheetsLabel: 'Timesheet weergeven van',
        submitButtonLabel: 'Opslaan',
        cancelButtonLabel: 'Annuleren',
        timesheetDeletionWarning: 'Taak verwijderen?',
        timesheetDataDeletionMessage: 'Wilt u <bold>${jobDescription}</bold> en de bijbehorende uren van deze timesheet verwijderen?',
        timesheetCheckMessage: 'Ik begrijp de impact van het verwijderen van deze timesheetgegevens',
        deleteTimesheetPrimaryButton: 'Ja, verwijder de taak',
        deleteTimesheetSecondaryButton: 'Nee, behoud de taak',
        addJobButtonLabel: 'Taak toevoegen',
        applyJobButtonLabel: 'Toevoegen',
        hoursAriaLabel: 'Uren invoeren'
    },

    //Reporting settings Page
    reportingTitle: 'Rapportage',
    reportSettingPage: {
        datasetRefreshModalTitle: 'NL_Dataset refresh status_NL',
        lastRunLabel: 'NL_Last Run_NL',
        successStatus: 'NL_Dataset refresh completed successfully_NL',
        failureStatus: 'NL_Dataset refresh failed_NL',
        inProgressStatus: 'NL_Dataset refresh is in progress..._NL',
        cancelledStatus: 'NL_Dataset refresh was cancelled_NL',
        unknownStatus: 'NL_Unknown status_NL',
        datasetRefreshButton: 'NL_Refresh dataset_NL',
        cancelButton: 'NL_Cancel_NL'
    },

    //Report Page
    reportPage: {
        commandBarConfig: {
            pageTitle: 'Rapporten'
        },
        pageTitle: 'Rapporten',
        editLabel: 'Bewerken',
        viewLabel: 'Weergeven',
        printLabel: 'Afdrukken',
        reportsIHaveCreatedLabel: 'Door mij aangemaakte rapporten',
        sharedReportsItemLabel: 'Met mij gedeelde rapporten',
        manageMyReportsItemLabel: 'Mijn rapporten beheren...',
        reportNoEditPermissions: 'Onvoldoende machtigingen om dit rapport te bewerken',
        emptyReportTitle: 'Geen rapporten gevonden',
        emptyReportPageText: 'Er zijn rapporten voor u om te bekijken',
        sharedReportsSectionTitle: 'Gedeelde rapporten die ik kan bewerken',
        reportDetailsTitle: 'Rapportgegevens',
        systemInfoTitle: 'Systeeminformatie',
        updatedByLabel: 'Bijgewerkt door',
        createdByLabel: 'Aangemaakt door',
        createdOnLabel: 'Aangemaakt op',
        updatedOnLabel: 'Bijgewerkt op',
        nameLabel: 'Naam',
        reportAccessLabel: 'Toegang tot rapport',
        emptyReportDetailsText: 'Begin rapporten aan te maken om ze in dit venster weer te geven.',
        modalHeaderTitle: 'Mijn rapporten beheren',
        newReportButtonLabel: 'Nieuw rapport',
        newReportButtonTooltip: 'Maximaal ${licenseCount} rapporten toegestaan per tenant. Wellicht kun t u niet alle rapporten zien zonder lees-toegang.',
        deleteButtonLabel: 'Geselecteerd rapport verwijderen',
        saveChangesButtonLabel: 'Wijzigingen opslaan',
        discardButtonLabel: 'Wijzigingen annuleren',
        cancelButtonLabel: 'Annuleren',
        pendingDeleteButtonLabel: 'Verwijderen...',
        pendingSaveButtonLabel: 'Opslaan...',
        unsavedChangesLabel: 'Niet-opgeslagen wijzigingen',
        youHaveUnsavedChangesLine: 'U heeft niet-opgeslagen wijzigingen voor dit rapport. Wilt u nog steeds afsluiten?',
        allProfilesLabel: 'Alle profielen',
        readOnlyLabel: 'Alleen-lezen',
        editAccessLabel: 'Toegang bewerken',
        helpTextLabel: 'Makers kunnen hun rapporten altijd bekijken en bewerken',
        maxLimitReachedLabel: 'Maximaal 20 ${resourcePluralLowerAlias} of beveiligingsprofielen',
        reportAccessEntityPicker: {
            searchLabel: 'Zoeken',
            applyButtonText: 'Toepassen',
            notLoadedLabel: 'Niet geladen',
            notLoadedValueLabel: 'Waarde niet geladen ',
            showMoreButtonText: 'Meer tonen'
        },
        deleteReportPrompt: {
            title: '\'${reportName}\' rapport verwijderen?',
            confirmDeleteLabel: 'Ja, rapport verwijderen',
            declineDeleteLabel: 'Nee, rapport behouden',
            createdByLine: 'Dit rapport is aangemaakt door ${createdBy}.',
            permanentlyDeleteLine: 'Wilt u dit rapport permanent verwijderen?',
            youLabel: 'u'
        },
        toasterMessages: {
            create: 'Rapport aangemaakt',
            delete: 'Rapport verwijderd',
            edit: 'Rapport bewerkt',
            save: 'Rapport opgeslagen'
        },
        selectLabel: 'Selecteren',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        securityProfilesLabel: 'Beveiligingsprofielen',
        emptyNameError: 'Voer een naam in',
        duplicateNameError: 'Deze naam wordt al gebruikt. Voer een unieke naam in.'
    },

    //Callback component
    callbackComponentText: 'Doorsturen...',

    // Planner/Jobs page
    plannerPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            pageTitle: 'Planner',
            addLabel: 'Toevoegen',
            editLabel: 'Bewerken',
            editRoleByNameButtonLabel: '${rolerequestSingularCapitalAlias} op naam bewerken',
            editRoleByCriteriaButtonLabel: '${rolerequestSingularCapitalAlias} op vereisten bewerken',
            viewLabel: 'Weergeven',
            barsLabel: 'Balkopties',
            showLabel: 'Tonen',
            increaseDateRangeLabel: 'Datumbereik vergroten',
            decreaseDateRangeLabel: 'Datumbereik verkleinen',
            dateRangeLabel: 'Datumbereik',
            goToTodayLabel: 'Ga naar vandaag',
            goToDateLabel: 'Ga naar datum',
            jobsLabel: 'Taken',
            resourcesLabel: 'Resources',
            filtersLabel: 'Filters',
            bookingLabel: 'Boeking',
            roleLabel: 'Rol',
            jobLabel: 'Taak',
            clientLabel: 'Klant',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} van een sjabloon',
            cutLabel: 'Knippen',
            copyLabel: 'Kopiëren',
            pasteLabel: 'Plakken',
            restartLabel: 'Opnieuw starten',
            archiveLabel: 'Archiveren',
            rejectLabel: 'Afwijzen',
            makeLiveLabel: 'Live gaan',
            submitRequestLabel: 'Aanvraag indienen',
            createLabel: 'Aanmaken',
            deleteLabel: 'Verwijderen',
            manageLabel: 'Beheren',
            manageRoleTemplatesLabel: '${rolerequestSingularLowerAlias} sjablonen beheren',
            moreLabel: 'Meer',
            newWorkspaceLabel: 'NL_New Workspace_NL',
            saveAsNewWorkspaceLabel: 'NL_Save as a new workspace_NL',
            manageMyWorkspacesLabel: 'NL_Manage My Workspaces_NL',
            privateWorkspacesLabel: 'NL_Private Workspaces_NL',
            publicWorkspacesLabel: 'NL_Public Workspaces_NL',
            noPublicWorkspacesCreatedLabel: 'NL_No public workspaces have been created_NL',
            noPrivateWorkspacesCreatedLabel: 'NL_No private workspaces have been created_NL',
            newPlanLabel: 'Nieuw plan',
            saveAsNewPlanLabel: 'Opslaan als nieuw plan',
            manageMyPlansLabel: 'Mijn plannen beheren',
            privatePlansLabel: 'Persoonlijk plan',
            publicPlansLabel: 'Openbaar plan',
            saveChangesLabel: 'Wijzigingen opslaan',
            saveChangesToPublicLabel: 'Wijzigingen opslaan naar openbaar',
            noPublicPlansCreatedLabel: 'Er zijn geen openbare plannen aangemaakt',
            noPrivatePlansCreatedLabel: 'Er zijn geen persoonlijke plannen aangemaakt',
            noRoleTemplatesCreatedLabel: 'Er zijn geen sjablonen toegevoegd',
            customDateRangeLabel: 'Aangepast datumbereik',
            dayLabel: 'Dag',
            '5daysLabel': '5 dagen',
            '7daysLabel': '7 dagen',
            '10daysLabel': '10 dagen',
            weekLabel: 'Week',
            '2weeksLabel': '2 weken',
            '4weeksLabel': '4 weken',
            '6weeksLabel': '6 weken',
            monthLabel: 'Maand',
            '2monthsLabel': '2 maanden',
            '3monthsLabel': '3 maanden',
            '6monthsLabel': '6 maanden',
            yearLabel: 'Jaar',
            weekendsLabel: 'Weekenden',
            potentialConflicts: 'Potentiële conflicten tonen',
            baseFilterLabel: 'Taken weergeven',
            rollForwardLabel: 'Dupliceren',
            rollForwardTooltipText: 'Kopieer de geselecteerde ${bookingEntityAlias} naar een andere ${jobEntityAlias} of datum',
            byNameSuffix: 'op naam',
            byRequirementSuffix: 'op vereisten',
            findResourcesLabel: 'Vind ${resourceEntityAlias}...',
            findResourceToolTipText: 'Vind ${resourceEntityAlias} gebaseerd op criteria (f)',
            showMenuTooltipText: 'Niet-toegewezen ${rolePluralLowerCase} worden nu standaard verborgen. Gebruik het menu \'Tonen\' om dit te wijzigen.',
            showInViewLabel: 'Tonen in ${pluralViewNameAlias} weergave',
            restorePlansLabel: 'Weergave resetten',
            restorePlanTooltipText: 'Reset de huidige weergave naar de oorspronkelijke status. Gebruik het vervolgkeuzemenu rechts om deze weergave als plan op te slaan.'
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: 'Plakken ${subRowEntityAlias}',
            hideFutureEntityLabel: 'Toekomst ${subRowEntityAlias}',
            hideUnassignedRowsEntityLabel: 'Niet-toegewezen rijen',
            hideUnassignedBookingsEntityLabel: 'NL_Unassigned ${bookingPluralLowerCase}_NL',
            hideRolesLabel: '${rolePluralCapitalized}',
            hideInactiveEntityLabel: 'Inactief ${resourcePluralCapitalized}',
            hidePastEntitiesExplanation: 'Toon ${subRowEntityAlias} waarvoor er alleen ${bookingPluralLowerCase} zijn die vandaag of eerder eindigen',
            hideFutureEntitiesExplanation: 'Toon ${subRowEntityAlias} waarvoor er alleen ${bookingPluralLowerCase} zijn die beginnen na het einde van het zichtbare datumbereik',
            hideRolesExplanation: 'Toon ${subRowEntityAlias} waarvoor er ${rolePluralLowerCase}',
            hideDraftRolesExplanation: 'Toon ${roleSingularCapitalized} concepten',
            hideRequestedRolesExplanation: 'Toon ${rolePluralCapitalized} aanvragen die live kunnen gaan ${bookingPluralLowerCase}',
            toggleShowUnassignedRoles: 'Niet-toegewezen ${rolePluralLowerCase}',
            toggleShowRolesByName: '${rolePluralCapitalized} op naam',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} op vereisten',
            hideJobTimelineToggleLabel: '${jobEntityAlias} tijdlijn',
            hideJobMilestonesToggleLabel: '${jobEntityAlias} mijlpalen',
            hideJobTimelineExplanation: 'Begin- en einddatums ${jobEntityAlias} tonen',
            hideJobMilestonesExplanation: 'Specifieke datums ${jobEntityAlias} tonen'
        },
        selectionBar: {
            editAllButtonLabel: 'Bewerken',
            deleteAllButtonLabel: 'Verwijderen',
            editButtonLabel: 'Bewerken',
            editRoleByNameButtonLabel: '${rolerequestSingularCapitalAlias} op naam bewerken',
            editRoleByCriteriaButtonLabel: '${rolerequestSingularCapitalAlias} op vereisten bewerken',
            deleteButtonLabel: 'Verwijderen',
            archiveAllButtonLabel: 'Archiveren',
            archiveButtonLabel: 'Archiveren',
            restartAllButtonLabel: 'Opnieuw starten',
            restartButtonLabel: 'Opnieuw starten',
            submitRequestAllButtonLabel: 'Aanvraag indienen',
            submitRequestButtonLabel: 'Aanvraag indienen',
            createButtonLabel: 'Aanmaken',
            makeLiveSingularButtonLabel: 'Live gaan',
            makeLivePluralButtonLabel: 'Live gaan',
            insufficientRightsToEditAndDelete: 'Onvoldoende machtigingen om deze te bewerken/verwijderen ${entityAlias}',
            insufficientActionRights: 'Onvoldoende machtigingen om de actie voor alles uit te voeren ${entityAlias}',
            selectedLabel: 'Geselecteerd',
            maxBookingsSuffix: 'max.',
            rollForwardLabel: 'Dupliceren',
            rollForwardTooltipText: 'Kopieer de geselecteerde ${bookingEntityAlias} naar een andere ${jobEntityAlias} of datum',
            showInView: 'Tonen in ${pluralViewNameAlias} weergave'
        },
        barOptions: {
            defaultLabel: 'Standaard',
            mediumLabel: 'Gemiddeld',
            expandedLabel: 'Samengevouwen'
        },
        showLabelsOnBarsLabel: 'Label op balken tonen',
        legendLabel: 'Legenda',
        barFieldsLabel: '${barSingularAlias} balkvelden',
        colourSchemeLabel: 'Kleurthema',
        customColourThemeLabel: 'Aangepast kleurthema',
        customColourSchemeLabel: 'Aangepast ${barSingularAlias} kleurthema',
        editedSuffix: 'bewerkt',
        createdSuffix: 'aangemaakt',
        deletedSuffix: 'verwijderd',
        archivedSuffix: 'gearchiveerd',
        restartedSuffix: 'opnieuw gestart',
        rejectedSuffix: 'afgewezen',
        requestedSuffix: 'aangevraagd',
        liveSuffix: 'ingesteld op',
        publishedRoleSuffix: 'gepubliceerd op ${marketplaceAlias}',
        scheduleRoleForPublishingSuffix: 'gepland voor publicatie op ${marketplaceAlias}',
        publicationEditedSuffix: 'publicatie bijgewerkt',
        publicationRemovedSuffix: 'verwijderd uit ${marketplaceAlias}',
        applyButtonText: 'Toepassen',
        searchLabel: 'Zoeken',
        notLoadedLabel: 'niet geladen',
        notLoadedValueLabel: 'waarde niet geladen',
        goToPageLabel: 'Ga naar pagina',
        legend: {
            legendTitle: 'Legenda',
            coloursColumnSubTitle: 'Balkkleuren zijn gebaseerd op de',
            barTypes: {
                draftRoles: '${rolerequestSingularCapitalAlias} concepten',
                roleRequestsToLiveBookings: '${rolerequestSingularCapitalAlias} aanvragen die live kunnen gaan ${bookingPluralLowerAlias}',
                unconfirmed: 'Onbevestigd',
                planned: 'Gepland',
                excludesNonWorkingDays: 'Sluit vrije dagen uit',
                includesNonWorkingDays: 'Voegt vrije dagen toe',
                inConflict: 'In conflict',
                startDateNonWorking: 'Begindatum op verborgen weekend',
                endDateNonWorking: 'Einddatum op verborgen weekend',
                bothDatesNonWorking: 'Begin- en einddatums op verborgen weekend',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} aanvragen die live zijn gegaan ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Kleurthema',
                barTypesTabTitle: 'Balktypen',
                milestonesTabTitle: '${jobSingularAlias} details'
            },
            jobDetailsLabels: {
                milestonesColumnTitle: 'Mijlpalen',
                timelineColumnTitle: 'Tijdlijn',
                linesColumnTitle: 'Regels',
                normalLineLabel: '${jobSingularAliasLinesSection} met begin- en einddatums',
                dashedLineLabel: '${jobSingularAliasLinesSection} met ontbrekende begin- en/of einddatums',
                lineEndsColumnTitle: 'Regeleinden',
                onscreenDatesLabel: 'Begin-/einddatum ${jobSingularAliasLineEndsSection} op het scherm',
                offscreenDatesLabel: 'Begin-/einddatum ${jobSingularAliasLineEndsSection} niet op het scherm',
                statesColumnTitle: 'Statussen',
                incompletedStateLabel: 'Onvoltooid',
                completedStateLabel: 'Voltooid',
                overduedStateLabel: 'Te laat'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'Geen kleurregels toegevoegd voor'
        },
        plans: {
            manageMyPlansLabel: 'Mijn plannen beheren',
            newPlanLabel: 'Nieuw plan',
            privatePlansColumnTitle: 'Mijn plannen',
            copyPlanLabel: 'Plan kopiëren',
            readOnlyLabel: 'Alleen-lezen',
            editAccessLabel: 'Toegang bewerken',
            renameLabel: 'Naam wijzigen',
            deleteLabel: 'Verwijderen',
            moveToPublicLabel: 'Verplaatsen naar openbaar',
            makeCopyLabel: 'Een kopie maken',
            makePublicCopyLabel: 'Een openbare kopie maken',
            makePrivateCopyLabel: 'Een persoonlijke kopie maken',
            moveToPrivateLabel: 'Verplaatsen naar persoonlijk',
            privatePlansLabel: 'Persoonlijk plan',
            publicPlansLabel: 'Openbaar plan',
            manageMyWorkspacesLabel: 'NL_Manage My Workspaces_NL',
            newWorkspaceLabel: 'NL_New Workspace_NL',
            privateWorkspacesColumnTitle: 'NL_My Workspaces_NL',
            privateWorkspacesLabel: 'NL_Private Workspaces_NL',
            publicWorkspacesLabel: 'NL_Public Workspaces_NL',
            copyWorkspaceLabel: 'NL_Copy workspace_NL',
            editWorkspaceLabel: 'NL_Edit workspace_NL'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'Boeken ${entityAlias}',
            addBookingToJobRecordListCaption: 'Boeken op ${entityAlias}',
            searchLabel: 'Zoeken',
            sortLabel: 'Rangschikken',
            sortyByLabel: 'Rangschikken op',
            columnsLabel: 'Kolommen',
            applyButtonText: 'Toepassen',
            detailsLabel: 'details',
            notLoadedLabel: 'niet geladen',
            notLoadedValueLabel: 'waarde niet geladen',
            historyFieldPlaceholder: 'Niet-gespecificeerd',
            pastLabel: 'plakken',
            lastLoginLabel: 'Laatste keer ingelogd',
            expandAndCollapseText: 'Rij samenvouwen en uitvouwen',
            expandAllCaption: 'Alles samenvouwen',
            collapseAllCaption: 'Alles uitvouwen',
            sortLabelButton: 'Rangschikken ${order}',
            resourcesLabel: 'NL_resources_NL',
            jobsLabel: 'NL_jobs_NL',
            calculatingSortCalcFields: 'NL_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_NL',
            numberResults: 'NL_${rowCount} results_NL',
            resourceLabel: 'NL_Resource_NL',
            jobLabel: 'NL_Job_NL'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Wijziging toewijzing',
            outsideJobDatesLabel: 'Buiten taakdatums',
            datesConflictWithBookingLabel: 'Datums conflicteren met boeking',
            bookingConflictLabel: 'Conflict met boeking',
            inactiveResourceLabel: ' is inactief. Stel in op een actieve of niet-toegewezen resource om wijzigingen op te slaan',
            fromLabel: 'Van',
            toLabel: 'Tot',
            noDiaryAssignmentLabel: 'Geen agenda toegewezen',
            selectMultipleBarsHintPrefix: '+ klik',
            selectMultipleBarsHint: 'om meerdere te selecteren',
            splitBookingBarHintPrefix: 'Houd S vast',
            splitBookingBar: 'om te splitsen ${bookingsPluralLowerEntity}'
        },
        dateBar: {
            customDateRangeLabel: 'Aangepast datumbereik',
            goToLabel: 'Ga naar',
            todayLabel: 'Vandaag',
            dayLabel: 'Dag',
            '5daysLabel': '5 dagen',
            '7daysLabel': '7 dagen',
            '10daysLabel': '10 dagen',
            weekLabel: 'Week',
            '2weeksLabel': '2 weken',
            '4weeksLabel': '4 weken',
            '6weeksLabel': '6 weken',
            monthLabel: 'Maand',
            '2monthsLabel': '2 maanden',
            '3monthsLabel': '3 maanden',
            '6monthsLabel': '6 maanden',
            yearLabel: 'Jaar',
            customLabel: 'Aangepast',
            weekendsLabel: 'Weekenden',
            prevLabel: 'Vorige',
            nextLabel: 'Volgende'
        },
        multiSelectionTooltip: {
            selectionTooltip: '${entityCount} ${entityAlias} Geselecteerd',
            mixedSelectionTooltip: '${bookingsCount} ${bookingAlias} en ${rolesCount} ${rolerequestAlias} geselecteerd'
        },
        multiSelectionAlert: {
            message: 'Limiet bereikt',
            description: 'U heeft de selectielimiet van ${maximumItemsCount} items bereikt'
        }
    },
    jobsPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Toevoegen',
            jobsLabel: 'Taken',
            filtersLabel: 'Filters',
            manageLabel: 'Beheren',
            editLabel: 'Bewerken',
            jobLabel: 'Taak',
            duplicateLabel: 'Dupliceren',
            clientLabel: 'Klant',
            editDetailsLabel: 'Details bewerken',
            baseFilterLabel: 'Weergeven',
            viewAllJobsLabel: 'Alles',
            viewJobsIManageLabel: 'Ik beheer',
            viewJobsActionRequiredLabel: 'Actie vereist',
            staticMessageAddJobsMenu: 'NL_Resources can be added via User management_NL'
        }
    },
    resourcesPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Toevoegen',
            jobsLabel: 'Taken',
            filtersLabel: 'Filters',
            manageLabel: 'Beheren',
            editLabel: 'Bewerken',
            jobLabel: 'Taak',
            duplicateLabel: 'Dupliceren',
            clientLabel: 'Klant',
            editDetailsLabel: 'Details bewerken',
            baseFilterLabel: 'Weergeven',
            viewAllJobsLabel: 'Alles',
            viewJobsIManageLabel: 'Ik beheer',
            viewJobsActionRequiredLabel: 'Actie vereist'
        }
    },
    roleInboxPage: {
        commandBarConfig: {
            scenarioLabel: '${rolerequestgroupSingularCapitalAlias}',
            addLabel: 'Toevoegen',
            editLabel: 'Bewerken',
            editRoleByNameButtonLabel: '${rolerequestSingularCapitalAlias} op naam bewerken',
            editRoleByCriteriaButtonLabel: '${rolerequestSingularCapitalAlias} op vereisten bewerken',
            deleteLabel: 'Verwijderen',
            filtersLabel: 'Filters',
            rolesLabel: 'Rollen',
            showLabel: 'Tonen',
            archiveLabel: 'Archiveren',
            restartLabel: 'Opnieuw starten',
            rejectLabel: 'Afwijzen',
            rolefromTemplateLabel: '${rolerequestSingularCapitalAlias} van een sjabloon',
            noRoleTemplatesCreatedLabel: 'Er zijn geen sjablonen toegevoegd',
            createLabel: 'Aanmaken',
            makeLiveLabel: 'Live gaan',
            submitRequestLabel: 'Aanvraag indienen',
            byNameSuffix: 'op naam',
            byRequirementSuffix: 'op vereisten',
            unassignFromRoleLabel: 'Niet-toegewezen formulier ',
            toggleShowUnassignedRoles: 'Niet-toegewezen ${rolePluralLowerCase}',
            toggleShowRolesByName: '${rolePluralCapitalized} op naam',
            toggleShowRolesByRequirements: '${rolePluralCapitalized} op vereisten',
            manageRoleTemplatesLabel: '${rolerequestSingularLowerAlias} sjablonen beheren',
            publishToMarketplaceLabel: 'Publiceren op ${marketplaceAlias}',
            editRolePublicationButtonLabel: '${rolerequestSingularLowerAlias} publicatie bewerken',
            removeRolePublicationButtonLabel: '${rolerequestSingularLowerAlias} publicatie verwijderen'
        }
    },
    marketplacePage: {
        roleCard: {
            startsOnLabel: '${rolerequestSingularCapitalAlias} start op',
            categoryLabel: 'Categorie',
            availabilityLabel: 'Uw beschikbaarheid voor die ${rolerequestSingularLowerAlias} is',
            numberOfResources: 'Aantal ${resourcePluralLowerAlias}',
            numberOfFte: 'FTE’s',
            systemDetails: 'Gepubliceerd op ${publishedOn} (bijgewerkt op ${updatedOn})',
            pendingResourcesNeededText: '${pendingResources} vereist',
            defaultRoleName: 'Nieuwe rol',
            notAvailableLabel: 'Niet beschikbaar'
        },
        commandBarConfig: {
            allLabel: 'Alles',
            filtersLabel: 'Filters',
            marketplaceLabel: 'Rollenbord',
            appliedAllLabel: 'Alles',
            appliedToLabel: 'Ik heb een aanvraag ingediend voor',
            availableForLabel: 'Ik ben beschikbaar voor'
        },
        entityWindow: {
            roleApplicationSubmitted: 'Aanvraag ingediend',
            roleApplicationWithdrawn: 'Aanvraag is ingetrokken'
        }
    },
    previewEntityPage: {
        sharePopoverTitle: 'Dit delen ${roleAlias}'
    },
    tableViewPage: {
        commandBarConfig: {
            pageTitle: 'Tabelweergave',
            addLabel: 'Toevoegen',
            editLabel: 'Bewerken',
            viewLabel: 'Weergeven',
            showLabel: 'Tonen',
            increaseDateRangeLabel: 'Datumbereik vergroten',
            decreaseDateRangeLabel: 'Datumbereik verkleinen',
            dateRangeLabel: 'Datumbereik',
            goToTodayLabel: 'Ga naar vandaag',
            goToDateLabel: 'Ga naar datum',
            jobsLabel: 'Taken',
            resourcesLabel: 'Resources',
            filtersLabel: 'Filters',
            bookingLabel: 'Boeking',
            roleLabel: 'Rol',
            jobLabel: 'Taak',
            clientLabel: 'Klant',
            cutLabel: 'Knippen',
            copyLabel: 'Kopiëren',
            pasteLabel: 'Plakken',
            createLabel: 'Aanmaken',
            deleteLabel: 'Verwijderen',
            manageLabel: 'Beheren',
            moreLabel: 'Meer',
            newPlanLabel: 'Nieuw plan',
            saveAsNewPlanLabel: 'Opslaan als nieuw plan',
            manageMyPlansLabel: 'Mijn plannen beheren',
            privatePlansLabel: 'Persoonlijk plan',
            publicPlansLabel: 'Openbaar plan',
            saveChangesLabel: 'Wijzigingen opslaan',
            saveChangesToPublicLabel: 'Wijzigingen opslaan naar openbaar',
            noPublicPlansCreatedLabel: 'Er zijn geen openbare plannen aangemaakt',
            noPrivatePlansCreatedLabel: 'Er zijn geen persoonlijke plannen aangemaakt',
            customDateRangeLabel: 'Aangepast datumbereik',
            dayLabel: 'Dag',
            '5daysLabel': '5 dagen',
            '7daysLabel': '7 dagen',
            '10daysLabel': '10 dagen',
            weekLabel: 'Week',
            '2weeksLabel': '2 weken',
            '4weeksLabel': '4 weken',
            '6weeksLabel': '6 weken',
            monthLabel: 'Maand',
            '2monthsLabel': '2 maanden',
            '3monthsLabel': '3 maanden',
            '6monthsLabel': '6 maanden',
            yearLabel: 'Jaar',
            weekendsLabel: 'Weekenden',
            potentialConflicts: 'Potentiële conflicten tonen',
            baseFilterLabel: 'Taken weergeven',
            findResourcesLabel: 'Vind ${resourceEntityAlias}...',
            findResourceToolTipText: 'Vind ${resourceEntityAlias} gebaseerd op criteria (F)',
            showMenuTooltipText: '' // This is empty to prevent the Show menu tooltip from showing on tableview page as it is not needed for now.
        },
        hideShowBarOptions: {
            hideHistoricEntityLabel: 'Plakken ${subRowEntityAlias}',
            hideFutureEntityLabel: 'Toekomst ${subRowEntityAlias}',
            hideInactiveEntityLabel: 'Inactief ${resourcePluralCapitalized}',
            hideRolesLabel: '${rolePluralCapitalized}',
            hidePastEntitiesExplanation: 'Toon ${subRowEntityAlias} waarvoor er alleen ${bookingPluralLowerCase} zijn die vandaag of eerder eindigen',
            hideFutureEntitiesExplanation: 'Toon ${subRowEntityAlias} waarvoor er alleen ${bookingPluralLowerCase} zijn die beginnen na het einde van het zichtbare datumbereik'
        },
        selectionBar: {
            editAllButtonLabel: 'Bewerken',
            deleteAllButtonLabel: 'Verwijderen',
            editButtonLabel: 'Bewerken',
            deleteButtonLabel: 'Verwijderen',
            createButtonLabel: 'Aanmaken',
            makeLiveSingularButtonLabel: 'Live gaan',
            makeLivePluralButtonLabel: 'Alles live maken',
            insufficientRightsToEditAndDelete: 'Onvoldoende machtigingen om deze te bewerken/verwijderen ${entityAlias}',
            insufficientActionRights: 'Onvoldoende machtigingen om de actie voor alles uit te voeren ${entityAlias}',
            selectedLabel: 'geselecteerd',
            maxBookingsSuffix: 'max.'
        },
        legendLabel: 'Legenda',
        barFieldsLabel: '${barSingularAlias} balkvelden',
        colourSchemeLabel: 'Kleurthema',
        customColourThemeLabel: 'Aangepast kleurthema',
        customColourSchemeLabel: 'Aangepast ${barSingularAlias} kleurthema',
        editedSuffix: 'bewerkt',
        createdSuffix: 'aangemaakt',
        deletedSuffix: 'verwijderd',
        applyButtonText: 'Toepassen',
        searchLabel: 'Zoeken',
        notLoadedLabel: 'niet geladen',
        notLoadedValueLabel: 'waarde niet geladen',
        legend: {
            legendTitle: 'Legenda',
            coloursColumnSubTitle: 'Balkkleuren zijn gebaseerd op de',
            barTypes: {
                draftRoles: '${rolerequestSingularCapitalAlias} concepten',
                roleRequestsToLiveBookings: '${rolerequestSingularCapitalAlias} aanvragen die live kunnen gaan ${bookingPluralLowerAlias}',
                unconfirmed: 'Onbevestigd',
                planned: 'Gepland',
                excludesNonWorkingDays: 'Sluit vrije dagen uit',
                includesNonWorkingDays: 'Voegt vrije dagen toe',
                inConflict: 'In conflict',
                startDateNonWorking: 'Begindatum op verborgen weekend',
                endDateNonWorking: 'Einddatum op verborgen weekend',
                bothDatesNonWorking: 'Begin- en einddatums op verborgen weekend',
                roleRequestsOnLiveBookings: '${rolerequestSingularCapitalAlias} aanvragen die live zijn gegaan ${bookingPluralLowerAlias}'
            },
            tabsTitlesMap: {
                colourTypesTabTitle: 'Kleurthema',
                barTypesTabTitle: 'Balktypen',
                milestonesTabTitle: '${jobSingularAlias} details'
            },
            bookingSectionTitle: '${bookingPluralCapitalAlias}',
            rolesSectionTitle: '${rolerequestPluralCapitalAlias}',
            noColourRulesAlert: 'Geen kleurregels toegevoegd voor'
        },
        plans: {
            manageMyPlansLabel: 'Mijn plannen beheren',
            newPlanLabel: 'Nieuw plan',
            privatePlansColumnTitle: 'Mijn plannen',
            copyPlanLabel: 'Plan kopiëren',
            editPlanLabel: 'Plan bewerken',
            readOnlyLabel: 'Alleen-lezen',
            editAccessLabel: 'Toegang bewerken',
            renameLabel: 'Naam wijzigen',
            deleteLabel: 'Verwijderen',
            moveToPublicLabel: 'Verplaatsen naar openbaar',
            makeCopyLabel: 'Een kopie maken',
            makePublicCopyLabel: 'Een openbare kopie maken',
            makePrivateCopyLabel: 'Een persoonlijke kopie maken',
            moveToPrivateLabel: 'Verplaatsen naar persoonlijk',
            privatePlansLabel: 'Persoonlijk plan',
            publicPlansLabel: 'Openbaar plan'
        },
        recordsList: {
            addBookingToJobRecordListCaption: 'Boeken ${entityAlias}',
            addBookingToJobLabel: '${entityAlias} boeken naar ${jobName}',
            addBookingToResourceRecordListCaption: 'Boeken op een ${entityAlias}',
            addBookingToResourceLabel: 'Boeken op ${entityAlias} naar ${resourceName}',
            searchLabel: 'Zoeken',
            sortLabel: 'Rangschikken',
            sortyByLabel: 'Rangschikken op',
            columnsLabel: 'Kolommen',
            applyButtonText: 'Toepassen',
            detailsLabel: 'details',
            notLoadedLabel: 'niet geladen',
            notLoadedValueLabel: 'waarde niet geladen',
            historyFieldPlaceholder: 'Niet-gespecificeerd',
            pastLabel: 'plakken',
            lastLoginLabel: 'Laatste keer ingelogd',
            expandAndCollapseText: 'Rij samenvouwen en uitvouwen',
            expandAllCaption: 'Alles samenvouwen',
            collapseAllCaption: 'Alles uitvouwen',
            sortLabelButton: 'Rangschikken ${order}',
            resourcesLabel: 'NL_resources_NL',
            jobsLabel: 'NL_jobs_NL',
            calculatingSortCalcFields: 'NL_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_NL',
            numberResults: 'NL_${rowCount} results_NL',
            resourceLabel: 'NL_Resource_NL',
            jobLabel: 'NL_Job_NL'
        },
        tooltipContextualMenu: {
            assignmentChangeLabel: 'Wijziging toewijzing',
            outsideJobDatesLabel: 'Buiten taakdatums',
            datesConflictWithBookingLabel: 'Datums conflicteren met boeking',
            bookingConflictLabel: 'Conflict met boeking',
            inactiveResourceLabel: ' is inactief. Stel in op een actieve of niet-toegewezen resource om wijzigingen op te slaan',
            fromLabel: 'Van',
            toLabel: 'Tot',
            noDiaryAssignmentLabel: 'Geen agenda toegewezen',
            selectMultipleBarsHintPrefix: '+ klik',
            selectMultipleBarsHint: 'om meerdere te selecteren'
        },
        dateBar: {
            goToLabel: 'Ga naar datum',
            todayLabel: 'Vandaag',
            monthLabel: 'Maand',
            '1monthsLabel': '1 maanden',
            '2monthsLabel': '2 maanden',
            '3monthsLabel': '3 maanden',
            '4monthsLabel': '4 maanden'
        }
    },
    rolegroupListPage: {
        emptyStateBoldLabel: 'Geen scenario’s',
        emptyStateLabel: 'Scenario’s voor deze taak aanmaken en vergelijken',
        actionRequiredLabel: 'actie vereist',
        rolesLabel: 'rollen'
    },
    rolegroupDetailsPage: {
        emptyStateLabel: 'Uw lijst met ${rolerequestPluralLowerAlias} wordt hier getoond',
        resourceInactiveString: 'Stel in op een actieve ${resourceSingularLowerAlias} om door te gaan naar ${rolerequestSingularLowerAlias}.',
        addRoleText: 'Toevoegen ${rolerequestSingularLowerAlias}',
        addRoleByNameLabel: '${rolerequestSingularCapitalAlias} op naam',
        addRoleByRequirementsLabel: '${rolerequestSingularCapitalAlias} op vereisten',
        roleFromTemplateLabel: '${rolerequestSingularCapitalAlias} van een sjabloon',
        noRoleTemplatesCreatedLabel: 'Er zijn geen sjablonen toegevoegd',
        manageRoleTemplates: '${rolerequestSingularLowerAlias} sjablonen beheren',
        notLoadedLabel: 'Niet geladen',
        noValuesLoadedLabel: 'Waarde niet geladen',
        applyText: 'Toepassen',
        searchText: 'Zoeken',
        actionButtonLabel: 'Acties',
        viewDetails: 'Details weergeven',
        defaultRoleName: 'Nieuwe rol',
        noRoleGroupSetLabel: 'Geen ${rolerequestgroupSingularLowerAlias} ingesteld',
        noResourcesMeetCriteriaText: 'Probeer een aantal vereisten te wijzigen of verwijderen en vervolgens wijzigingen op te slaan.',
        noResourcesFoundAdditionalText: 'Het kan tot 24 uur duren voordat onlangs bijgewerkte vaardigheidsgegevens in suggesties worden getoond.',
        moveToButtonLabel: 'Verplaatsen naar...',
        noMatchesFoundTopText: 'Geen overeenkomsten gevonden',
        unsavedChangesToRoleText: 'Niet-opgeslagen wijzigingen naar ${rolerequestSingularLowerAlias}',
        suggestionsNotUpToDateText: 'De suggesties zijn niet up-to-date, sla wijzigingen op voordat er een ${resourcePluralLowerAlias} wordt voorgesteld.',
        assignResource: 'Toewijzen aan ${rolerequestSingularLowerAlias}',
        unassignResource: 'Niet-toegewezen formulier ${rolerequestSingularLowerAlias}',
        addToShortlist: 'Toevoegen aan shortlist',
        removeFromShortlist: 'Verwijderen van shortlist',
        shortlist: 'Shortlist',
        shortlisted: 'Op shortlist geplaatst',
        notShortListed: 'Niet op shortlist geplaatst',
        shortlistedBy: 'Op shortlist geplaatst door',
        maxShortlistReachedFirstRow: 'Beperkt tot',
        maxShortlistReachedSecondRow: '6 resources',
        hiddenSuggestionsTopText: 'Verborgen suggesties',
        hiddenSuggestionsText: 'De voorgestelde ${resourcePluralLowerAlias} zijn verborgen, omdat ze gebaseerd zijn op vereisten waar u onvoldoende voor bent gemachtigd om ze te bekijken.',
        tailMessage: 'Geen geschikte overeenkomst gevonden? Probeer een aantal rolvereisten te wijzigen of verwijderen.',
        editButtonLabel: 'Bewerken ${rolerequestSingularCapitalAlias}',
        multipleRolesSelectedTopText: 'Meerdere ${rolerequestPluralLowerAlias} geselecteerd',
        multipleRolesSelectedBodyText: 'Om voorgestelde ${resourcePluralLowerAlias} te bekijken, selecteert u maar één ${rolerequestSingularCapitalAlias} op vereisten.',
        saveAsTemplateLabel: 'Opslaan als sjabloon',
        potentialConflictsTooltip: 'Potentiële conflicten met bestaande {bookingSingularLowerAlias}',
        maxAllowedRolesLabel: 'Maximaal 100 ${rolerequestPluralLowerAlias}',
        commandBarConfig: {
            editLabel: 'Bewerken',
            duplicateLabel: 'Dupliceren',
            deleteLabel: 'Verwijderen'
        },
        recordsList: {
            addBookingToResourceRecordListCaption: 'Boeken ${entityAlias}',
            addBookingToJobRecordListCaption: 'Boeken op ${entityAlias}',
            searchLabel: 'Zoeken',
            sortLabel: 'Rangschikken',
            sortyByLabel: 'Rangschikken op',
            columnsLabel: 'Kolommen',
            applyButtonText: 'Toepassen',
            detailsLabel: 'details',
            notLoadedLabel: 'niet geladen',
            notLoadedValueLabel: 'waarde niet geladen',
            historyFieldPlaceholder: 'Niet-gespecificeerd',
            pastLabel: 'plakken',
            lastLoginLabel: 'Laatste keer ingelogd',
            expandAndCollapseText: 'Rij samenvouwen en uitvouwen',
            expandAllCaption: 'Alles samenvouwen',
            collapseAllCaption: 'Alles uitvouwen',
            sortLabelButton: 'Rangschikken ${order}',
            resourcesLabel: 'NL_resources_NL',
            jobsLabel: 'NL_jobs_NL',
            calculatingSortCalcFields: 'NL_Sorting by ${fieldAlias} can take longer if there are many ${entity} to calculate for_NL',
            numberResults: 'NL_${rowCount} results_NL',
            resourceLabel: 'NL_Resource_NL',
            jobLabel: 'NL_Job_NL'
        }
    },
    roleRequirementsSection: {
        addCriteriaButtonLabel: 'Vereisten toevoegen',
        resourceAttributesLabel: '${resourceSingularCapitalAlias} attributen',
        mustMeetCriteriaDescription: 'Moet voldoen aan',
        mustMeetCriteriaExplanation: '${resourcePluralCapitalAlias} die niet aan deze vereisten voldoen, worden niet voorgesteld',
        criteriaEmptyStateMessage: 'Geen vereisten toegevoegd',
        removeRequirementLabel: 'Verwijderen',
        resourceSkillsLabel: '${resourceSingularCapitalAlias} vaardigheden',
        searchLabel: 'Zoeken',
        applyButtonText: 'Toepassen',
        removeSkillsText: 'Vaardigheden wissen',
        removeFilterText: 'Vaardigheden verwijderen',
        levelText: 'Niveau',
        levelsText: 'Niveaus',
        anySkillsText: 'Willekeurig',
        oneOfSkillsText: 'Eén van...',
        saveSkillsText: 'Opslaan',
        cancelSkillsText: 'Annuleren'
    },
    entityWindow: {
        basicDetailsSectionTitle: 'Standaarddetails',
        requirementsSectionTitle: 'Vereisten',
        milestonesSectionTitle: 'Mijlpalen',
        cMeSectionTitle: 'C-me eigenschappen',
        workHistoryTitle: 'Recente werkzaamheden',
        workDetailsSectionTitle: 'Werkgegevens',
        budgetSectionTitle: 'Budget',
        budgetDetailsSectionTitle: 'Budgetdetails',
        timeAndFinancialsSectionTitle: 'NL_Time and financials_NL',
        revenueSectionTitle: 'NL_Revenue_NL',
        costsSectionTitle: 'NL_Costs_NL',
        profitSectionTitle: 'NL_Profit_NL',
        hoursSectionTitle: 'NL_Hours_NL',
        planningSectionTitle: 'Planning',
        previousRelatedJob: 'Vorige gerelateerde taak',
        nextRelatedJob: 'Volgende gerelateerde taak',
        contactSectionTitle: 'Contact',
        emplyomentDetailsSectionTitle: 'Werkdetails',
        skillsSectionTitle: 'Vaardigheden',
        systemInfoSectionTitle: 'Systeeminformatie',
        timeAllocationTitle: 'Toewijzing van tijd',
        projectHealthTitle: 'Gezondheid van het project',
        fixedTimeSectionSuffix: 'uren',
        loadingSectionSuffix: '% van capaciteit resource',
        timeSectionSuffix: 'totaal aantal geboekte uren',
        hoursInTotalSuffix: 'uren in totaal',
        hoursPerDaySuffix: 'uren per dag',
        FTESuffix: 'FTE',
        resourcesSuffix: '${resourcePluralLowerAlias}',
        nameLabel: 'Naam',
        updatedToLabel: 'bijgewerkt naar',
        fromLabel: 'van',
        numberOfResourcesPrefix: 'Aantal resources',
        chargeRateFieldsControlTitle: 'Factureringstarief',
        bookingResourceChargeRateLabel: 'Factureringstarief resource',
        bookingOverriddenChargeRateLabel: 'Ander factureringstarief gebruiken',
        bookingCustomChargeRateLabel: 'Aangepast tarief gebruiken',
        bookingRevenueRatesRowTitle: 'Opbrengsten',
        bookingCostRatesRowTitle: 'Kosten',
        bookingProfitRatesRowTitle: 'Winst',
        bookingViewModeChargeRatesTitle: 'Tarieven',
        bookingOwnResourceChargeModeLabel: 'Factureringstarief resource',
        bookingDifferentResourceChargeModeLabel: 'Ander factureringstarief',
        bookingCustomChargeModeLabel: 'Aangepast tarief',
        rolerequestDescriptionPlaceholder: 'bijvoorbeeld projectmanager',
        rolerequestOwnResourceChargeModeLabel: 'Factureringstarief resource',
        rolerequestDifferentResourceChargeModeLabel: 'Ander factureringstarief',
        rolerequestCustomChargeModeLabel: 'Aangepast tarief',
        rolerequestResourceChargeRateLabel: 'Factureringstarief resource',
        rolerequestOverriddenChargeRateLabel: 'Ander factureringstarief gebruiken',
        rolerequestCustomChargeRateLabel: 'Aangepast tarief gebruiken',
        rolerequestRevenueRatesRowTitle: 'Opbrengsten',
        rolerequestCostRatesRowTitle: 'Kosten',
        rolerequestProfitRatesRowTitle: 'Winst',
        roleByNameWindowTitle: '${rolerequestCapitalEntityAlias} op naam',
        manageRoleTemplatesWindowTitle: 'Mijn sjablonen beheren',
        roleTemplateWindowTitle: '${rolerequestCapitalEntityAlias} sjabloon',
        roleByRequirementWindowTitle: '${rolerequestCapitalEntityAlias} op vereisten',
        dateRangeLabel: 'Datumbereik',
        datesRequiredLabel: 'Datums vereist',
        nonWorkSectionFieldText: 'Vrije dagen toevoegen',
        bookingSectionTitle: 'Boeking',
        rolerequestSectionTitle: 'Vereisten',
        rolerequestGroupSectionTitle: 'Rol groep',
        jobSectionTitle: 'Taak',
        resourceSectionTitle: 'Resource',
        clientSectionTitle: 'Klant',
        bookingSectionTitlePlural: 'Boekingen',
        jobSectionTitlePlural: 'Taken',
        resourceSectionTitlePlural: 'Resources',
        clientSectionTitlePlural: 'Klanten',
        rolerequestSectionTitlePlural: 'Vereisten',
        rolerequestGroupSectionTitlePlural: 'Rol groepen',
        moreInfoButtonLabel: 'Meer informatie',
        duplicateLabel: 'Dupliceren',
        assignToRoleButtonLabel: 'Toewijzen aan',
        unassignFromRoleButtonLabel: 'Niet-toegewezen formulier',
        rejectButtonLabel: 'Afwijzen',
        restartButtonLabel: 'Opnieuw starten',
        archiveButtonLabel: 'Archiveren',
        editButtonLabel: 'Bewerken',
        applyButtonLabel: 'Toepassen',
        closeButtonLabel: 'Sluiten',
        withdrawButtonLabel: 'Applicatie intrekken',
        editRoleByNameButtonLabel: '${rolerequestSingularCapitalAlias} op naam bewerken',
        editRoleByCriteriaButtonLabel: '${rolerequestSingularCapitalAlias} op vereisten bewerken',
        createButtonLabel: 'Aanmaken',
        createEntityTitle: 'Aanmaken ${entityTitleAlias}',
        editEntityTitle: 'Bewerken ${entityTitleAlias}',
        makeLiveSingularButtonLabel: 'Live gaan',
        makeLivePluralButtonLabel: 'Alles live maken',
        submitRequestButtonLabel: 'Aanvraag indienen',
        addBookingLabel: 'Boeking toevoegen',
        cancelButtonLabel: 'Annuleren',
        saveChangesButtonLabel: 'Wijzigingen opslaan',
        saveAllButtonLabel: 'Alles opslaan',
        discardChangesButtonLabel: 'Wijzigingen annuleren',
        progressButtonLabel: 'Voortgang',
        deleteButtonLabel: 'Verwijderen',
        editAllButtonLabel: 'Alles bewerken',
        archiveAllButtonLabel: 'Alles archiveren',
        restartAllButtonLabel: 'Alles opnieuw starten',
        submitRequestAllButtonLabel: 'Aanvragen indienen',
        deleteAllButtonLabel: 'Alles verwijderen',
        newButtonLabel: 'Aanmaken',
        viewButtonLabel: 'Weergeven',
        compareButtonLabel: 'Vergelijken',
        createTemplateLabel: 'Sjabloon aanmaken',
        roleTemplateLabel: '${rolerequestSingularCapitalAlias} sjabloon',
        rolePublicationWindowTitle: '${rolerequestCapitalEntityAlias} publicatie',
        insufficientActionRights: 'Onvoldoende machtigingen om de actie voor alles uit te voeren',
        addNewRoleLabel: 'Een nieuwe toevoegen ${rolerequestSingularLowerAlias}',
        roleListBodyEmptyStateLabel: 'U kunt een ${resourceSingularLowerAlias} met naam toevoegen, of ${rolerequestSingularLowerAlias} vereisten invoeren om geschikte ... te vinden ${resourcePluralLowerAlias}',
        manageRoleTemplatesEmptyStateLabel: 'U heeft geen ${rolerequestSingularLowerAlias} sjablonen.',
        templateDetailsLabel: 'Sjabloongegevens',
        provideTemplateNameLabel: 'Voer een naam in voor uw sjabloon',
        maxLengthValidationMessage: 'Maximaal ${maxNameLength} symbolen toegestaan',
        renameLabel: 'Naam wijzigen',
        createdLabel: 'Aangemaakt',
        myTemplatesLabel: 'Mijn sjablonen',
        deleteMultipleBookinngsButtonLabel: 'Verwijderen',
        deleteMultipleRolerequestsButtonLabel: 'Verwijderen',
        bookingStatusFieldExplanation: 'Resource blijft beschikbaar in de geboekte tijd',
        tableViewBookingStatusFieldExplanation: 'Niet bevestigde ${bookingSingularLowerAlias} is zichtbaar op ${plannerPageAlias} pagina. ${resourceSingularCapitalAlias} blijft beschikbaar in geboekte tijd.',
        nonWorkSectionFieldExplanation: 'Extra uren worden geboekt op de vrije dagen.',
        jobIsConfidentialFieldExplanation: 'Vertrouwelijke taken kunnen alleen door mensen met toegang worden bekeken',
        rolerequestRolerequestGroupFieldExplanation: 'Dit leeg laten creëert een rol buiten een ${rolerequestgroupSingularLowerAlias}',
        rolerequestFTEFieldExplanation: '1 fulltime equivalent is ${referenceDiaryTime} uur per dag',
        resourceSectionFieldExplanation: 'Meerdere resources toevoegen creëert een ${bookingSingularLowerAlias} voor allemaal',
        jumpToSectionTitle: 'Ga naar',
        additionalSectionTitle: 'Extra gedeelte',
        additionalDetailsSectionTitle: 'Aanvullende details',
        commentsSectionTitle: 'Opmerkingen',
        roleGroupListSectionTitle: 'Rol groepen',
        detailsPaneTooltipText: 'Het detailpaneel geeft u informatie over het nieuwe item dat u heeft geselecteerd. De pictogrammen brengen u naar de taak-, resource- of boekingsinformatie.',
        detailsPaneTooltipTitle: 'Detailpaneel',
        attachmentsSectionTitle: 'Documenten',
        moreOptionsButtonLabel: 'Meer opties',
        bookingBudgetDetailsMessage: 'Budgetberekeningen gebruiken het factureringstarief dat geldig was op de eerste dag van de boeking.',
        entityCreatedSuffix: 'aangemaakt',
        entityDeletedSuffix: 'verwijderd',
        notFoundPrefix: 'waarde',
        notFoundSuffix: 'niet gevonden',
        roleMarketplaceCriteriaMatchExplanation: 'Aanvragers moeten aan de vereisten voldoen',
        rolerequestDiaryForEstimationLabel: 'Agenda voor schatting',
        selectChargeRateLabel: 'Een factureringstarief selecteren',
        customChargeRateLabel: 'Aangepast factureringstarief',
        estimatedBudgetLabel: 'Geschat budget',
        estimatesTabLabel: 'Schatting',
        assignedTotalsTabLabel: 'Toegewezen totalen',
        roleGroupCountLabel: '${roleGroupCount} ${rolerequestgroupPluralCapitalAlias}',
        messages: {
            bookingBudgetDetailsMessageText: 'Budgetberekeningen gebruiken het factureringstarief dat geldig was op de eerste dag van de ${bookingSingularLowerAlias}.',
            roleBudgetDetailsMessageText: 'Budgetberekeningen gebruiken het factureringstarief dat geldig was op de eerste dag van de ${rolerequestSingularLowerAlias}.',
            roleAssigneesTotalsDifferenceText: 'Werkelijke totalen kunnen verschillen als toegewezen personen verschillende agenda’s of factureringstarieven hebben voor de schatting.',
            bookingMultipleResourcesBudgetDetailsMessageText: `Specifieke budgettarieven voor elke \${bookingSingularLowerAlias} kunnen nadat ze zijn aangemaakt in hun details worden bekeken.
            Budgetberekeningen gebruiken het factureringstarief dat geldig was op de eerste dag van de \${bookingSingularLowerAlias}.`,
            roleResourceWarningText: 'Wijs een ${resourceSingularLowerAlias} toe om een ${bookingSingularLowerAlias} aan te vragen.',
            roleResourcesContainUnassigedWarningText: `\${bookingSingularCapitalAlias} aanvragen kunnen niet worden verwerkt met
            Niet-toegewezen \${resourcePluralLowerAlias}, annuleer het gedeelte 'Niet-toegewezen' \${resourceSingularLowerAlias}.`,
            criteriaRoleUnassignedResourceText: 'Budgetinformatie wordt berekend wanneer ${resourcePluralLowerAlias} zijn toegewezen aan de ${rolerequestSingularLowerAlias}.',
            requirementSectionInsufficientPermissionsText: 'Sommige vereisten zijn verborgen, omdat u niet voldoende bent gemachtigd.',
            rolerequestCriteriaDPSuggestionPaneMessageText: 'Wijs ${resourcePluralLowerAlias} toe aan de ${rolerequestSingularLowerAlias} via het {Suggestion pane}.',
            suggestionPaneButtonText: 'Suggestiepaneel',
            criteriaRoleAssignResourceText: 'Wijs ${resourcePluralLowerAlias} toe aan de ${rolerequestSingularLowerAlias} via het suggestiepaneel.',
            criteriaRoleAssignedResourceChangeMessageText: 'Wijzig de toewijzing aan de ${rolerequestSingularLowerAlias} via het suggestiepaneel.',
            criteriaRoleAssignResourceToCalculateBudgetText: 'Om het budget te berekenen, moet u ${resourcePluralLowerAlias} toewijzen aan de ${rolerequestSingularLowerAlias}.',
            criteriaRolePublishMessageText: 'De ${rolerequestSingularLowerAlias} post wordt automatisch beëindigd na de einddatum van ${rolerequestSingularLowerAlias} - ${endDate}',
            roleApplicationAppliedOnText: 'U heeft u aangemeld voor deze ${rolerequestSingularLowerAlias} op ${applyDate}.',
            bookingJobOverBudgetMessageText: 'Deze ${bookingSingularLowerAlias} zorgt dat deze ${jobSingularLowerAlias} buiten het budget valt.',
            bookingResourceOverBudgetMessageText: 'Deze ${resourceSingularLowerAlias} boeken zorgt dat deze ${jobSingularLowerAlias} buiten het budget valt.',
            bookingJobHoursOverBudgetMessageText: 'NL_This ${bookingSingularLowerAlias} will put this ${jobSingularLowerAlias}\'s Total hours at ${jobHoursPercentageBudget}% (${totalHoursOverBudget} hrs over) of its Budget hours_NL',
            resourceChargeRateAndDiaryWarningMessage: 'NL_We recommend modifying ${chargeRateAlias} and ${diaryGroupAlias} outside of work hours as recalculating time and financials can take multiple hours depending on how many resources and bookings this change affects (this may include past bookings). Users will experience longer load times while this is happening._NL'
        },
        financialInformationSectionTitle: 'Financiële informatie',
        schedulingSectionTitle: 'Planning',
        rolesSectionTitle: 'Rollen',
        resourceSummaryTitle: 'Overzicht',
        overlappingBookingsTitle: 'Overlappende boekingen en rollen',
        saveAsADraft: 'Opslaan als concept',
        backToSuggestionLabel: 'Terug naar suggesties',
        suggestLabel: 'Voorstellen',
        suggestedLabel: 'Voorgesteld',
        forLabel: 'voor',
        moveButtonLabel: 'Verplaatsen',
        moveToModalTitle: 'Verplaatsen naar...',
        searchForLabel: 'Zoek naar een',
        lastRefreshedText: 'Laatst vernieuwd',
        SelectionTitleLabel: 'Bulkupdate van alles ',
        SelectionDescriptionLabel: 'Waarden instellen of leeg laten om waarden te wissen',
        SelectionFieldsCaptionLabel: 'Velden',
        shortlistUptoSixText: 'U kunt tot 6 resources op de shortlist plaatsen.',
        manageBudgetLabel: 'Budget beheren',
        movePendingFTELabel: 'FTE’s in behandeling verplaatsen',
        removePendingFTELabel: 'FTE’s in behandeling verwijderen',
        movePendingResourcesLabel: 'Resources in behandeling verplaatsen',
        removePendingResourcesLabel: 'Resources in behandeling verwijderen',
        publishToMarketplaceLabel: 'Publiceren op ${pageAlias}',
        publishRoleLabel: 'Publiceren ${rolerequestSingularLowerAlias}',
        roleMarketplaceCategoryPlaceholder: 'Categorie toevoegen',
        saveAsTemplateLabel: 'Opslaan als sjabloon',
        editRolePublicationButtonLabel: '${rolerequestSingularLowerAlias} publicatie bewerken',
        removeRolePublicationButtonLabel: '${rolerequestSingularLowerAlias} publicatie verwijderen',
        emptyState: {
            noRoleGroupItemsCoincidenceMessage: 'Om ${rolerequestgroupSingularLowerAlias} details te bekijken, selecteert u ${rolerequestPluralLowerAlias} die van dezelfde ${rolerequestgroupSingularLowerAlias} zijn.',
            noRoleGroupItemsCoincidenceContent: 'Meerdere ${rolerequestgroupPluralLowerAlias} in selectie'
        },
        createScenarioLabel: 'Aanmaken ${rolerequestgroupSingularLowerAlias}',
        editScenarioLabel: 'Bewerken ${rolerequestgroupSingularLowerAlias}',
        openScenarioButtonLabel: 'Openen ${rolerequestgroupSingularCapitalAlias}',
        fieldValueCaption: {
            budget: 'NL_of budget_NL',
            target: 'NL_of target_NL'
        },
        reviewSettings: {
            headerTitle: 'NL_Review settings_NL',
            saveChangesButtonLabel: 'NL_Save changes_NL',
            cancelBtnLabel: 'NL_Cancel',
            resourceSkillsReviewLabel: 'NL_Resource skills reviews_NL',
            reviewerOnThisJobLabel: 'NL_Reviewer on this job_NL',
            eligibleForReviewLabel: 'NL_Eligible for reviews_NL',
            reviewerOnThisJobCaptionLabel: 'NL_Choose who can review resource skills on this job_NL',
            pastBookingLabel: 'NL_Only resources with past booking on this job_NL',
            allBookedLabel: 'NL_All booked resources on this job_NL',
            clearSkillsReviewsLabel: 'NL_Clear all skills reviews_NL'
        },
        reviewSection: {
            skillReviewLabel: 'NL_Skill reviews_NL',
            resourceReviewedLabel: 'NL_{0} Resource reviewed_NL',
            submitReviewButtonLabel: 'NL_Submit skills review_NL'
        }
    },
    resourceSummarySection: {
        availabilityText: 'Beschikbaarheid',
        suitabilityText: 'Geschiktheid',
        hoursSuffix: 'u',
        andLabel: 'en'
    },
    suggestedResources: {
        addToShortlist: 'Toevoegen aan shortlist',
        matchTextSuffix: 'match',
        isATextMessage: 'is een ',
        skillsLabel: 'Vaardigheden',
        workExperienceLabel: 'Werkervaring',
        skillWithPrefix: 'Vaardigheid met',
        similarityTextSuffix: 'overeenkomst:',
        suitabilityText: 'Geschiktheid',
        mixedSortOptionText: 'Geschiktheid en beschikbaarheid',
        sortOrderText: 'Sorteervolgorde: ',
        suggestionsLabel: 'Suggesties',
        forLabel: 'voor',
        appliedOn: 'Aangevraagd op',
        lastRefreshedText: 'Laatst vernieuwd',
        refreshLabel: 'Vernieuwen',
        loadingLabel: 'Laden...',
        noRelevantAISuggestionDataText: 'Onvoldoende vaardigheids- of ervaringsgegevens voor een geschikte score',
        infoBannerMessage: 'Alle resultaten voldoen aan de ${resourceSingularLowerAlias} attribuutvereisten.',
        aiSuggestionsLabel: 'Alle suggesties',
        aiToggleTooltipText: 'Schal dit in om AI-suggesties te gebruiken'
    },
    common: {
        selectRowTextAriaLabel: 'Rij selecteren voor ${name}',
        maximumFieldLengthValidationMessage: 'Maximaal ${maximumFieldSize} tekens',
        daysString: 'dagen',
        singleDayString: 'dag',
        workingString: 'werkt',
        nonWorkingString: 'werkt niet',
        hoursString: 'uren',
        FTEstring: 'FTE',
        pendingString: 'in behandeling',
        hoursPerDaySuffix: 'uren per dag',
        excludeNonWorkingString: 'sluit vrije dagen uit',
        includeNonWorkingString: 'voegt vrije dagen toe',
        addPrefix: 'Toevoegen',
        newPrefix: 'Nieuw',
        allPrefix: 'Alles',
        addAnotherPrefix: 'Nog eentje toevoegen',
        clickToEditSuffix: '(klik om te bewerken)',
        insufficientPermissionsToEditSuffix: 'Onvoldoende machtigingen om te bewerken',
        historyFieldPlaceholder: 'Niet-gespecificeerd',
        noValueMessage: 'Geen ${fieldInfoAlias} ingesteld',
        milestoneHistoryFieldPlaceholder: 'Geen mijlpaal ingesteld',
        startingLabel: 'Start',
        endingLabel: 'Eindigt',
        dueDate: 'Einddatum',
        nameLabel: 'Naam',
        restrictedLabel: 'Beperkt',
        milestonesSectionTitle: 'Mijlpalen',
        milestonesSectionTitleNameDueDate: 'Naam mijlpalen einddatum',
        milestonePlaceholder: 'bijvoorbeeld projectgoedkeuring',
        markAsCompletedLabel: 'Markeren als Voltooid',
        noString: 'Nee',
        setString: 'instellen',
        totalSuffix: 'Totaal',
        hourlySuffix: 'per uur',
        noResultsFoundMessage: 'Geen resultaten gevonden',
        noResultsMessage: 'Geen resultaten',
        checkedMessage: 'Ja',
        uncheckedMessage: 'Nee',
        shownLabel: 'Tonen',
        hiddenLabel: 'Verbergen',
        jobUtitilizationInfo: 'Boekingen uitsluiten van gebruik',
        excludeValue: 'Uitsluiten',
        includeValue: 'Toevoegen',
        noResultsMessagePrefix: 'Nee',
        noResultsMessageSuffix: 'is gevonden met deze naam.',
        noOptionsSetSuffix: 'categorie-opties ingesteld door uw beheerder',
        showMorePrefix: 'Tonen',
        showMoreSuffix: 'meer',
        showLessText: 'Minder tonen',
        seeMoreText: 'NL_See more_NL',
        detailsTabLabel: 'Details',
        historyTabLabel: 'Geschiedenis',
        editAllTabLabel: 'Alles bewerken',
        roleListLabel: 'Lijst met rollen',
        newTemplateLabel: 'Nieuw sjabloon',
        audit: {
            sortLabel: 'Rangschikken',
            showMoreText: 'Meer tonen',
            backToTopText: 'Terug naar boven',
            oldValuePrefix: 'van',
            actorPrefix: 'door',
            startingText: 'start',
            timeLineActionCreateAlias: 'toegevoegd',
            timeLineActionUpdateAlias: 'bijgewerkt',
            unassignedValue: 'Toewijzing geannuleerd',
            levelString: 'niveau',
            timeLineActionUpdateSuffixAlias: 'tot',
            timeLineActionDeleteAlias: 'verwijderd',
            timeLineActionRemoveAlias: 'verwijderd',
            falseString: 'Nee',
            trueString: 'Ja',
            anyLevelString: 'Alle niveaus',
            resourceNotFoundCaption: '${resourceSingularCapitalAlias} niet gevonden',
            templateTexts: {
                historyRecordCreateText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionCreateAlias}',
                historyRecordUpdateText: '${alias} ${timeLineActionUpdateAlias} ${timeLineActionUpdateSuffixAlias} ${valueDescription} ${oldValuePrefix} ${oldValueDescription} ${startingText} ${valueStartDate}',
                historyRecordDeleteText: '${alias}: ${valueDescription} ${startingText} ${valueStartDate} ${timeLineActionRemoveAlias}'
            },
            auditSectionTitles: {
                projectHealthTitle: 'Gezondheid van het project:'
            }
        },
        lastUpdatedLabel: 'Laatst bijgewerkt',
        updatedByLabel: 'door',
        noDiaryOnDatesLabel: 'Geen agenda',
        noOverlappingBookings: 'Geen overlappende boekingen',
        archivedLabel: 'Archiveren',
        restartedLabel: 'Opnieuw starten',
        requestedLabel: 'Aanvragen',
        batchRequestedLabel: 'aanvragen',
        rejectedLabel: 'Afwijzen',
        expiredLabel: 'Laten verlopen',
        draftLabel: 'Concept',
        liveLabel: 'Live',
        actionsDropdownLabel: 'Acties',
        unassignedPlaceholder: 'Toewijzing geannuleerd',
        availabilityText: 'Beschikbaarheid',
        movePendingFTELabel: 'FTE’s die worden behandeld verplaatsen',
        removePendingFTELabel: 'FTE’s die worden behandeld verwijderen',
        movePendingResourcesLabel: 'In behandeling verplaatsen ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'In behandeling verwijderen ${resourcePluralLowerAlias}',
        rolerequestLoadingExplanation: 'van ${resourcePluralLowerAlias} capaciteit',
        noRateSetLabel: 'Geen tarief ingesteld',
        resourcesString: '${resourcePluralLowerAlias}',
        potentialConflictsTooltip: 'Potentiële conflicten met bestaande ${booking}',
        addFiltersButtonLabel: 'Filters toevoegen',
        singleDayUnit: 'dag',
        daysUnit: 'dagen',
        singleWeekUnit: 'week',
        weeksUnit: 'weken',
        singleMonthUnit: 'maand',
        monthsUnit: 'maanden',
        singleYearUnit: 'jaar',
        yearsUnit: 'jaren',
        revertToStartAndEndDates: 'Begin- en einddatums terugzetten',
        searchToSelect: 'Zoeken om te selecteren',
        fieldMandatoryText: 'Dit veld is verplicht',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        estimatesTooltip: 'Schatting van ${rolerequestSingularLowerAlias} budget voordat er resources zijn toegewezen.',
        assigneesTotalsTooltip: 'Huidige ${rolerequestSingularLowerAlias} budget met huidige toegewezen personen.',
        editLabel: 'Knop Bewerken',
        closeLabel: 'Knop Sluiten',
        deleteLabel: 'Knop Verwijderen',
        addLabel: 'Knop Toevoegen',
        quantityAria: 'Invoeren ${fieldAlias}',
        criteriaRoleBudgetDescription: 'Geschatte waarden worden gebruikt in ${rolerequestgroupSingularLowerAlias} budgettotalen totdat de ${rolerequestSingularLowerAlias} vol zit',
        noResourcesAssigned: 'Geen ${resourcePluralLowerAlias} toegewezen',
        noRoleGroupSetLabel: 'Geen ${rolerequestgroupSingularLowerAlias} ingesteld',
        avatarAltText: 'Profielfoto voor ${resourceName}',
        copyOfPrefix: 'Kopie van',
        ascendingSort: 'Oplopend',
        descendingSort: 'Aflopend',
        selectionListSort: 'Selectielijst sorteren',
        deleteLabelText: 'Verwijderen',
        deleteLabelItemText: 'Verwijderen ${item}',
        scrollDownText: 'Omlaag scrollen',
        scrollBarText: 'Schuifbalk',
        scrollUpText: 'Omhoog scrollen',
        cancelChangesLabel: 'Wijzigingen annuleren',
        saveChangesLabel: 'Wijzigingen opslaan',
        fullScreenButtonLabel: 'volledige pagina',
        clearNameLabel: '${fieldName} veld wissen',
        collapseLeftNavigation: 'Linker navigatie uitvouwen',
        expandLeftNavigation: 'Linker navigatie samenvouwen',
        fieldAriaLabel: 'Invoeren ${fieldAlias}',
        selectAllRolesLabel: 'Alle rollen selecteren',
        expandRowLabel: 'Rij samenvouwen',
        collapseRowLabel: 'Rij uitvouwen',
        floatingActionBarLabel: 'NL_${entity} ${fieldName} may be different for this date range_NL',
        floatingActionBarButtonLabel: 'NL_Update sort order_NL'
    },
    tableOptions: {
        displayDensityOptionTitle: 'Dichtheid display',
        chooseDetailsOptionTitle: 'Details kiezen',
        compactDensityOptionTitle: 'Compact',
        defaultDensityOptionTitle: 'Standaard',
        expandedDensityOptionTitle: 'Samengevouwen',
        searchLabel: 'Zoeken',
        applyButtonText: 'Toepassen',
        notLoadedLabel: 'niet geladen',
        notLoadedValueLabel: 'waarde niet geladen',
        tableOptionsLabel: 'Tabelopties'
    },
    dataGrid: {
        sortLabel: 'Rangschikken',
        sortyByLabel: 'Rangschikken op',
        pageOptionSuffix: ' per pagina',
        noItemsMessage: 'U heeft geen taken',
        noRolesItemsMessage: 'U heeft geen rollen',
        newJob: 'Nieuwe taak',
        noMatchingItemsMessage: 'Geen taken gevonden die met de filters overeenkomen',
        noMatchingResourceItemsMessage: 'NL_No resources found matching your filters_NL',
        noMatchingResourceItemsContentMessage: 'NL_Try changing your filters or view settings_NL',
        noMatchingRolesItemsMessage: 'Geen rollen gevonden die met de filters overeenkomen',
        noMatchingItemsContent: 'Probeer uw filters aan te passen.',
        noChargetypeSet: 'Geen factureringstype ingesteld',
        noRoleGroupItemsMessage: 'Geen scenario’s',
        noRoleGroupItemsContent: 'Scenario’s voor deze taak aanmaken en vergelijken',
        noResourceFoundMessage: 'Geen ${resourceEntityAlias} gevonden die aan de criteria voldoet',
        noMarketplaceRolesPublished: 'Er zijn geen ${rolerequestPluralLowerAlias} gepubliceerd',
        showingCaption: '${pageRolesCount} weergeven van ${totalRolesCount} ${rolerequestAlias}',
        entityPageOptionSuffix: '${rolerequestPluralLowerAlias} per pagina',
        noResultsMessage: 'Geen resultaten gevonden',
        tryAdjustingFiltersMessage: 'Probeer uw zoekopdracht of filter aan te passen',
        sortLabelButton: 'Rangschikken ${order}',
        sortAscending: 'Oplopend',
        sortDescending: 'Aflopend',
        operationLogEmptyMessage: 'Activiteitenlog is leeg',
        operationLogEmptyContent: 'Dit logbestand is momenteel leeg en bevat geen opgeslagen activiteiten.',
        operationLogSuccess: 'Activiteit voltooid',
        operationLogIncomplete: 'Activiteit niet voltooid',
        operationLogCancelled: 'Activiteit geannuleerd door',
        operationLogFailed: 'Activiteit mislukt',
        cancelOperation: 'Activiteit annuleren',
        undoOperation: 'Activiteit ongedaan maken'
    },
    filterPane: {
        anyLevelLabel: 'Elk niveau',
        selectLabel: 'Selecteren',
        filterSuffix: 'weergave',
        headingTitle: 'Filters',
        applyButtonText: 'Toepassen',
        discardChangesText: 'Filters resetten',
        applyButtonTooltipText: 'Voeg filters toe en vul alles in om in te schakelen.',
        resetButtonTooltipText: 'Reset filters naar de oorspronkelijke status.',
        showMoreButtonText: 'Meer tonen',
        maxDateRangeMessage: 'Maximaal 5 datumbereiken',
        startDateLabel: 'Begin',
        endDateLabel: 'Einde',
        fromDateLabel: 'Van',
        toDateLabel: 'Tot',
        searchLabel: 'Zoeken',
        searchToSelectLabel: 'Zoeken om te selecteren',
        textOperatorLabel: 'Liken',
        loadingLabel: 'aan het laden',
        betweenLabel: 'tussen',
        clearFiltersText: 'Filters wissen',
        removeFilterText: 'Filter verwijderen',
        notLoadedLabel: 'niet geladen',
        notLoadedValueLabel: 'waarde niet geladen',
        levelText: 'Niveau',
        levelsText: 'Niveaus',
        resetFilterText: 'Alles resetten',
        clearAllFiltersText: 'Alles wissen',
        numberResults: '${rowCount} resultaten',
        allLabel: 'Alles',
        yesLabel: 'Ja',
        noLabel: 'Nee',
        addLabel: 'Toevoegen ${fieldAlias}',
        removeLabel: 'Verwijderen ${fieldAlias}',
        removeFilterButtonLabel: 'Het datumbereik van ${startDate} tot verwijderen ${endDate}',
        typeHereLabel: 'Hier invoeren',
        hiddenFiltersMessage: 'Deze filters kunnen niet worden toegepast. Dit kan liggen aan uw gebruikersrechten, of een ongeldig veld/functie. Deze workspace verwijderen zorgt',
        hiddenFiltersBoldMessage: 'dat alle verborgen filters worden verwijderd.',
        operators: {
            DB_OPERATORS: {
                LESS_THAN: 'LessThan',
                LESS_THAN_OR_EQUAL: 'LessThanOrEqual',
                EQUALS: 'Gelijk aan',
                GREATER_THAN_OR_EQUAL: 'GreaterThanOrEqual',
                GREATER_THAN: 'GreaterThan',
                LIKE: 'Liken',
                CONTAINS: 'Bevat'
            },
            NUMERIC_OPERATORS_ALIAS: {
                LessThan: 'minder dan',
                LessThanOrEqual: 'maximaal',
                Equals: 'gelijk aan',
                GreaterThanOrEqual: 'ten minste',
                GreaterThan: 'meer dan'
            },
            NUMERIC_PARAMETER_OPERATORS_ALIAS: {
                LessThan: 'minder dan',
                LessThanOrEqual: 'maximaal',
                Equals: 'gelijk aan',
                GreaterThanOrEqual: 'ten minste',
                GreaterThan: 'meer dan'
            },
            TEXT_OPERATORS_ALIAS: {
                Like: 'bevat'
            },
            MULTY_VALUES_OPERATORS_ALIAS: {
                Contains: 'maakt onderdeel uit van'
            },
            DATE_OPERATORS_ALIAS: {
                LessThanOrEqual: 'tot',
                GreaterThanOrEqual: 'van'
            },
            DATE_SENSITIVE_OPERATORS_ALIAS: {
                GreaterThanOrEqual: 'ten minste',
                LessThanOrEqual: 'maximaal'
            },
            SKILL_OPERATORS_ALIAS: {
                Equals: 'is'
            },
            CHECKBOX_OPERATORS_ALIAS: {
                Equals: 'aan'
            },
            BOOLEAN_OPERATORS_ALIAS: {
                Equals: 'is'
            }
        },
        advancedFilterOperators: {
            TEXT: {
                Contains: 'Bevat',
                IsBlank: 'Is leeg',
                IsNotBlank: 'Is niet leeg'
            },
            NUMERIC: {
                Is: 'Is',
                IsNot: 'Is niet',
                IsMoreThan: 'Is meer dan',
                IsLessThan: 'Is minder dan',
                IsBlank: 'Is leeg',
                IsNotBlank: 'Is niet leeg'
            },
            BOOLEAN: {
                Is: 'Is',
                IsNot: 'Is niet'
            },
            SKILL: {
                Is: 'Is',
                IsNot: 'Is niet',
                IsBlank: 'Is leeg',
                IsNotBlank: 'Is niet leeg'
            }
        },
        logicalOperators: {
            and: 'En',
            or: 'Of'
        },
        uiFilterOperators: {
            Is: 'is',
            IsNot: 'is niet',
            IsMoreThan: 'is meer dan',
            IsLessThan: 'is minder dan',
            Contains: 'bevat',
            UpTo: 'tot',
            From: 'van',
            IsNotBlank: 'is niet leeg',
            IsBlank: 'is leeg',
            IsMoreOrEqual: 'is meer of gelijk',
            IsLessOrEqual: 'is minder of gelijk'
        },
        filterFieldMessages: {
            resource_has_skill_resource_skill_levelAlias: 'Vaardigheden',
            resource_guidAlias: 'Naam resource',
            availabilityAlias: 'Beschikbaarheid',
            utilisationAlias: 'Gebruik',
            departmentAlias: 'resource_current_department_guidDepartment',
            resource_manager_resource_guidAlias: 'Lijnmanager',
            resource_location_guidAlias: 'Locatie',
            resource_localgrade_guidAlias: 'Opleiding',
            resource_resourcetype_guidAlias: 'Type werkzaamheden',
            resource_rolenameAlias: 'Titel',
            resource_booking_countAlias: 'Boekingsaccount',
            'Charge RateAlias': 'Factureringstarief',
            job_guidAlias: 'Taaknaam',
            job_startAlias: 'Begin taak',
            job_endAlias: 'Einde taak',
            job_client_guidAlias: 'Klant',
            job_engagementlead_resource_guidAlias: 'Lijnmanager',
            job_location_guidAlias: 'Locatie taak',
            job_jobstatus_guidAlias: 'Taakstatus',
            job_codeAlias: 'Referentiecode taak',
            job_chargetype_guidAlias: 'Factureringstype',
            booking_is_assignedAlias: 'Toewijzing geannuleerd',
            booking_startAlias: 'Begin boeking',
            booking_endAlias: 'Einde boeking',
            booking_bookingtype_guidAlias: 'Boekingsstatus',
            booking_notesAlias: 'Opmerkingen boeking',
            booking_workactivity_guidAlias: 'Werkactiviteit',
            updatedonAlias: 'bijgewerkt op',
            updatedbyAlias: 'bijgewerkt door resource',
            createdonAlias: 'aangemaakt op',
            createdbyAlias: 'aangemaakt door resource'
        }
    },
    talentProfilePage: {
        profileTitle: 'Mijn profiel',
        shareProfileCaption: 'NL_Share profile_NL',
        uploadLabel: 'Documenten uploaden',
        changeProfielPictureText: 'Profielfoto wijzigen',
        viewOtherProfile: 'Ander profiel bekijken',
        viewMyProfile: 'Mijn profiel bekijken',
        cMeProfileTitle: 'C-Me-profiel',
        editDetailsLabel: 'NL_Edit details_NL',
        updateAvatarWindowMessages: {
            headerTitle: 'Profielfoto aanpassen',
            applyBtnTitle: 'Toepassen',
            removeBtnTitle: 'Foto verwijderen',
            cancelBtnTitle: 'Annuleren',
            uploadAreaText: 'Klik op of sleep bestand naar dit gedeelte om te uploaden',
            fileTypesString: 'Bestandstypen: ',
            dragControlLabel: 'Slepen',
            zoomControlLabel: 'In-/uitzoomen'
        },
        recommendationTitle: 'Aanbevelingen',
        recommendationAlertHeader: 'Breid uw vaardigheden uit en wordt opgemerkt',
        recommendationAlertDescription: 'Hier volgen enkele vaardigheden die u aan uw profiel kunt toevoegen',
        skillApproval: 'NL_Your changes will be sent to manager for approval. Changes to skill preferences do not require approval._NL',
        additionalDetailsSectionTitle: 'Aanvullende details',
        messages: {
            fileTooLargeLabel: 'Document niet geüpload: Bestand is te groot',
            fileTypeForbiddenLabel: 'Document niet geüpload: Bestandstype is niet toegestaan',
            noFilesUploadedLabel: 'U heeft geen documenten geüpload',
            uploadsLimitReachedLabel: 'Documentlimiet bereikt: verwijder documenten om meer te uploaden'
        },
        uploadDocumentsWindowMessages: {
            headerTitle: 'NL_Upload document_NL',
            uploadBtnTitle: 'NL_Upload_NL',
            cancelBtnTitle: 'NL_Cancel_NL',
            uploadAreaText: 'NL_Click or drag file to this area to upload_NL',
            fileTypesString: 'NL_Accepted formats: _NL',
            documentType: 'NL_Type of document_NL',
            expiryDate: 'NL_Expiry date_NL',
            maxFileSize: 'NL_Max ${maxFileSize} MB per file_NL'
        }
    },
    prompts: {
        createOrSaveAsNewPlanPrompt: {
            title: 'Nieuw plan',
            placeholder: 'Plannaam toevoegen',
            helpMessage: 'Voer een naam in voor uw plan',
            okText: 'Opslaan',
            cancelText: 'Annuleren',
            name: 'Naam',
            type: 'Type',
            access: 'Toegang',
            editAccess: 'Toegang bewerken',
            readOnlyAccess: 'Alleen-lezen',
            subHeading: 'Opslaan als een nieuw persoonlijk of openbaar plan',
            privatePlan: 'Persoonlijk plan',
            publicPlan: 'Openbaar plan',
            maxLengthValidationMessage: 'Maximaal ${maxNameLength} symbolen toegestaan',
            newPlanLabel: 'Nieuw plan',
            switchOnLabel: 'Inschakelen'
        },
        createOrSaveAsNewWorkspacePrompt: {
            title: 'NL_New workspace_NL',
            placeholder: 'NL_Insert workspace name_NL',
            helpMessage: 'NL_Please provide a name for your workspace_NL',
            okText: 'Opslaan',
            cancelText: 'Annuleren',
            name: 'Naam',
            type: 'Type',
            access: 'Toegang',
            editAccess: 'Toegang bewerken',
            readOnlyAccess: 'Alleen-lezen',
            subHeading: 'NL_Save as a new private or public workspace_NL',
            privatePlan: 'NL_Private workspace_NL',
            publicPlan: 'NL_Public workspace_NL',
            maxLengthValidationMessage: 'Maximaal ${maxNameLength} symbolen toegestaan',
            newPlanLabel: 'NL_New Workspace_NL',
            switchOnLabel: 'Inschakelen'
        },
        extendJobRangePrompt: {
            okText: 'Ja, taakdatums aanpassen',
            cancelText: 'Nee, annuleren',
            title: 'De taak verlengen?',
            message: 'Wilt u de taak verlengen om deze boeking te kunnen verplaatsen?',
            detailsText: 'Nieuwe taakdatums voor'
        },
        deleteBookingPrompt: {
            title: 'Verwijderen',
            message: 'Wilt u deze ... verwijderen',
            okText: 'Ja, verwijder de',
            cancelText: 'Nee, behoud de',
            noEntityDescriptionPrefix: 'Nee',
            noEntityDescriptionSuffix: 'beschrijving'
        },
        makePlanPrivatePrompt: {
            okText: 'Plan persoonlijk maken',
            cancelText: 'Plan openbaar houden',
            makeString: 'Een',
            privatePlanString: 'persoonlijk plan aanmaken',
            planTypeMessageStart: 'Dit plan is momenteel',
            messageFirstPart: 'Als u dit plan persoonlijk maakt',
            messageBold: 'is het niet langer beschikbaar voor anderen.'
        },
        makeWorkspacePrivatePrompt: {
            okText: 'NL_Make workspace private_NL',
            cancelText: 'NL_Keep workspace public_NL',
            makeString: 'Een',
            privatePlanString: 'NL_a private workspace_NL',
            planTypeMessageStart: 'NL_This workspace is currently_NL',
            messageFirstPart: 'NL_If you make this workspace private,_NL',
            messageBold: 'is het niet langer beschikbaar voor anderen.'
        },
        deletePlanPrompt: {
            okText: 'Ja, plan verwijderen',
            cancelText: 'Nee, plan behouden',
            deleteString: 'Verwijderen',
            planString: 'plan',
            workspaceTypeMessage: 'Dit plan is momenteel',
            publicDeleteMessageStart: 'Als u dit plan verwijdert',
            publicDeleteMessageEnd: 'is het niet langer beschikbaar voor anderen',
            question: 'Wilt u dit plan permanent verwijderen?'
        },
        deleteWorkspacePrompt: {
            okText: 'NL_Yes, delete workspace_NL',
            cancelText: 'NL_No, keep workspace_NL',
            deleteString: 'Verwijderen',
            planString: 'NL_workspace_NL',
            workspaceTypeMessage: 'NL_This workspace is currently_NL',
            publicDeleteMessageStart: 'NL_If you delete this workspace_NL',
            publicDeleteMessageEnd: 'is het niet langer beschikbaar voor anderen',
            question: 'NL_Do you wish to permanently delete this workspace?_NL'
        },
        deleteRoleTemplatePrompt: {
            okText: 'Ja, sjabloon verwijderen',
            cancelText: 'Nee, sjabloon behouden',
            warning: 'Dit kan niet ongedaan worden gemaakt.',
            question: 'Wilt u dit ${rolerequestSingularLowerAlias} sjabloon permanent verwijderen?',
            title: '${roleTemplateDescription} sjabloon verwijderen?'
        },
        renameRoleTemplatePrompt: {
            okText: 'Sjabloonnaam wijzigen',
            cancelText: 'Oude naam houden',
            question: 'U heeft de sjabloonnaam gewijzigd. Wilt u deze wijziging opslaan?',
            title: 'Sjabloonnaam wijzigen',
            fromText: 'van ',
            toText: 'tot '
        },
        deleteClientPrompt: {
            title: 'Klant verwijderen?',
            message: 'Als u deze klant verwijdert, wordt deze permanent verwijderd uit het volledige systeem inclusief alle gekoppelde taken. Het is niet mogelijk om deze klant toe te wijzen aan toekomstige taken. \n Wilt u doorgaan?',
            okText: 'Ja, klant verwijderen',
            cancelText: 'Nee, klant behouden'
        },
        deleteJobPrompt: {
            title: 'Verwijderen',
            okText: 'Verwijderen',
            cancelText: 'Behouden',
            thereString: 'Er',
            isString: 'is',
            areString: 'zijn',
            andString: 'en',
            onString: 'aan',
            thisString: 'dit',
            theseString: 'deze',
            withString: 'met',
            betweenString: 'tussen',
            onThisJobString: 'voor deze taak',
            deletingThisJobWillAlsoDeleteString: 'Als u deze taak verwijdert, verwijdert u ook',
            doYouWishToPermanentlyDeleteString: 'Wilt u ... permanent verwijderen',
            messages: {
                deleteJobTitle: '${jobDescription} verwijderen?',
                deleteJobLabel: '${jobAlias}, ${bookingAlias}, ${roleGroupAlias} en ${roleRequestAlias} verwijderen?'
            }
        },
        deleteRolePrompt: {
            title: 'Verwijderen',
            message: 'Wilt u deze ... verwijderen',
            okText: 'Ja, verwijder de',
            cancelText: 'Nee, behoud de ',
            noEntityDescriptionPrefix: 'Nee',
            noEntityDescriptionSuffix: 'beschrijving',
            defaultRoleName: 'Nieuwe rol',
            noRoleGroupSetLabel: 'Geen ${rolerequestgroupSingularLowerAlias} ingesteld'
        },
        removeRolePublicationPrompt: {
            title: '${rolerequestSingularLowerAlias} verwijderen uit ${roleBoardPageAlias}',
            message: 'Er zijn ${countOfResources} ${resourcePluralLowerAlias} toegepast op deze ${rolerequestSingularLowerAlias}. Als u de ${rolerequestSingularLowerAlias} uit ${roleBoardPageAlias} verwijdert, worden ook hun applicaties verwijderd.',
            question: 'Wilt u ${rolePublicationDescription} verwijderen uit ${roleBoardPageAlias}?',
            confirmation: '${resourcePluralLowerAlias} applicaties en ${rolerequestSingularLowerAlias} verwijderen uit ${roleBoardPageAlias}?',
            okText: 'Verwijderen ${rolerequestSingularLowerAlias}',
            cancelText: 'Behouden ${rolerequestSingularLowerAlias}',
            defaultRoleName: 'Nieuwe rol'
        },
        unsavedChangesPrompt: {
            message: 'U heeft niet-opgeslagen wijzigingen op deze pagina. Wilt u nog steeds afsluiten?',
            saveLabel: 'Wijzigingen opslaan',
            discardLabel: 'Wijzigingen annuleren',
            cancelLabel: 'Annuleren'
        },
        renamePlanPrompt: {
            okText: 'Plannaam wijzigen',
            cancelText: 'Oude naam houden',
            title: 'Plannaam wijzigen',
            message: 'U heeft de plannaam gewijzigd. Wilt u deze wijziging opslaan?',
            oldNamePrefix: 'van',
            newNamePrefix: 'tot'
        },
        renameWorkspacePrompt: {
            okText: 'NL_Rename workspace_NL',
            cancelText: 'Oude naam houden',
            title: 'NL_Rename workspace_NL',
            message: 'NL_You have renamed the workspace. Do you wish to save this change?_NL',
            oldNamePrefix: 'van',
            newNamePrefix: 'tot'
        },
        saveChangesPrompt: {
            okText: 'Doorgaan zonder op te slaan',
            cancelText: 'Annuleren',
            title: 'Niet-opgeslagen wijzigingen',
            message: 'is een',
            planSuffix: 'plan',
            saveAsPrivatePlanButtonLabel: 'Opslaan als een persoonlijk',
            saveChangesToPublicButtonLabel: 'Wijzigingen opslaan naar openbaar'
        },
        saveWorkspaceChangesPrompt: {
            okText: 'Doorgaan zonder op te slaan',
            cancelText: 'Annuleren',
            title: 'Niet-opgeslagen wijzigingen',
            message: 'is een',
            planSuffix: 'NL_workspace_NL',
            saveAsPrivatePlanButtonLabel: 'Opslaan als een persoonlijk',
            saveChangesToPublicButtonLabel: 'Wijzigingen opslaan naar openbaar'
        },
        deleteResourceSkillPrompt: {
            okText: 'Ja, vaardigheid verwijderen',
            cancelText: 'Nee, vaardigheid behouden',
            selectedSkill: 'Geselecteerde vaardigheid',
            title: 'Vaardigheid verwijderen?',
            messagePrefix: 'Wilt u ',
            messageSuffix: ' uit het profiel verwijderen?'
        },
        deleteCommentPrompt: {
            okText: 'Opmerking verwijderen',
            cancelText: 'Opmerking behouden',
            title: 'Opmerking verwijderen?'
        },
        singleCreateBookingErrorPrompt: {
            title: 'Fout tijdens aanmaken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het aanmaken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit aan te maken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleCreateJobErrorPrompt: {
            title: 'Fout tijdens aanmaken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het aanmaken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit aan te maken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleCreateClientErrorPrompt: {
            title: 'Fout tijdens aanmaken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het aanmaken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit aan te maken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleCreateRolegroupErrorPrompt: {
            title: 'Fout tijdens aanmaken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het aanmaken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit aan te maken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singlePublishRoleErrorPrompt: {
            title: 'Fout tijdens publiceren ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het publiceren van het volgende ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om deze ${entitySingularLower} te publiceren op ${marketplaceAlias}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Publicatiegegevens bewerken',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleEditRolePublicationErrorPrompt: {
            title: 'Fout tijdens bewerken ${entitySingularLower} publicatie',
            message: 'Er is een fout opgetreden tijdens het publiceren van de volgende ${entitySingularLower} publicatie:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om deze ${entitySingularLower} publicatie te bewerken',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleRemoveRolePublicationErrorPrompt: {
            title: 'Fout tijdens verwijderen ${entitySingularLower} publicatie',
            message: 'Er is een fout opgetreden tijdens het verwijderen van de volgende ${entitySingularLower} publicatie:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om deze ${entitySingularLower} publicatie te verwijderen van ${marketplaceAlias}',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleEditBookingErrorPrompt: {
            title: 'Fout tijdens bewerken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het bewerken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te bewerken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleEditJobErrorPrompt: {
            title: 'Fout tijdens bewerken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het bewerken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te bewerken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleEditClientErrorPrompt: {
            title: 'Fout tijdens bewerken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het bewerken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te bewerken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleEditResourceErrorPrompt: {
            title: 'Fout tijdens bewerken profiel',
            message: 'Er is een fout opgetreden tijdens het bewerken van het volgende profiel:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit profiel te bewerken',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Profiel bewerken',
            discardButtonLabel: 'Profiel verwijderen'
        },
        singleSaveTemplateErrorPrompt: {
            title: 'Fout tijdens aanmaken ${entitySingularLower} sjabloon',
            message: 'Er is een fout opgetreden tijdens het aanmaken van de volgende ${entitySingularLower} sjabloon:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om ${entitySingularLower} sjabloon aan te maken',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: '${entitySingularLower} sjabloon verwijderen',
            defaultRoleTemplateName: 'Nieuw ${entitySingularUpper}'
        },
        singleDeleteErrorPrompt: {
            title: 'Fout tijdens verwijderen ${entitySingularLower} sjabloon',
            message: 'Er is een fout opgetreden tijdens het verwijderen van de volgende ${entitySingularLower} sjabloon:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om ${entitySingularLower} sjabloon te verwijderen',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: '${entitySingularLower} sjabloon verwijderen',
            defaultRoleTemplateName: 'Nieuw ${entitySingularUpper}'
        },
        singleEditErrorPrompt: {
            title: 'Fout tijdens bewerken ${entitySingularLower} sjabloon',
            message: 'Er is een fout opgetreden tijdens het bewerken van de volgende ${entitySingularLower} sjabloon:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om ${entitySingularLower} sjabloon te bewerken',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: '${entitySingularLower} sjabloon verwijderen',
            defaultRoleTemplateName: 'Nieuw ${entitySingularUpper}'
        },
        singleEditRolegroupErrorPrompt: {
            title: 'Fout tijdens bewerken ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het bewerken van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te bewerken ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleDeleteBookingErrorPrompt: {
            title: 'Fout tijdens verwijderen ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het verwijderen van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verwijderen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleDeleteJobErrorPrompt: {
            title: 'Fout tijdens verwijderen ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het verwijderen van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verwijderen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleDeleteClientErrorPrompt: {
            title: 'Fout tijdens verwijderen ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het verwijderen van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verwijderen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleDeleteRolegroupErrorPrompt: {
            title: 'Fout tijdens verwijderen ${entitySingularLower}',
            message: 'Er is een fout opgetreden tijdens het verwijderen van het volgende ${entitySingularLower}',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verwijderen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            editButtonLabel: 'Bewerken ${entitySingularLower}',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        carouselCreateErrorPrompt: {
            title: 'Fout tijdens het bewerken van ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} van ${attemptedCount} ${entityString} succesvol bijgewerkt.',
            permissionsErrorMessage: 'Onvoldoende gemachtigd om dit te bewerken ${entityString}',
            retry: 'Opnieuw proberen',
            errorSectionMessage: 'Er is een fout opgetreden tijdens het bewerken van het volgende ${entityString}',
            edit: 'Bewerken ${entityString}',
            cancel: 'Annuleren ${entityString}',
            close: 'Sluiten'
        },
        carouselRollForwardCreateErrorPrompt: {
            title: 'Fout tijdens aanmaken van ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} van ${attemptedCount} ${entityString} succesvol aangemaakt.',
            permissionsErrorMessage: 'Onvoldoende gemachtigd om dit aan te maken ${entityString}',
            retry: 'Opnieuw proberen',
            errorSectionMessage: 'Er is een fout opgetreden tijdens het aanmaken van het volgende ${entityString}',
            edit: 'Terug naar opties voor \'Uitrollen\'',
            cancel: 'Verwijderen mislukt ${entityString}',
            close: 'Sluiten'
        },
        carouselEditErrorPrompt: {
            title: 'Fout tijdens het bewerken van ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} van ${attemptedCount} ${entityString} succesvol bijgewerkt.',
            permissionsErrorMessage: 'Onvoldoende gemachtigd om dit te bewerken ${entityString}',
            retry: 'Opnieuw proberen',
            errorSectionMessage: 'Er is een fout opgetreden tijdens het bewerken van het volgende ${entityString}',
            edit: 'Bewerken ${entityString}',
            cancel: 'Annuleren ${entityString}',
            close: 'Sluiten'
        },
        carouselDeleteErrorPrompt: {
            title: 'Fout tijdens verwijderen van ${entityString} (${failedCount})',
            successfulCountMessage: '${succeededCount} van ${attemptedCount} ${entityString} succesvol verwijderd.',
            permissionsErrorMessage: 'Onvoldoende gemachtigd om dit te verwijderen ${entityString}',
            retry: 'Opnieuw proberen',
            errorSectionMessage: 'Er is een fout opgetreden tijdens het verwijderen van het volgende ${entityString}',
            edit: 'Bewerken ${entityString}',
            cancel: 'Annuleren ${entityString}',
            close: 'Sluiten'
        },
        batchedCreateEntityErrorPrompt: {
            successfulCountMessage: '${succeededCount} van ${attemptedCount} ${entityString} succesvol aangemaakt.',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit aan te maken ${entityString}',
            title: 'Fout tijdens aanmaken ${entityString}',
            errorSectionMessage: 'Er is een fout opgetreden tijdens het aanmaken van het volgende ${entityString}',
            retryButtonLabel: 'Opnieuw proberen',
            cancelButtonLabel: 'Annuleren',
            editEntitiesButtonLabel: 'Bewerken ${entityString}',
            discardEntitiesButtonLabel: 'Annuleren ${entityString}',
            closeDialogButtonLabel: 'Sluiten',
            tryAgainMessage: 'Wilt u het opnieuw proberen?',
            requests: 'aanvragen'
        },
        deleteMultipleBookingsPrompt: {
            title: '${bookingsAliasUpper} verwijderen? (${bookingsCount})',
            message: 'Wilt u ${pronounString} ${bookingsAliasLower} verwijderen?',
            okText: 'Ja, verwijder de ${bookingsAliasUpper}',
            cancelText: 'Nee, behoud de ${bookingsAliasUpper}',
            close: 'Sluiten',
            theseString: 'deze',
            thatString: 'dat'
        },
        cantPasteBarPrompt: {
            message: 'Selecteer een cel om uw ${barAliasLower} te plakken naar'
        },
        deleteMultipleRolerequestsPrompt: {
            title: '${rolerequestsAliasUpper} verwijderen? (${rolerequestsCount})',
            message: 'Wilt u ${pronounString} ${rolerequestsAliasLower} verwijderen?',
            okText: 'Ja, verwijder de ${rolerequestsAliasUpper}',
            cancelText: 'Nee, behoud de ${rolerequestsAliasUpper}',
            close: 'Sluiten',
            theseString: 'deze',
            thatString: 'dat'
        },
        moveRolerequestTimeAllocationPrompt: {
            title: 'Verplaats ${pluralFieldName} in behandeling van ${rolerequestDescription}',
            message: 'Alle aangevraagde ${pluralFieldName} in behandeling worden verplaatst naar een nieuwe ‘Concept’ ${rolerequestsSingularLower}, en deze ${rolerequestsSingularLower} worden op‘Live’ ingesteld. De toewijzing van ‘Aangevraagde’ personen wordt geannuleerd.',
            warningMessage: 'Dit kan niet ongedaan worden gemaakt.',
            okText: 'In behandeling verplaatsen ${pluralFieldName}',
            cancelText: 'Houd aanvraag open',
            close: 'Sluiten',
            FTEs: 'FTE’s'
        },
        removeRolerequestTimeAllocationPrompt: {
            title: '${pluralFieldName} in behandeling verplaatsen van ${rolerequestDescription}',
            message: 'Alle aangevraagde ${pluralFieldName} in behandeling worden verplaatst en de ${rolerequestsSingularLower} wordt op‘Live’ ingesteld. De toewijzing van ‘Aangevraagde’ personen wordt geannuleerd.',
            warningMessage: 'Dit kan niet ongedaan worden gemaakt.',
            okText: 'In behandeling verwijderen ${pluralFieldName}',
            cancelText: 'Houd aanvraag open',
            close: 'Sluiten',
            FTEs: 'FTE’s'
        },
        createRolegroupModal: {
            placeholder: 'Nieuw ${roleGroupAlias}',
            title: 'Aanmaken ${roleGroupAlias}',
            nameDescriptor: 'Naam',
            createLabel: 'Aanmaken',
            cancelLabel: 'Annuleren',
            currentValue: '${jobDescription} ${roleGroupAlias} ${currentSubsequentNumber}',
            helpMessage: 'Voer een naam in voor uw ${roleGroupAlias}',
            maxLengthValidationMessage: 'Maximaal ${maxNameLength} symbolen toegestaan'
        },
        saveAsTemplateModal: {
            placeholder: 'Nieuw ${roleAlias} sjabloon',
            title: 'Nieuw sjabloon',
            headerTitle: 'Geef de nieuwe ${roleAlias} sjabloon een naam en sla deze op.',
            nameDescriptor: 'Naam',
            createLabel: 'Opslaan',
            cancelLabel: 'Annuleren',
            currentValue: '${rolerequestDescription}',
            defaultNewRole: 'Nieuw ${roleAlias}',
            helpMessage: 'Voer een naam in voor uw ${rolerequestDescription} sjabloon',
            maxLengthValidationMessage: 'Maximaal ${maxNameLength} symbolen toegestaan'
        },
        progressRolesWindow: {
            title: 'Fout tijdens voortgang ${roleAlias}'
        },
        deleteRoleGroupPrompt: {
            title: '${roleGroupDescription} verwijderen?',
            roleGroupInfoMessage: 'Er zijn <bold>${rolesNumber}</bold> ${roleAliasPlural} inclusief <bold>${roleRequests}</bold> aanvragen in deze ${roleGroupAliasSingular} tussen <bold>${roleStartDate}</bold> - <bold>${roleEndDate}</bold>. Als u deze ${roleGroupAliasSingular} verwijdert, worden deze ${roleAliasPlural} en aanvragen ook verwijderd.',
            shouldDeleteQuestion: 'Wilt u <bold>${roleGroupDescription}</bold> permanent verwijderen?',
            checkboxText: 'Verwijder ${roleGroupAliasSingular}, ${roleAliasPlural} en aanvragen.',
            cancelMessage: 'Behouden ${roleGroupAliasSingular}',
            confirmMessage: 'Verwijderen ${roleGroupAliasSingular}'
        },
        extendJobRangeDetailsPagePrompt: {
            okText: 'Ja, taakdatums aanpassen',
            cancelText: 'Nee, annuleer planning',
            title: 'De taak verlengen?',
            message: 'Wilt u de taak verlengen om deze',
            roleTailMessage: 'rol te kunnen plannen?',
            rolesTailMessage: 'deze rollen te kunnen plannen?',
            detailsText: 'Nieuwe taakdatums voor'
        },
        singleMoveResourceRolegroupErrorPrompt: {
            title: 'Fout tijdens verplaatsen naar in behandeling ${resourcePluralLower}',
            message: 'Er is een fout opgetreden tijdens het verplaatsen van de ${resourcePluralLower} in behandeling voor de volgende ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verplaatsen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleRemoveResourceRolegroupErrorPrompt: {
            title: 'Fout tijdens verwijderen uit in behandeling ${resourcePluralLower}',
            message: 'Er is een fout opgetreden tijdens het verwijderen van volgende ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verplaatsen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleMoveFTERolegroupErrorPrompt: {
            title: 'Fout tijdens verplaatsen van FTE’s in behandeling',
            message: 'Er is een fout opgetreden tijdens het verplaatsen van de FTE’s in behandeling voor de volgende ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verplaatsen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        singleRemoveFTERolegroupErrorPrompt: {
            title: 'Fout tijdens verwijderen FTE’s in behandeling',
            message: 'Er is een fout opgetreden tijdens het verwijderen van de FTE’s in behandeling voor de volgende ${entitySingularLower}:',
            insufficientPermissionsMessage: 'Onvoldoende gemachtigd om dit te verplaatsen ${entitySingularLower}',
            retryButtonLabel: 'Opnieuw proberen',
            discardButtonLabel: 'Annuleren ${entitySingularLower}'
        },
        updateRolerequestStatusWindow: {
            title: 'Fout tijdens bijwerken ${roleAlias} status'
        },
        publishRoleErrorPrompt: {
            title: 'Fout tijdens publiceren ${roleAlias}'
        },
        editRolePublicationPrompt: {
            title: 'Fout tijdens bewerken ${roleAlias} publicatie'
        },
        withdrawRoleApplicationPrompt: {
            title: 'Intrekken bevestigen',
            question: 'U wordt niet langer overwogen voor deze ${rolerequestSingularLowerAlias}.',
            warning: 'Weet u zeker dat u uw aanvraag in wilt trekken?',
            okText: 'Ja, trek mijn aanvraag in',
            cancelText: 'Nee, behoud mijn aanvraag'
        },
        tableViewHoursValidationPrompt: {
            title: 'Ongeldige uren',
            message: 'U kunt tussen 0 en 168 uren per week boeken.',
            okText: 'Ok',
            tooltipText: 'Druk op Esc om af te sluiten'
        },
        tableViewCellEditErrornPrompt: {
            title: 'Fout tijdens opslaan wijzigingen',
            errorMessage: 'De ${tableViewPageAlias} pagina bijwerken kan een combinatie vereisen van ${bookingPluralForm} in de achtergrond aanmaken, bewerken en verwijderen.',
            contactMessage: 'Neem contact op met uw beheerder om te controleren of u de juiste machtigingen heeft.',
            discardText: 'Wijzigingen annuleren',
            retryText: 'Opnieuw proberen'
        },
        splitBookingsErrorPrompt: {
            title: 'Fout tijdens opslaan wijzigingen',
            question: 'Geselecteerde ${bookingSingularLowerAlias} zijn wellicht door iemand anders verwijderd of bewerkt, of u bent onvoldoende gemachtigd. ${bookingPluralLowerAlias} opsplitsen vereist een combinatie van ${bookingPluralLowerAlias} in de achtergrond aanmaken en bewerken.',
            warning: 'Vernieuw de pagina en probeer het opnieuw, of neem contact op met uw beheerder.',
            cancelText: 'OK'
        },
        setPasswordConfirmationPrompt: {
            title: 'Bevestiging vereist',
            question: 'Het wachtwoord wijzigen maakt bestaande integraties ongeldig. Weet u het zeker?',
            okText: 'Ja, ik weet het zeker',
            cancelText: 'Nee, ga terug'
        },
        duplicateJobErrorPrompt: {
            title: 'Fout tijdens opslaan wijzigingen',
            errorMessage: 'Dit kan het gevolg zijn van een of meer van de volgende redenen:',
            suggestedActions: [
                'Deze ${jobSingularLowerAlias} heeft of geen ${bookingPluralLowerAlias} binnen het geselecteerde datumbereik',
                'De ${jobSingularLowerAlias} duplicatiewachtrij is vol',
                'U bent onvoldoende gemachtigd voor de geselecteerde ${bookingPluralLowerAlias}, aangezien een ${jobSingularLowerAlias} dupliceren vereist dat ${bookingPluralLowerAlias} in de achtergrond wordt aangemaakt.'
            ],
            contactMessage: 'Vernieuw de pagina en probeer het opnieuw, of neem contact op met uw beheerder.',
            okText: 'Ok'
        },
        duplicateRoleGroupErrorPrompt: {
            title: 'Fout tijdens aanmaken ${rolerequestPluralLowerAlias}',
            errorMessage: 'U bent onvoldoende gemachtigd om deze ${rolerequestPluralLowerAlias} aan te maken.',
            contactMessage: 'Vernieuw de pagina en probeer het opnieuw, of neem contact op met uw beheerder.',
            okText: 'Opnieuw proberen',
            cancelText: 'Annuleren'
        },
        deleteOperationLogPrompt: {
            title: 'Tijdens deze activiteit aangemaakte items verwijderen?',
            shouldDeleteQuestion: 'Wilt u tijdens deze activiteit aangemaakte items permanent verwijderen? Als er nieuwe taken zijn aangemaakt, worden alle voor deze taken aangemaakte boekingen, scenario’s of rollen ook verwijderd.',
            checkboxText: 'Tijdens deze activiteit gecreëerde items verwijderen',
            cancelMessage: 'Items behouden',
            confirmMessage: 'Items verwijderen'
        },
        confirmMassDuplicatePrompt: {
            title: 'Aan het dupliceren',
            message: 'Uw activiteit is in de wachtrij geplaatst. U kunt de status ervan volgen in het activiteitenlog.',
            closeText: 'OK'
        },
        updateSeriesBookingPrompt: {
            message: 'Deze boekingen worden verwijderd en opnieuw aangemaakt met bijgewerkte gegevens.',
            okText: 'Doorgaan',
            cancelText: 'Annuleren'
        }
    },
    comments: {
        editedFlag: 'bewerkt',
        editButtonLabel: 'Bewerken',
        deleteButtonLabel: 'Verwijderen',
        confirmEditButtonLabel: 'Bijwerken',
        cancelButtonLabel: 'Annuleren',
        createButtonLabel: 'Opmerking toevoegen',
        createPlaceholder: 'Begin een opmerking in te voeren',
        showMoreButtonLabel: 'Meer opmerkingen tonen'
    },
    navigation: {
        title: 'Help',
        contactSupport: 'Contact opnemen met support',
        helpPageLink: 'Help-documentatie',
        keyboardShortcuts: 'Sneltoetsen',
        legend: 'Legenda',
        operationLogButtonLabel: 'Activiteitenlog',
        notifications: 'Meldingen',
        settings: 'Instellingen',
        logout: 'Uitloggen'
    },
    skills: {
        noAddedSkills: 'Geen vaardigheden toegevoegd',
        noRecommendations: 'Geen nieuwe aanbevelingen',
        recommendationTitle: 'Aanbevelingen',
        mySkillsLabel: 'NL_My skills_NL',
        approvalRequestsLabel: 'NL_Approval requests_NL',
        approvalRequestSent: 'NL_Approval request sent_NL',
        noSkillPendingRequestsLabel: 'NL_No skill update requests_NL',
        noSkillApprovalHistoryLabel: 'NL_No skill approval historic requests_NL',
        skillApprovalHistoryLabel: 'NL_Historic requests are automatically removed after 1 year_NL',
        skillTagLabel: 'Tag',
        skillCategoryLabel: 'Categorieën',
        defaultSelectedSection: 'Alle vaardigheidstypen',
        noMatchingSkillsText: 'Geen overeenkomende vaardigheden gevonden.',
        searchPlaceholder: 'Zoeken naar een vaardigheid',
        saveButtonLabel: 'Vaardigheden toevoegen',
        cancelButtonLabel: 'Annuleren',
        headerTitle: 'Vaardigheden toevoegen',
        primarySaveButtonLabel: 'Opslaan',
        skillsToAddSectionName: 'Toe te voegen vaardigheden',
        addSkillsButtonLabel: 'Vaardigheden toevoegen',
        editSkillsButtonLabel: 'Vaardigheden bewerken',
        skillsSectionTitle: 'Vaardigheden',
        expandAllCaption: 'Alles samenvouwen',
        collapseAllCaption: 'Alles uitvouwen',
        singularSkillString: 'vaardigheid',
        pluralSkillsString: 'vaardigheden',
        markDeletedMessage: 'Gemarkeerd voor verwijdering. Wordt verwijderd wanneer u de wijzigingen opslaat.',
        cancelDeletionMessage: 'Verwijdering annuleren',
        skillLevelDeletedMessage: 'Het vaardigheidsniveau is verwijderd voor deze vaardigheid. Er moet een nieuw niveau worden ingesteld.',
        validationRequiredText: 'is vereist',
        validationLessThanText: 'mag niet minder zijn dan',
        validationGreaterThanText: 'mag niet meer zijn dan',
        validationIntegerNumberText: 'moet een heel getal zijn',
        maxCharactersPrefix: 'Maximaal aantal',
        maxCharactersSuffix: 'tekens',
        tagsPrefixText: 'Tags:',
        markedForDeletionMessage: 'Gemarkeerd voor verwijdering. Wordt verwijderd wanneer u de wijzigingen opslaat.',
        deleteLabel: 'Verwijderen ${skillName}',
        cancelDeletionSkillLabel: 'Verwijdering annuleren van ${skillName}',
        noValueMessage: 'Geen ${fieldInfoAlias} ingesteld',
        insufficientPermissionsToEditSuffix: 'Onvoldoende machtigingen om te bewerken',
        searchSkillFilterCascaderPlaceholder: 'NL_Select skills and levels_NL',
        noManagerToApproveMessage:'NL_You do not have a manager to approve your skill updates_NL'
    },
    pages: {
        plannerPage: 'Plannen',
        adminSettings: 'Instellingen',
        jobsPage: 'Taken',
        talentProfile: 'Talentprofiel',
        translation: 'Mijn profiel',
        report: 'Rapport',
        logout: 'Uitloggen',
        collapseText: 'Uitvouwen',
        expandText: 'Samenvouwen',
        errorMessages: {
            goBackText: 'Ga terug naar uw laatste pagina',
            goToText: 'Ga naar',
            errorCodeMessagePrefix: 'Fout',
            defaultHeaderText: 'Er is iets fout gegaan',
            closeText: 'Sluiten',
            '401': {
                headerText: 'Niet-geautoriseerd',
                message: 'Uw sessie is verlopen. Log opnieuw in.'
            },
            '403': {
                headerText: 'Toegang geweigerd.',
                message: 'Onvoldoende gemachtigd.'
            },
            '404': {
                headerText: 'Onze excuses.',
                message: 'We kunnen de pagina die u zoekt helaas niet vinden.'
            },
            error: {
                headerText: 'Onze excuses.',
                message: 'Er lijkt iets mis te zijn gegaan.',
                errorCodeMessage: 'Er is een probleem opgetreden. Probeer de pagina opnieuw te laden.'
            },
            calculateFteError: {
                headerText: 'Kan FTE niet berekenen',
                message: 'Selecteer een geldige FTE-referentie-agenda voor deze taak.'
            }
        }
    },
    manageEntityLookupWindow: {
        searchForLabel: 'Zoek naar een',
        showLessLabel: 'Minder tonen',
        showMoreLabels: {
            prefix: 'Tonen',
            suffix: 'meer'
        },
        noResultsMessagePrefix: 'Nee',
        noResultsMessageSuffix: 'is gevonden met deze naam.'
    },
    contextualMenu: {
        createEntity: 'Aanmaken',
        editEntity: 'Bewerken',
        deleteEntity: 'Verwijderen',
        copyEntity: 'Kopiëren',
        rollForwardEntity: 'Dupliceren',
        cutEntity: 'Knippen',
        pasteEntity: 'Plakken',
        clearEntity: 'Wissen',
        setDateRange: 'Datumbereik instellen',
        loadingCaption: '...laden',
        restart: 'Opnieuw starten',
        archive: 'Archiveren',
        reject: 'Afwijzen',
        makeLive: 'Live gaan',
        submitRequest: 'Aanvraag indienen',
        rollForwardTooltipText: 'Kopieer de geselecteerde ${bookingEntityAlias} naar een andere ${jobEntityAlias} of datum',
        unassignResource: 'Niet-toegewezen formulier ${rolerequestSingularLowerAlias}',
        createCriteriaRole: '${rolerequestSingularCapitalAlias} aanmaken op vereisten',
        createRoleByName: '${rolerequestSingularCapitalAlias} aanmaken op naam',
        movePendingResources: 'In behandeling verplaatsen ${resourcePluralLowerAlias}',
        removePendingResources: 'In behandeling verwijderen ${resourcePluralLowerAlias}',
        movePendingFTEs: 'FTE’s die worden behandeld verplaatsen',
        removePendingFTEs: 'FTE’s die worden behandeld verwijderen',
        manageBudget: 'Budget beheren',
        saveAsTemplate: 'Opslaan als sjabloon',
        showInViewLabel: 'Tonen in ${pluralViewNameAlias} weergave'
    },
    detailsPane: {
        paneLabel: 'paneel',
        inModalLabel: 'paneel in venster',
        showPanePrefixLabel: 'Tonen',
        showPaneSuffixLabel: 'paneel',
        suggestionsSingular: 'Suggestie',
        suggestionsPlural: 'Suggesties'
    },
    blankValues: {
        notFoundString: 'niet gevonden',
        dateNotFoundString: 'Datum niet gevonden',
        noChargeTypeSetString: 'Geen factureringstype ingesteld',
        unspecifiedString: 'niet-gespecificeerd',
        noString: 'Nee',
        setString: 'instellen'
    },
    hotKeysHelpWindow: {
        generalLabel: 'Algemeen',
        helpWindowTitle: 'Sneltoetsen',
        bookingsLabel: '${bookingPluralCapitalAlias}',
        jobsLabel: '${jobPluralCapitalAlias}',
        resourcesLabel: '${resourcePluralCapitalAlias}',
        rolesLabel: '${rolerequestPluralCapitalAlias}',
        dateManipulationLabel: 'Datum manipulatie',
        menusLabel: 'Menu’s',
        filtersLabel: 'Filters',
        viewOptionsLabel: 'Opties weergeven',
        createLabel: 'Aanmaken',
        helpLabel: 'Help',
        helpDocumentationLabel: 'Help-documentatie',
        orLabel: 'OF',
        plannerPage: {
            addLabel: 'Toevoegen',
            editLabel: 'Bewerken',
            cutLabel: 'Knippen',
            copyLabel: 'Kopiëren',
            pasteLabel: 'Plakken',
            deleteLabel: 'Verwijderen',
            restartLabel: 'Opnieuw starten',
            archiveLabel: 'Archiveren',
            rejectLabel: 'Afwijzen',
            makeLiveLabel: 'Live gaan',
            submitRequestLabel: 'Aanvraag indienen',
            expandDateRangeLabel: 'Datumbereik samenvouwen',
            reduceDateRangeLabel: 'Datumbereik reduceren',
            setRangeTo5DayLabel: 'Datumbereik instellen op 5 dagen',
            setRangeTo7DaysLabel: 'Datumbereik instellen op 7 dagen',
            setRangeTo10DaysLabel: 'Datumbereik instellen op 10 dagen',
            setRangeTo2WeekLabel: 'Datumbereik instellen op 2 weken',
            setRangeTo4WeeksLabel: 'Datumbereik instellen op 4 weken',
            setRangeTo6WeeksLabel: 'Datumbereik instellen op 6 weken',
            setRangeTo2MonthsLabel: 'Datumbereik instellen op 2 maanden',
            setRangeTo3MonthsLabel: 'Datumbereik instellen op 3 maanden',
            setRangeTo6MonthsLabel: 'Datumbereik instellen op 6 maanden',
            setRangeTo1YearLabel: 'Datumbereik instellen op 1 jaar',
            goToTodayLabel: 'Ga naar vandaag',
            addMenuLabel: 'Menu Toevoegen',
            editMenuLabel: 'Menu Bewerken',
            viewMenuLabel: 'Menu Weergeven',
            addJobLabel: 'Een ... toevoegen ${jobSingularLowerAlias}',
            findResourcesLabel: 'Zoeken ${resourcePluralLowerAlias}',
            defaultDensityLabel: 'Dichtheid display instellen op standaard',
            mediumDensityLabel: 'Dichtheid display instellen op gemiddeld',
            expandedDensityLabel: 'Dichtheid display instellen op samengevouwen',
            helpWindowLabel: 'Help-venster',
            openLegendLabel: 'Legenda kleurenschema openen',
            showHideWeekendsLabel: 'Weekenden tonen/verbergen',
            showHidePotentialConflictsLabel: 'Potentiële conflicten tonen/verbergen',
            addRoleByName: '${rolerequestSingularCapitalAlias} toevoegen op naam',
            addRoleByRequirements: '${rolerequestSingularCapitalAlias} toevoegen op vereisten',
            editRoleByNameLabel: '${rolerequestSingularCapitalAlias} op naam bewerken',
            editRoleByCriteriaLabel: '${rolerequestSingularCapitalAlias} op vereisten bewerken',
            rollForwardLabel: 'Dupliceren',
            splitBookingLabel: 'Splitsen ${bookingSingularLowerAlias}',
            movePendingResources: 'Resources in behandeling verplaatsen',
            removePendingResources: 'Resources in behandeling verwijderen',
            movePendingFTEs: 'FTE’s in behandeling verplaatsen',
            removePendingFTEs: 'FTE’s in behandeling verwijderen',
            showInViewLabel: 'Tonen in ${jobPluralLowerAlias}/${resourcePluralLowerAlias} weergave',
            dragToSelect: 'Slepen om te selecteren'
        },
        jobsPage: {
            addLabel: 'Toevoegen',
            editLabel: 'Bewerken',
            deleteLabel: 'Verwijderen',
            addMenuLabel: 'Menu toevoegen',
            editMenuLabel: 'Menu bewerken',
            compactDensityLabel: 'Dichtheid display instellen op compact',
            defaultDensityLabel: 'Dichtheid display instellen op standaard',
            expandedDensityLabel: 'Dichtheid display instellen op samengevouwen',
            helpWindowLabel: 'Help-venster'
        },
        roleInboxPage: {
            movePendingResources: 'Resources in behandeling verplaatsen',
            removePendingResources: 'Resources in behandeling verwijderen',
            makeLiveLabel: 'Live gaan',
            submitRequestLabel: 'Aanvraag indienen',
            restartLabel: 'Opnieuw starten',
            rejectLabel: 'Afwijzen',
            deleteLabel: 'Verwijderen',
            archiveLabel: 'Archiveren',
            addRoleByName: '${rolerequestSingularLowerAlias} toevoegen op naam',
            addRoleByRequirements: '${rolerequestSingularLowerAlias} toevoegen op vereisten',
            editRoleByNameLabel: '${rolerequestSingularCapitalAlias} op naam bewerken',
            editRoleByCriteriaLabel: '${rolerequestSingularCapitalAlias} op vereisten bewerken',
            helpWindowLabel: 'Help-venster',
            publishToMarketplaceLabel: 'Publiceren op ${marketplaceAlias}'
        },
        adminSettingsPage: {
            addNewItemLabel: 'Nieuw item toevoegen',
            helpWindowLabel: 'Help-venster'
        }
    },
    validationMessages: {
        unableToSaveChanges: 'Kan wijzigingen niet opslaan Bekijk gemarkeerde fouten.',
        mandatoryFieldsNotCompleted: 'Verplichte velden moeten worden voltooid',
        formHasErrors: 'Dit formulier bevat fouten',
        activeUserText: 'is inactief. Stel in op een actieve of niet-toegewezen resource om wijzigingen op te slaan',
        fieldMandatoryText: 'Dit veld is verplicht',
        mandatoryText: 'is verplicht',
        minimumText: 'Minimum',
        maximiumText: 'Maximum',
        maxCharactersPrefix: 'Maximum',
        maxCharactersSuffix: 'tekens',
        selectValidText: 'Selecteer een geldige',
        invalidNamePrefix: 'Ongeldig',
        invalidNameSuffix: 'naam',
        integerTypeText: 'is van het type geheel getal',
        maxSelectedYearText: 'Geselecteerde jaar moet voor 10000 zijn',
        minSelectedYearText: 'Geselecteerde jaar moet na 0000 zijn',
        startDateInvalid: 'Ongeldige begindatum opgegeven',
        jobEndBeforeJobStart: 'Einde mag niet voor begin zijn',
        fteMaxValidationText: 'Totaal aantal FTE’s overschrijdt aangevraagde aantal',
        fteString: 'FTE'
    },
    attachmentsMessages: {
        uploadButtonLabel: 'Documenten uploaden',
        deleteButtonLabel: 'Verwijderen',
        fileTooLargeLabel: 'Document niet geüpload: Bestand is te groot',
        fileTypeForbiddenLabel: 'Document niet geüpload: Bestandstype is niet toegestaan',
        noFilesUploadedLabel: 'U heeft geen documenten geüpload',
        uploadsLimitReachedLabel: 'Documentlimiet bereikt: verwijder documenten om meer te uploaden',
        allowedFormatsLabel: 'Documenten kunnen de volgende indelingen hebben: ${formattedAcceptedFileTypes}',
        maxFileSizeLabel: 'Er geldt een maximale bestandsgrootte van ${defaultMaxFileSizeMb}MB voor ieder document',
        maxUploadsAllowed: 'U mag maximaal ${defaultMaxUploadsAllowed} documenten uploaden'
    },
    treeSelectionMessages: {
        chargeMode: 'Laadmodus',
        revenue: 'Opbrengsten',
        cost: 'Kosten',
        profit: 'Winst',
        dateRange: 'Datumbereik',
        timeAllocation: 'Toewijzing van tijd',
        selectFieldsCaption: 'Velden selecteren',
        addButtonCaption: 'Toevoegen',
        historyFieldsSuffix: '(datum overschrijven)'
    },
    contextualDropdown: {
        detailsLabel: 'details',
        editLabel: 'Bewerken',
        duplicateLabel: 'Dupliceren',
        viewLabel: 'Weergeven',
        newLabel: 'Nieuw',
        deleteLabel: 'Verwijderen',
        roleByName: '${roleSingularCapitalAlias} op naam',
        roleByRequirements: '${roleSingularCapitalAlias} op vereisten',
        newRoleLabel: 'Nieuw ${roleSingularLowerAlias}',
        editDetailsLabel: 'Details bewerken',
        archiveLabel: 'Archiveren',
        restartLabel: 'Opnieuw starten',
        rejectLabel: 'Afwijzen',
        makeLiveLabel: 'Live gaan',
        submitRequestLabel: 'Aanvraag indienen',
        createLabel: 'Aanmaken',
        unassignLabel: 'Niet-toegewezen formulier',
        createBookingEllipsisLabel: '${bookingSingularCapitalAlias} aanmaken...',
        createRoleByNameEllipsisLabel: '${rolerequestSingularCapitalAlias} op naam aanmaken...',
        editEllipsisLabel: 'Bewerken...',
        goToProfileEllipsisLabel: 'Ga naar profiel',
        copyProfileUrlEllipsisLabel: 'Profiel-URL kopiëren',
        movePendingFTE: 'FTE’s in behandeling verplaatsen',
        removePendingFTE: 'FTE’s in behandeling verwijderen',
        movePendingResourcesLabel: 'In behandeling verplaatsen ${resourcePluralLowerAlias}',
        removePendingResourcesLabel: 'In behandeling verwijderen ${resourcePluralLowerAlias}',
        manageBudgetLabel: 'Budget beheren',
        saveAsTemplateLabel: 'Opslaan als sjabloon',
        publishToMarketplaceLabel: 'Publiceren op ${marketplaceAlias}',
        editRolePublicationButtonLabel: '${rolerequestSingularLowerAlias} publicatie bewerken',
        removeRolePublicationButtonLabel: '${rolerequestSingularLowerAlias} publicatie verwijderen',
        detailsJobLabel: '${jobSingularCapitalAlias} details',
        editJobLabel: 'Bewerken ${jobSingularLowerAlias}',
        duplicateJobLabel: 'Dupliceren ${jobSingularLowerAlias}',
        viewRoleRequestGroupLabel: 'Vergelijken ${rolerequestgroupSingularLowerAlias}',
        newRoleRequestGroupLabel: 'Aanmaken ${rolerequestgroupSingularLowerAlias}',
        detailsResourceLabel: '${resourceSingularCapitalAlias} details',
        openLabel: 'Openen',
        moreOptionsButtonLabel: 'Meer opties'
    },
    progressRolesWindow: {
        totalText: 'Alle wijzigingen aan taak',
        cancelText: 'Annuleren',
        progressRoleLabel: 'Voortgang rol',
        jobLabel: 'Taak',
        roleLabel: 'Rol',
        dateRangeLabel: 'Datumbereik',
        budgetLabel: 'Budget',
        makeLive: {
            selectMessage: '${rolePluralAlias} selecteren om door te gaan naar ${bookingPluralAlias}',
            title: 'Live gaan',
            submitText: 'Live gaan'
        },
        submitRequest: {
            selectMessage: '${rolePluralAlias} selecteren om aan te vragen als ${bookingPluralAlias}',
            title: 'Aanvraag indienen',
            submitText: 'Aanvraag indienen'
        }
    },
    progressRoleErrors: {
        alreadyLiveMsg: 'Is al live',
        noPermissionsMsg: 'Onvoldoende gemachtigd'
    },
    rejectRolesWindow: {
        title: 'Afwijzen',
        submitText: 'Aanvraag afwijzen',
        cancelText: 'Annuleren',
        rejectErrorMessage: 'Er is een probleem opgetreden. De actie kan niet worden voltooid',
        buttonLabel: 'Sluiten',
        rejectReasonText: 'Selecteer reden voor afwijzen van deze aanvraag',
        errorDialogTitle: 'Fout tijdens overgang rol',
        customReasonPlaceholderText: 'Schrijf aangepaste reden voor het afwijzen van deze rol',
        jobLabel: 'Taak',
        roleLabel: 'Rol',
        dateRangeLabel: 'Datumbereik',
        budgetLabel: 'Budget',
        statusLabel: 'Status'
    },
    carousel: {
        defaultRoleName: 'Nieuw ${rolerequestSingularCapitalAlias}',
        ungrouped: 'Geen ${rolerequestgroupSingularLowerAlias} ingesteld'
    },
    rollForwardDialog: {
        title: 'Dupliceren ${bookingEntityAlias}',
        submitText: 'Aanmaken ${bookingEntityAlias}',
        cancelText: 'Annuleren',
        duplicateBooking: '${bookingSingularCapitalAlias} gedupliceerd',
        duplicateBookings: '${bookingPluralLowerAlias} gedupliceerd',
        forwardOptions: {
            alertMessage: 'Aan het kopiëren ${noOfBooking} ${bookingEntityAlias}',
            destinationStartDateLabel: 'Begindatum bestemming',
            destinationStartDateLabelError: 'Begindatum bestemming is verplicht',
            destinationStartDateLabelErrorDescription: 'Relatieve posities van geselecteerde ${bookingEntityAlias} worden bewaard na dupliceren.',
            destinationJobLabel: 'Bestemming ${jobSingularAlias}',
            destinationJobError: 'Bestemming ${jobSingularAlias} is verplicht',
            destinationBookingTypeLabel: '${bookingEntityAlias} type bestemming',
            destinationBookingTypeError: '${bookingEntityAlias} type bestemming is verplicht',
            destinaltionJobExplanation: '${jobSingularAlias} bereiken bestemming worden dienovereenkomstig aangepast',
            offsetExplanation: 'Vanaf het begin van de eerst geselecteerde ${bookingEntityAlias}',
            editBookingLabel: '${bookingEntityAlias} bewerken na dupliceren',
            editBookingDescription: 'Opent het dialoogvenster Bewerken om extra wijzigingen door te voeren in de nieuwe ${bookingEntityAlias}',
            valuePostfix: '${bookingEntityAlias}',
            keepBookingTypeText: 'Houd ${bookingEntityAlias} type zoals het is',
            onPrefix: 'Aan',
            inPrefix: 'In'
        }
    },
    repeatBookingDialog: {
        createRepeatBooking: {
            title: 'Herhaling instellen',
            submitText: 'Opslaan',
            cancelText: 'Annuleren',
            repeatEvery: 'Herhaal elke',
            repeatUntil: 'Tot',
            noRepeatText: 'Wordt niet herhaald',
            positiveWholeNumberErrorMessage: 'Voer een positief heel getal in'
        },
        editRepeatBooking: {
            title: 'Welke terugkerende boekingen bewerken?',
            selectedOnly: 'Alleen geselecteerde boeking',
            selectedAndFuture: 'Geselecteerde en volgende boekingen',
            allBookings: 'Alle boekingen in de serie',
            actionLabel: 'Serie bewerken',
            singleBookingMessage: 'Je bewerkt een enkele boeking in een serie.',
            singleAndFutureBookingsMessage: 'Je bewerkt deze en alle volgende boekingen in een terugkerende reeks.',
            allBookingsMessage: 'Je bewerkt alle boekingen in een terugkerende serie.',
            partOfSeriesMessage: 'Deze boeking maakt deel uit van een terugkerende serie.',
            updateFailureMessage: 'Boekingsserie niet bijgewerkt.',
            bulkBookingMessage: 'Je bewerkt afzonderlijke boekingen in een terugkerende serie.',
            editedSingleBookingMessage: 'Deze boeking maakt deel uit van een terugkerende serie, maar is apart bewerkt. Sommige details kunnen afwijken.'
        },
        deleteRepeatBooking: {
            title: 'Welke terugkerende boekingen verwijderen?',
            cannotBeUndone: 'Dit kan niet ongedaan worden gemaakt.',
            selectedOnly: 'Alleen geselecteerde boeking',
            selectedAndFuture: 'Geselecteerde en volgende boekingen',
            allBookings: 'Alle boekingen in de serie'
        },
        doesNotRepeatText: 'wordt niet herhaald',
        repeatsEveryText: 'herhaalt elke',
        on: 'op',
        starting: 'start',
        until: 'tot',
        intervalText: {
            day: 'dag',
            days: 'dagen',
            week: 'week',
            weeks: 'weken',
            month: 'maand',
            months: 'maanden'
        },
        dayOfWeekText: {
            0: 'Zondag',
            1: 'Maandag',
            2: 'Dinsdag',
            3: 'Woensdag',
            4: 'Donderdag',
            5: 'Vrijdag',
            6: 'Zaterdag'
        },
        dayText: 'dag',
        confirmRepeatBookingPrompt: {
            title: 'Terugkerende boekingen maken',
            message: 'Je bewerking is in de wachtrij geplaatst. Je kunt de status ervan volgen in het Bewerkings-logboek.',
            closeText: 'OK'
        },
        auditTrail: {
            recurringIntervalCreated: 'Herhalingsserie gemaakt tot Herhaal elke',
            recurringIntervalEdited: 'Herhalingsinterval bewerkt tot Herhaal elke',
            recurrentSeries: 'Terugkerende series'
        },
        seriesText: 'serie'
    },
    jobDuplicateDialog: {
        title: 'Dupliceren ${jobSingularLowerAlias}',
        submitText: 'Aanmaken ${bookingPluralLowerAlias}',
        cancelText: 'Annuleren',
        newPrefix: 'Nieuw',
        newEntityLabel: '${newPrefix} ${tableAlias}',
        searchToSelect: 'Zoeken om te selecteren',
        changeRangeToIncludeBookingsString: 'Bereik wijzigen en alle ${bookingPluralLowerAlias} toevoegen?',
        forwardOptions: {
            destinationStartDateLabel: 'Begindatum bestemming',
            destinationStartDateLabelError: 'Begindatum bestemming is verplicht',
            destinationStartDateLabelErrorDescription: 'Relatieve posities van ${bookingPluralLowerAlias} worden bewaard na dupliceren.',
            outOfRangeEntityExplanation: 'Het lijkt erop dat sommige ${bookingPluralLowerAlias} buiten de begin- en einddatum liggen van deze ${jobSingularLowerAlias}.',
            rolesPositionWarning: 'Bestaande ${rolerequestPluralLowerAlias} blijven op de geselecteerde ${jobSingularLowerAlias} en worden niet gedupliceerd.',
            jobRangeLabelError: 'Datumbereik voor bestemming ${jobSingularLowerAlias} is verplicht',
            maximumJobRangeMessage: '${jobSingularCapitalAlias} datumbereik moet binnen 24 maanden vanaf nu liggen',
            dateRangeValueMandatory: 'Dit veld is verplicht',
            destinationJobLabel: 'Bestemming ${jobSingularLowerAlias}',
            destinationJobError: 'Bestemming ${jobSingularLowerAlias} is verplicht',
            destinationBookingTypeLabel: '${bookingSingularLowerAlias} type bestemming',
            destinaltionJobExplanation: '${jobSingularLowerAlias} bereiken bestemming worden dienovereenkomstig aangepast',
            offsetExplanation: 'Vanaf het begin van de eerste ${bookingSingularLowerAlias} binnen het datumbereik',
            dateRangeForJobLabel: 'Datumbereik voor geselecteerde ${jobSingularLowerAlias}',
            selectedJobLabel: 'Geselecteerde ${jobSingularLowerAlias}',
            destinationBookingTypeError: '${bookingSingularLowerAlias} type bestemming is verplicht',
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Houd ${bookingEntityAlias} type zoals het is',
            onPrefix: 'Aan',
            inPrefix: 'In'
        }
    },
    //mass duplicate settings
    massDuplicateJobs: {
        filterTitle: 'Filters toevoegen',
        placeholderLabel: 'Zoeken op ${jobAlias} naam...',
        modalTitle: 'Selecteer een ${jobAlias}',
        resultSingular: '${rowCount} resultaat',
        resultPlural: '${rowCount} resultaten',
        upToResults: 'Tot ${rowCount} resultaten',
        massDuplicateJobsTitle: 'Gegevens dupliceren',
        massDuplicateJobsFieldTitle: 'Taken selecteren voor massaduplicatie',
        massDuplicateJobsSubLabel: '  ',
        saveButtonLabel: 'Taken dupliceren',
        cancelButtonLabel: 'Wissen',
        formHasErrorsMessage: 'Dit formulier bevat fouten',
        massDuplicateJobsInfoText: 'Filter de taken eruit die u wilt dupliceren.',
        massDuplicateJobsInfoTips1: '<b>Rollen</b> in de geselecteerde taken worden niet gedupliceerd',
        massDuplicateJobsInfoTips2: 'Relatieve datums van taken en boekingen worden bewaard, oftewel een boeking die 3 dagen \nna de begindatum van de taak start, wordt 3 dagen na de begindatum van de nieuwe taak aangemaakt.',
        massDuplicateJobsInfoTips3: 'Taken met een geldige <i>Volgende gerelateerde taak</i> <b>worden niet gedupliceerd</b>. Alleen de boekingen ervan worden \nnaar de gerelateerde taak gekopieerd.',
        massDuplicateJobsInfoTips4: 'Aan <b>inactieve resources</b> toegewezen boekingen worden gedupliceerd en blijven aan ze toegewezen',
        massDuplicateJobsTextNewBooking: 'Nieuwe boekingen aanmaken op basis van',
        massDuplicateJobsNewBookingTextBookings: 'Boekingen',
        massDuplicateJobsNewBookingTextActuals: 'Actuele gegevens',
        massDuplicateJobsNewBookingTipMessage: 'Maakt wekelijkse boekingen aan op basis van gegevens uit urenstaten',
        massDuplicateJobsTextDestinationBooking: 'Bestemming boekingstype',
        massDuplicateJobsDestinationOption1: 'Geplande boekingen',
        massDuplicateJobsDestinationOption2: 'Onbevestigde boekingen',
        massDuplicateJobsDestinationOption3: 'Boekingstype houden zoals het is',
        massDuplicateJobsDaterangeText: 'Taken en boekingen selecteren tussen',
        massDuplicateJobsNewJobsText: ' Nieuwe taken aanmaken',
        forwardOptions: {
            valuePostfix: '${bookingSingularLowerAlias}',
            keepBookingTypeText: 'Houd ${bookingEntityAlias} type zoals het is',
            chooseHowJobsCreatedTitle: 'Kiezen hoe taken en boekingen worden aangemaakt',
            chooseHowJobsCreatedTitleExtra: 'Taken met een <i>Volgende gerelateerde taak</i> hebben de offset van de begindatums van de nieuwe boeking van de begindatum \nvan de taak in dezelfde hoeveelheid als de oorspronkelijke taak',
            dateRangeForJobLabel: 'Taken die zijn gestart tussen',
            fieldMandatoryText: 'Dit veld is verplicht',
            createNewJobsLabel: 'Nieuwe taken aanmaken',
            inPrefix: 'In',
            onPrefix: 'Aan',
            inPrefixExtra: 'Vanaf het begin van de eerste boeking binnen het \ngeselecteerde datumbereik',
            onPrefixExtra: 'Nieuwe taken starten op deze datum. Boekingsposities \nworden relatief aan de startdatum aangehouden.',
            createNewBookingsLabel: 'Nieuwe boekingen aanmaken op basis van',
            bookings: 'Boekingen',
            actuals: 'Actuele gegevens',
            destinationBookingTypeLabel: 'Bestemming boekingstype',
            plannedBookings: 'Geplande boekingen',
            unconfirmedBookings: 'Onbevestigde boekingen',
            keepBookingType: 'Boekingstype houden zoals het is',
            nextRelatedJobsLabel: 'Alleen dubbele taken met een geldige Volgende gerelateerde taken',
            newJobNamesLabel: 'Nieuwe taaknamen',
            explanationNewJobNames: 'Als er voor de oorspronkelijke taak een Volgende gerelateerde taak is geselecteerd, wordt de taaknaam niet gewijzigd',
            newJobsNamesDefaultOption: 'Standaard',
            newJobsNamesDefaultOptionExample: 'Voorbeeld: Kopie van Aqua Audit 2023',
            newJobsNamesOriginalOption: 'Oorspronkelijke naam gebruiken',
            newJobsNamesReplaceOption: 'Vervangen',
            newJobsNamesReplaceWithOption: 'door',
            newJobsNamesReplaceOptionExample: 'Voorbeeld: Aqua Audit 2023 wordt Aqua Audit 2024',
            newJobsNamesCantFindTextToReplaceLabel: 'Als we de te vervangen tekst niet kunnen vinden, wordt de standaardnaam gebruikt (Kopie van...)'
        },
        massDuplicateJobsReviewTitle: 'Overzicht: ${totalNumberOfJobs} taken geselecteerd om uit te rollen',
        massDuplicateJobsReviewPoint1: '${totalNumberOfBookings} totaal aantal geselecteerde boekingen',
        massDuplicateJobsReviewPoint2: '${totalNumberOfConfirmedHours} bevestigde uren',
        massDuplicateJobsReviewPoint3: '${totalNumberOfUnconfirmedHours} onbevestigde uren',
        massDuplicateJobsReviewPoint4: '${totalNumberOfJobsWithoutNextJob} nieuwe taken worden aangemaakt',
        massDuplicateJobsReviewPoint5: '${totalNumberOfJobsWithNextJob} voor taken met een Volgende gerelateerde taak worden alleen de boekingen of actuele gegevens uitgerold',
        massDuplicateJobsReviewSummaryButtonLabel: 'Overzicht vernieuwen'
    },
    roleGroupDuplicateDialog: {
        title: 'Dupliceren ${rolerequestgroupSingularLowerAlias}',
        submitText: 'Dupliceren ${rolerequestgroupSingularLowerAlias}',
        cancelText: 'Annuleren',
        forwardOptions: {
            scenarioNameLabel: '${rolerequestgroupSingularCapitalAlias} naam',
            destinationJobLabel: 'Bestemming ${jobSingularLowerAlias}',
            destinationJobError: 'Bestemming ${jobSingularLowerAlias} is verplicht',
            destinationStartDateLabel: 'Begindatum bestemming',
            destinationStartDateLabelError: 'Begindatum bestemming is verplicht',
            destinationStartDateLabelErrorDescription: 'Relatieve posities van ${rolerequestPluralLowerAlias} worden bewaard na dupliceren.',
            destinaltionStartDateExplanation: 'vanaf het begin van de eerste ${rolerequestSingularLowerAlias} binnen het ${rolerequestgroupSingularLowerAlias}',
            newRoleGroupDescriptionLabel: 'Beschrijving',
            onPrefix: 'Aan',
            inPrefix: 'In',
            scenarioNameError: 'Dit veld is verplicht'
        }
    },
    peopleFinderDialog: {
        createBookingText: 'Aanmaken ${bookingEntityAlias}',
        createRoleText: '${roleEntityAlias} aanmaken op naam',
        closeText: 'Sluiten',
        filterTitle: 'Filters toevoegen',
        infoToolTipText: 'Actief datumbereik in plan',
        refreshButtonLabel: 'Vernieuwen',
        profileUrlCopied: 'Profiel-URL gekopieerd',
        plannerPage: {
            title: 'Zoeken ${resourceEntityAlias}',
            emptyFinderText: 'Selecteer criteria om ... te vinden ${resourceEntityAlias}',
            resourceFoundInRangeText: '${resourcePluralCapitalAlias} gevonden tijdens <bold>${startDate} - ${endDate}</bold> wordt hier weergegeven.',
            summaryText: '<bold>${resourceCount} resultaten</bold> voor bereik'
        },
        profilePage: {
            title: 'Ander profiel bekijken',
            emptyFinderText: 'Criteria selecteren om profiel te vinden',
            summaryText: '<bold>${resourceCount} resultaten</bold>'
        }
    },
    jobFilterDialog: {
        filterTitle: 'Filters toevoegen',
        placeholderLabel: 'Zoeken op ${jobAlias} naam...',
        modalTitle: 'Selecteer een ${jobAlias}',
        resultSingular: '${rowCount} resultaat',
        resultPlural: '${rowCount} resultaten',
        upToResults: 'Tot ${rowCount} resultaten'
    },
    operationsLogDialog: {
        heading: 'Activiteitenlog',
        dataGridExplanations: 'In dit log worden op uw locatie uitgevoerde duplicaties opgeslagen'
    },
    expandedOperationsLogDialog: {
        heading: 'Duplicatie van taak',
        jobsRollForwarded: 'uitgerolde taken',
        bookingsCreated: 'totaal aantal aangemaakte boekingen',
        newJobsCreated: 'nieuwe aangemaakte taken',
        jobsDuplicated: 'voor taken met een Volgende gerelateerde taak waren alleen de boekingen of actuele gegevens uitgerold'
    },
    actionBarWithFooterButtons: {
        saveButtonLabel: 'Wijzigingen opslaan',
        cancelButtonLabel: 'Annuleren',
        formHasErrorsMessage: 'Dit formulier bevat fouten'
    },
    cMeSection: {
        title: 'C-me eigenschappen'
    },
    educationSection: {
        formConfiguration: {
            education: 'Opleiding',
            dialogConfig: {
                saveText: 'Opslaan',
                cancelText: 'Annuleren'
            },
            institutionLabel: 'Instelling',
            institutionError: 'Instelling is verplicht',
            fieldLabel: 'Veld',
            fieldError: 'Veld is verplicht',
            degreeLabel: 'Diploma',
            noResultsFoundMessage: 'Geen diploma-opties ingesteld door uw beheerder',
            startDateLabel: 'Begindatum',
            endDateLabel: 'Einddatum',
            endFieldError: 'Einddatum mag niet voor begindatum liggen',
            endDateDescription: 'Einddatum kan verwijzen naar verwachte maand/jaar van afstuderen',
            detailsLabel: 'Details',
            addInstitutionPlaceholder: 'Instelling toevoegen',
            addFieldPlaceholder: 'Veld toevoegen',
            addDegreePlaceholder: 'Diploma toevoegen',
            addDetailsPlaceholder: 'Details toevoegen',
            maxCharErrorPrefix: 'Maximaal',
            maxCharErrorSuffix: 'tekens zijn toegestaan'
        },
        addEducationButtonLabel: 'Opleiding toevoegen',
        editEducationButtonLabel: 'Opleiding bewerken'
    },
    experienceSection: {
        formConfiguration: {
            experience: 'Ervaring',
            companyLabel: 'Bedrijf',
            roleLabel: 'Functie',
            locationLabel: 'Locatie',
            startDateLabel: 'Begindatum',
            endDateLabel: 'Einddatum',
            detailsLabel: 'Details',
            endDateFieldError: 'Einddatum mag niet voor begindatum liggen',
            roleError: 'Functie is verplicht',
            companynameError: 'Bedrijfsnaam is verplicht',
            maxCharErrorPrefix: 'Maximaal',
            maxCharErrorSuffix: 'tekens zijn toegestaan',
            addDetailsPlaceholder: 'Details toevoegen',
            addCompanyPlaceholder: 'Bedrijf toevoegen',
            addRolePlaceholder: 'Functie toevoegen',
            addLocationPlaceholder: 'Locatie toevoegen',
            dialogConfig: {
                saveText: 'Opslaan',
                cancelText: 'Annuleren'
            }
        },
        addExperienceButtonLabel: 'Ervaring toevoegen',
        editExperienceButtonLabel: 'Ervaring bewerken'
    },
    cookieConsentBanner: {
        accept: 'Accepteren',
        title: 'Deze website maakt gebruik van cookies',
        info: 'We gebruiken cookies om uw ervaring te verbeteren en voor analytische doeleinden. Door op "Accepteren" te klikken, stemt u in met het gebruik van cookies. Voor meer informatie over hoe we cookies gebruiken en meer informatie over uw privacyrechten, raadpleegt u ons <cookie>Cookiebeleid</cookie> en <privacy>Privacybeleid</privacy>.'
    },
    banners: {
        maintenanceStatusBanner: {
            dismissLabel: 'Annuleren',
            singleDayInfo: '<bold>Gepland onderhoud op ${startDate}, van ${startTime} tot ${endTime}</bold>. Retain is tijdelijk niet beschikbaar, omdat we onze site verbeteren.',
            multiDayInfo: '<bold>Gepland onderhoud op ${startDate}, van ${startTime} op ${endDate}, om ${endTime}</bold>. Retain is tijdelijk niet beschikbaar, omdat we onze site verbeteren.'
        },
        jobsPageBookmarkBanner: {
            dismissLabel: 'NL_Dismiss_NL',
            description: 'NL_We have changed the URL of this page. Update your bookmark if needed_NL'
        }
    },
    lastLoginSection: {
        minutesAgo: '${timeAgo}m geleden',
        hoursAgo: '${timeAgo}u geleden',
        daysAgo: '${timeAgo}d geleden',
        now: 'Nu',
        lastLoginLabel: 'Laatste keer ingelogd'
    },
    longRunningTaskBanners: {
        duplicateJob: {
            processing: {
                title: '\'${jobDescription}\' ${progress}% dupliceren voltooid...',
                content: {
                    message: 'We brengen u op de hoogte als we klaar zijn.',
                    progressSeparator: 'van'
                }
            },
            completed: {
                title: 'Uw ${jobSingularLowerAlias} is klaar',
                content: {
                    message: '\'${jobDescription}\' succesvol gedupliceerd.'
                }
            },
            failed: {
                title: 'Dupliceren mislukt ${jobSingularLowerAlias}',
                content: {
                    message: '\'${jobDescription}\' dupliceren mislukt.',
                    retry: 'Opnieuw proberen'
                }
            },
            queued: {
                title: 'Uw dubbele ${jobSingularLowerAlias} is in de wachtrij geplaatst',
                content: {
                    message: 'We brengen u op de hoogte als we klaar zijn.'
                }
            },
            multiple: {
                title: 'Uw dubbele ${jobSingularLowerAlias} is in de wachtrij geplaatst',
                content: {
                    message: 'Meerdere activiteiten in de wachtrij geplaatst.',
                    button: 'Activiteitenlog weergeven'
                }
            }
        },
        longRunningjobIndicatorTooltipMessage: 'Er wordt momenteel een langlopende activiteit uitgevoerd voor deze ${entityAlias}.'
    },
    cMeProfiling: {
        aboutcMeColours: 'About C-me colours',
        about: 'NL_About_NL',
        cMeColourProfilingDialog: {
            dialogTitle: 'About C-me colour profiling',
            topMessageLine1: 'Human behaviours can be complicated to describe. We\'ve partnered with C-me, a behaviour profiling service that associates behaviours with colours. A resource\'s C-me data tells you about their preferred ways of doing things, expressed in the language of four colours.',
            topMessageLine2: 'We automatically update a resource\'s skills with traits from their most dominant colour.',
            redBox: {
                title: 'Red',
                boxList: [
                    'Action oriented',
                    'Assertive',
                    'Competitive',
                    'Decisive',
                    'Determined',
                    'Fast paced',
                    'Strategic'
                ]

            },
            yellowBox: {
                title: 'Yellow',
                boxList: [
                    'Dynamic presenter',
                    'Energetic',
                    'Flexible',
                    'Imaginative',
                    'Inspirational',
                    'Optimistic',
                    'Spontaneous'
                ]
            },
            greenBox: {
                title: 'Green',
                boxList: [
                    'Collaborative',
                    'Democratic',
                    'Diplomatic',
                    'Empathetic',
                    'Non-judgemental',
                    'Patient',
                    'Values driven'
                ]
            },
            blueBox: {
                title: 'Blue',
                boxList: [
                    'Analytical',
                    'Disciplined',
                    'Methodical',
                    'Organised',
                    'Precise',
                    'Systematic',
                    'Thorough'
                ]
            }
        }
    },
    summaryPage: {
        personalGreeting: 'Hallo',
        customiseButtonLabel: 'Aanpassen',
        doneButtonLabel: 'Gereed',
        arrangeWidgetsText: 'Klik en sleep widgets om ze te herschikken.',
        welcomeGreeting: 'Welkom bij Retain',
        summaryDurationOptionLabels: {
            4: 'Komende 4 weken',
            6: 'Komende 6 weken',
            12: 'Komende 12 weken'
        },
        widgets: {
            yourRequests: {
                title: 'Uw aanvragen',
                emptyStateMessage: 'Geen door u ingediende aanvragen voor deze periode'
            },
            plannedHours: {
                title: 'Geplande uren',
                emptyStateMessage: ''
            },
            ongoingJobs: {
                title: 'Doorlopend ${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            actionRequired: {
                title: '${rolerequestPluralCapitalAlias} nog uit te voeren',
                emptyStateMessage: 'Geen nog uit te voeren ${rolerequestPluralLowerAlias} in deze periode'
            },
            jobsOverBudgetDetails: {
                title: '${jobPluralCapitalAlias} over budget',
                emptyStateMessage: 'Geen doorlopend ${jobPluralLowerAlias} over budget'
            },
            chargeableUtilisation: {
                title: 'Factureerbaar gebruik',
                emptyStateMessage: ''
            },
            utilisation: {
                title: 'Gebruik',
                emptyStateMessage: ''
            },
            pendingRequests: {
                title: '${rolerequestPluralCapitalAlias} nog uit te voeren',
                subTitleUnit: 'uren',
                emptyStateMessage: ''
            },
            unassignedBookingsDetails: {
                title: 'Toewijzing geannuleerd ${bookingPluralLowerAlias}',
                emptyStateMessage: 'Geen toegewezen ${bookingPluralLowerAlias} in deze periode'
            },
            unassignedBookings: {
                title: 'Toewijzing geannuleerd ${bookingPluralLowerAlias}',
                subTitleUnit: '${jobPluralLowerAlias}',
                emptyStateMessage: ''
            },
            upcomingBookingsDetails: {
                title: 'Uw aanstaande ${bookingPluralLowerAlias}',
                emptyStateMessage: 'Geen ${bookingPluralLowerAlias} aan u toegewezen in deze periode'
            }
        },
        widgetDetailsTotalsUnit: {
            bookings: '${bookingPluralLowerAlias}',
            jobs: '${jobPluralLowerAlias}',
            hours: 'uren',
            planned: 'gepland',
            unconfirmed: 'onbevestigd',
            availability: 'beschikbaarheid',
            requests: 'aanvragen',
            rejected: 'Afgewezen',
            requested: 'Aangevraagd',
            draft: 'Concept',
            live: 'Live'
        },
        budgetConsumedText: 'Uitgegeven budget',
        pageTitle: 'Overzicht',
        configurationPane: {
            arrangeButtonLabel: 'Rangschikken',
            searchPlaceholder: 'Zoeken',
            openOnLoginLabel: 'Deze pagina openen tijdens inloggen',
            emptySearchMessage: 'Er zijn geen resultaten gevonden.',
            sectionTitles: {
                personal: 'Persoonlijk',
                bookings: '${bookingPluralCapitalAlias}',
                jobs: '${jobPluralCapitalAlias}',
                resources: '${resourcePluralCapitalAlias}',
                roles: '${rolerequestPluralCapitalAlias}'
            }
        },
        explainSummaryPageTextSecurity: 'Kies widgets die ze kunnen toevoegen aan hun overzichtspagina. Persoonlijke widgets zijn beschikbaar voor alle gebruikers. \n\nWidgets tonen geen voor dit beveiligingsprofiel verborgen records en velden.'
    },
    listPage: {
        pageTitle: 'NL_Lists_NL',
        workspacesMessages: {
            defaultWorkspaceLabel: 'NL_Default workspace_NL',
            newWorkspaceLabel: 'NL_New workspace_NL',
            saveChangesToPublicLabel: 'NL_Save changes to public_NL',
            saveAsNewWorkspaceLabel: 'NL_Save as a new workspace_NL',
            manageMyWorkspacesLabel: 'NL_Manage my workspaces_NL',
            privateWorkspacesLabel: 'NL_Private workspaces_NL',
            publicWorkspacesLabel: 'NL_Public workspaces_NL',
            noPublicWorkspacesCreatedLabel: 'NL_No public workspaces have been created_NL',
            noPrivateWorkspacesCreatedLabel: 'NL_No private workspaces have been created_NL'
        }
    }
};
