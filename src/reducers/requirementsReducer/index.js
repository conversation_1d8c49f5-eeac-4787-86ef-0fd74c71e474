import { CRITERIA_ACTIONS } from '../../actions/actionTypes';
import createChangesReducer from '../commonReducers/changesReducer';
import { getNewEntityId } from '../../utils/entityStructureUtils';
import { cloneDeep, every, isEmpty, isEqual, isString, uniq } from 'lodash';
import { ROLECRITERIA_FIELDS } from '../../constants/fieldConsts';
import { blankCriteriaChanges, buildCriteriaMap, getIsRoleCriteriaTypeAttribute, getIsRoleCriteriaTypeSkill, getRequiremntsWithValues } from '../../utils/requirementsUtils';
import { CRITERIA_SECTIONS } from '../../constants/rolesConsts';
import { ROLECRITERIA_TYPES } from '../../constants/requirementsConst';

const { RESOURCE_ATTRIBUTES, RESOURCE_SKILLS } = ROLECRITERIA_TYPES;

const createBaseCriteriaState = () => {
    return {
        criteriaDetails: {},
        criteriaSections: {
            [CRITERIA_SECTIONS.MUST_MEET]: []
        },
        criteriaChanges: {
            ...blankCriteriaChanges
        }
    };
};

const createCriteria = (value) => {
    return {
        ...value
    };
};

const criteriaChangesReducer = createChangesReducer(createCriteria);

const changesCollectionAddField = (changesState, criteria) => {
    const changesAction = {
        type: 'DO_INSERT',
        payload: {
            id: criteria[ROLECRITERIA_FIELDS.FIELD],
            value: {
                requirement_guid: criteria[ROLECRITERIA_FIELDS.FIELD],
                [ROLECRITERIA_FIELDS.GUID]: criteria[ROLECRITERIA_FIELDS.GUID],
                [ROLECRITERIA_FIELDS.TYPE]: criteria[ROLECRITERIA_FIELDS.TYPE]
            }
        }
    };

    return criteriaChangesReducer(changesState, changesAction);
};

const changesCollectionRemoveDeleteField = (changesState, criteria) => {
    const changesAction = {
        type: 'DO_REMOVE_DELETE',
        payload: {
            id: criteria[ROLECRITERIA_FIELDS.FIELD],
            value: {
                requirement_guid: criteria[ROLECRITERIA_FIELDS.FIELD],
                [ROLECRITERIA_FIELDS.GUID]: criteria[ROLECRITERIA_FIELDS.GUID],
                [ROLECRITERIA_FIELDS.TYPE]: criteria[ROLECRITERIA_FIELDS.TYPE]
            }
        }
    };

    return criteriaChangesReducer(changesState, changesAction);
};

const changesCollectionAddFields = (changesState, criterias) => {
    let newChangesState = changesState;

    criterias.forEach(criteria => {
        newChangesState = getAddAction(newChangesState, criteria);
    });

    return newChangesState;
};

const changesCollectionRemoveInsertedField = (changesState, criteria) => {
    const changesActions = [];

    changesActions.push({
        type: 'DO_REMOVE_INSERT',
        payload: {
            id: criteria[ROLECRITERIA_FIELDS.FIELD],
            value: {
                requirement_guid: criteria[ROLECRITERIA_FIELDS.FIELD],
                [ROLECRITERIA_FIELDS.GUID]: criteria[ROLECRITERIA_FIELDS.GUID],
                id: criteria[ROLECRITERIA_FIELDS.FIELD],
                [ROLECRITERIA_FIELDS.TYPE]: criteria[ROLECRITERIA_FIELDS.TYPE]
            }
        }
    });

    changesActions.push({
        type: 'DO_REMOVE_UPDATE',
        payload: {
            id: criteria[ROLECRITERIA_FIELDS.FIELD]
        }
    });

    let newChangesState = changesState;

    changesActions.forEach(changesAction => {
        newChangesState = criteriaChangesReducer(newChangesState, changesAction);
    });

    return newChangesState;
};

const changesCollectionRemoveField = (changesState, criteria) => {
    const options = { isDeleted: true };
    const changesAction = {
        type: 'DO_REMOVE',
        payload: {
            options,
            value: {
                requirement_guid: criteria[ROLECRITERIA_FIELDS.FIELD],
                [ROLECRITERIA_FIELDS.GUID]: criteria[ROLECRITERIA_FIELDS.GUID],
                [ROLECRITERIA_FIELDS.TYPE]: criteria[ROLECRITERIA_FIELDS.TYPE]
            }
        }
    };

    return criteriaChangesReducer(changesState, changesAction);
};

const changesCollectionRemoveUpdate = (changesState, criteria) => {
    const options = { isDeleted: true };
    const changesAction = {
        type: 'DO_REMOVE_UPDATE',
        payload: {
            options,
            value: {
                requirement_guid: criteria[ROLECRITERIA_FIELDS.FIELD],
                [ROLECRITERIA_FIELDS.GUID]: criteria[ROLECRITERIA_FIELDS.GUID],
                id: criteria[ROLECRITERIA_FIELDS.FIELD],
                [ROLECRITERIA_FIELDS.TYPE]: criteria[ROLECRITERIA_FIELDS.TYPE]
            }
        }
    };

    return criteriaChangesReducer(changesState, changesAction);
};

const changesCollectionUpdateField = (changesState, fieldName, value, rolecriteriaGuid, criteriaType) => {
    const changesAction = {
        type: 'DO_UPDATE',
        payload: {
            id: fieldName,
            value: {
                requirement_guid: fieldName,
                [ROLECRITERIA_FIELDS.GUID]: rolecriteriaGuid,
                [ROLECRITERIA_FIELDS.VALUE]: value,
                [ROLECRITERIA_FIELDS.TYPE]: criteriaType
            }
        }
    };

    return criteriaChangesReducer(changesState, changesAction);
};

const changesCollectionUpdateInsertField = (changesState, fieldName, value, rolecriteriaGuid, criteriaType) => {
    const changesAction = {
        type: 'DO_UPDATE_INSERT',
        payload: {
            id: fieldName,
            value: {
                requirement_guid: fieldName,
                [ROLECRITERIA_FIELDS.GUID]: rolecriteriaGuid,
                [ROLECRITERIA_FIELDS.VALUE]: value,
                [ROLECRITERIA_FIELDS.TYPE]: criteriaType
            }
        }
    };

    return criteriaChangesReducer(changesState, changesAction);
};

const getAddAction = (changesState, criteria) => {
    let newChangesState = changesState;
    const { deletes: { map = {} } } = changesState;
    const deletedFields = Object.values(map).map(value => value[ROLECRITERIA_FIELDS.FIELD]);
    const isDeletedField = !isEmpty(map) && deletedFields.includes(criteria[ROLECRITERIA_FIELDS.FIELD]);

    if (isDeletedField) {
        newChangesState = changesCollectionRemoveDeleteField(newChangesState, criteria);
    } else {
        newChangesState = changesCollectionAddField(newChangesState, criteria);
    }

    return newChangesState;
};

const getDeleteAction = (insertsMap, updatesMap, currentState, criteriaToRemove) => {
    const isFieldInserted = Object.keys(insertsMap).includes(criteriaToRemove[ROLECRITERIA_FIELDS.FIELD]);
    const isFieldUpdated = Object.keys(updatesMap).includes(criteriaToRemove[ROLECRITERIA_FIELDS.FIELD]);

    let result;

    if (isFieldInserted) {
        result = changesCollectionRemoveInsertedField(currentState, criteriaToRemove);
    } else {
        result = changesCollectionRemoveField(currentState, criteriaToRemove);
        if (isFieldUpdated) {
            result = changesCollectionRemoveUpdate(result, criteriaToRemove);
        }
    }

    return result;
};

const getUpdateAction = (insertsMap, currentState, fieldName, value, rolecriteriaGuid, criteriaType) => {
    const isInsertedField = Object.keys(insertsMap).includes(fieldName);

    return isInsertedField
        ? changesCollectionUpdateInsertField(currentState, fieldName, value, rolecriteriaGuid, criteriaType)
        : changesCollectionUpdateField(currentState, fieldName, value, rolecriteriaGuid, criteriaType);
};

const changesCollectionRemoveFields = (changesState, criterias) => {
    let newChangesState = changesState;
    const { inserts: { map: insertsMap = {} }, updates: { map: updatesMap } } = changesState;

    criterias.forEach(criteria => {
        newChangesState = getDeleteAction(insertsMap, updatesMap, newChangesState, criteria);
    });

    return newChangesState;
};

const getSkillFields = (field, criteriaGuid) => {
    const { skillsData, skillId, sectionName, sectionId, skillLevels, operator } = field;
    const result = {
        [ROLECRITERIA_FIELDS.GUID]: criteriaGuid || getNewEntityId(),
        [ROLECRITERIA_FIELDS.FIELD]: skillId,
        [ROLECRITERIA_FIELDS.VALUE]: {
            [ROLECRITERIA_FIELDS.SKILL_TYPE_ID]: sectionId,
            [ROLECRITERIA_FIELDS.SKILL_TYPE_NAME]: sectionName,
            [ROLECRITERIA_FIELDS.SKILL_ID]: skillsData.id,
            [ROLECRITERIA_FIELDS.SKILL_VALUE]: skillsData.name,
            [ROLECRITERIA_FIELDS.OPERATOR]: operator,
            [ROLECRITERIA_FIELDS.SKILL_LEVELS]: skillLevels
        },
        [ROLECRITERIA_FIELDS.TYPE]: RESOURCE_SKILLS
    };

    return result;
};

const getHiddenFields = (criteriaDetails) => {
    const hiddenFields = Object.entries(criteriaDetails)
        .filter(([, criteriaInfo]) => criteriaInfo.isHidden)
        .map(([fieldName]) => fieldName) || [];

    return hiddenFields;
};

const extractResourceAttributesCriteriaDetails = (roleRequirementsState, payload) => {
    const addedCriterias = [];
    let newCriteriaDetails = {};
    const { criteriaSectionName, criteriaFieldNames, criteriaGuid } = payload;
    const currentCriteriaSectionFields = roleRequirementsState.criteriaSections[criteriaSectionName] || [];
    const { criteriaDetails = {} } = roleRequirementsState || {};
    const hiddenFields = getHiddenFields(criteriaDetails);

    const newCriteriaFieldNames = [...criteriaFieldNames, ...hiddenFields];
    const addedFields = newCriteriaFieldNames.filter(field => !currentCriteriaSectionFields.includes(field));
    Object.keys(criteriaDetails).forEach(field => {
        if (getIsRoleCriteriaTypeSkill(criteriaDetails[field])) {
            newCriteriaDetails[field] = criteriaDetails[field];
        }
    });

    newCriteriaFieldNames.forEach((fieldName) => {
        const isAddedField = addedFields.includes(fieldName);
        let result = criteriaDetails[fieldName];
        if (isAddedField) {
            result = {
                [ROLECRITERIA_FIELDS.GUID]: criteriaGuid || getNewEntityId(),
                [ROLECRITERIA_FIELDS.VALUE]: undefined,
                [ROLECRITERIA_FIELDS.FIELD]: fieldName,
                [ROLECRITERIA_FIELDS.TYPE]: RESOURCE_ATTRIBUTES
            };

            addedCriterias.push(result);
        }

        newCriteriaDetails = {
            ...newCriteriaDetails,
            [fieldName]: result
        };
    });

    return { newCriteriaDetails, addedCriterias };
};

const extractResourceAttributesRemovedCriterias = (roleRequirementsState, payload) => {
    const { criteriaSectionName, criteriaFieldNames } = payload;
    const currentCriteriaSectionFields = roleRequirementsState.criteriaSections[criteriaSectionName] || [];

    const { criteriaDetails = {} } = roleRequirementsState || {};
    const hiddenFields = getHiddenFields(criteriaDetails);

    const newCriteriaFieldNames = [...criteriaFieldNames, ...hiddenFields];

    const removedFields = currentCriteriaSectionFields.filter(field => getIsRoleCriteriaTypeAttribute(criteriaDetails[field]) && !newCriteriaFieldNames.includes(field));
    const removedCriterias = removedFields.map((fieldName) => criteriaDetails[fieldName]);

    return { removedCriterias };
};

const extractResourceAttributesCriteriaSections = (roleRequirementsState, payload, removedCriterias) => {
    const { criteriaSectionName, criteriaFieldNames } = payload;
    const { criteriaDetails = {}, criteriaSections } = roleRequirementsState || {};
    let newCriteriaSections = [];
    const hiddenFields = getHiddenFields(criteriaDetails);

    const newCriteriaFieldNames = [...criteriaFieldNames, ...hiddenFields];
    let remainingFields = [];

    if (removedCriterias.length > 0) {
        const removedFields = removedCriterias.map(item => item.rolecriteria_field);
        remainingFields = criteriaSections[criteriaSectionName].filter(field => !removedFields.includes(field));
    } else {
        remainingFields = criteriaSections[criteriaSectionName].filter(field => field);
    }

    const hasFieldPositionChanged = isEqual([...newCriteriaFieldNames].sort(), [...remainingFields].sort());

    if (hasFieldPositionChanged) {
        newCriteriaSections = {
            ...roleRequirementsState.criteriaSections,
            [criteriaSectionName]: uniq([...newCriteriaFieldNames])
        };
    } else {
        newCriteriaSections = {
            ...roleRequirementsState.criteriaSections,
            [criteriaSectionName]: uniq([...remainingFields, ...newCriteriaFieldNames])
        };
    }

    return { newCriteriaSections };
};

const getNewChangesState = (criteriaChanges, addedCriterias, removedCriterias) => {
    let newChangesState = criteriaChanges;
    newChangesState = changesCollectionAddFields(newChangesState, addedCriterias);
    newChangesState = changesCollectionRemoveFields(newChangesState, removedCriterias);

    return newChangesState;
};

const getNewChangesStateForSkills = (addedCriterias, removedCriterias, updatedCriterias, roleRequirementsState) => {
    const { criteriaDetails = {}, criteriaChanges } = roleRequirementsState || {};
    let newChangesState = getNewChangesState(criteriaChanges, addedCriterias, removedCriterias);

    updatedCriterias.forEach(item => {
        const { operator, sectionId, sectionName, skillId, skillLevels, skillsData } = item;
        const rolecriteriaGuid = (criteriaDetails[skillId] || {})[ROLECRITERIA_FIELDS.GUID] || '';
        const { name } = skillsData;
        const value = {
            rolecriteria_skillTypeId: sectionId,
            rolecriteria_skillTypeName: sectionName,
            rolecriteria_skillId: skillId,
            rolecriteria_skillValue: name,
            rolecriteria_skillLevels: skillLevels,
            rolecriteria_operator: operator
        };

        newChangesState = changesCollectionUpdateField(newChangesState, skillId, value, rolecriteriaGuid, RESOURCE_SKILLS);
    });

    return newChangesState;
};

const extractResourceSkillsCriteriaDetails = (roleRequirementsState, payload, roleRequirementsCleanMapState) => {
    const { criteriaSectionName, criteriaSkillNames } = payload;
    const currentCriteriaSectionFields = roleRequirementsState.criteriaSections[criteriaSectionName] || [];
    const currentCriteriaSectionFieldsCleanMapState = (roleRequirementsCleanMapState.criteriaSections && roleRequirementsCleanMapState.criteriaSections[criteriaSectionName]) || [];

    const { criteriaDetails = {} } = roleRequirementsState || {};
    const { criteriaDetails: criteriaDetailsCleanMapState = {} } = roleRequirementsCleanMapState || {};

    const addedCriterias = [];
    let newCriteriaDetails = {};
    const updatedCriterias = [];

    const newCriteriaSkillNames = [...criteriaSkillNames];
    const hasFieldPositionChanged = every(newCriteriaSkillNames, isString) && newCriteriaSkillNames.length > 0;

    if (hasFieldPositionChanged) {
        const hiddenFields = getHiddenFields(criteriaDetails);

        newCriteriaSkillNames.push(...hiddenFields);

        newCriteriaSkillNames.forEach(field => {
            newCriteriaDetails = {
                ...newCriteriaDetails,
                [field]: criteriaDetails[field]
            };
        });
    } else {
        const addedFields = newCriteriaSkillNames
            .filter((field) => !currentCriteriaSectionFields.includes(field.skillId))
            .map((item) => item.skillId);

        const modifiedCriteriaSkillNames = newCriteriaSkillNames.filter((field) => field.hasSkillLevels && currentCriteriaSectionFieldsCleanMapState.includes(field.skillId));

        modifiedCriteriaSkillNames.forEach(item => {
            const { operator, skillLevels = [] } = item;
            const criteriaDetailsCleanMapStateDetails = criteriaDetailsCleanMapState[item.skillId];
            if (criteriaDetailsCleanMapStateDetails) {
                const { rolecriteria_value } = criteriaDetailsCleanMapStateDetails || {};
                const { rolecriteria_operator, rolecriteria_skillLevels } = rolecriteria_value || {};
                if (!isEqual(rolecriteria_skillLevels, skillLevels) || rolecriteria_operator !== operator) {
                    updatedCriterias.push(item);
                }
            }
        });

        Object.keys(criteriaDetails).forEach(field => {
            if (getIsRoleCriteriaTypeAttribute(criteriaDetails[field])) {
                newCriteriaDetails[field] = criteriaDetails[field];
            }
        });
        newCriteriaSkillNames.forEach(field => {
            const { skillId } = field;
            const criteriaGuid = criteriaDetails[skillId] && criteriaDetails[skillId].rolecriteria_guid;
            const isAddedField = addedFields.includes(skillId);
            const result = getSkillFields(field, criteriaGuid);

            if (isAddedField) {
                addedCriterias.push(result);
            }
            newCriteriaDetails = {
                ...newCriteriaDetails,
                [skillId]: result
            };
        });
    }

    return { newCriteriaDetails, addedCriterias, updatedCriterias };
};

const extractResourceSkillsRemovedCriterias = (roleRequirementsState, payload) => {
    const { criteriaSectionName, criteriaSkillNames } = payload;
    const currentCriteriaSectionFields = roleRequirementsState.criteriaSections[criteriaSectionName] || [];

    const { criteriaDetails = {} } = roleRequirementsState || {};
    let removedCriterias = [];
    const newCriteriaSkillNames = [...criteriaSkillNames];
    const hasFieldPositionChanged = every(newCriteriaSkillNames, isString);

    if (!hasFieldPositionChanged || newCriteriaSkillNames.length === 0) {
        const updatedCriteriaSkillNames = newCriteriaSkillNames.map(item => item.skillId);
        const removedFields = currentCriteriaSectionFields.filter(field => getIsRoleCriteriaTypeSkill(criteriaDetails[field]) && !updatedCriteriaSkillNames.includes(field));
        removedCriterias = removedFields.map((fieldName) => criteriaDetails[fieldName]);
    }

    return { removedCriterias };
};

const extractResourceSkillsCriteriaSections = (roleRequirementsState, payload, removedCriterias) => {
    const { criteriaSectionName, criteriaSkillNames } = payload;

    const { criteriaDetails = {}, criteriaSections } = roleRequirementsState || {};
    let newCriteriaSections = [];
    let newCriteriaSkillNames = [...criteriaSkillNames];

    let remainingFields = [];

    if (removedCriterias.length > 0) {
        const removedFields = removedCriterias.map(item => item.rolecriteria_field);
        remainingFields = criteriaSections[criteriaSectionName].filter(field => !removedFields.includes(field));
    } else {
        remainingFields = criteriaSections[criteriaSectionName].filter(field => field);
    }

    const hasFieldPositionChanged = every(newCriteriaSkillNames, isString) && newCriteriaSkillNames.length > 0;

    if (hasFieldPositionChanged) {
        const hiddenFields = getHiddenFields(criteriaDetails);

        newCriteriaSkillNames.push(...hiddenFields);

        newCriteriaSections = {
            ...roleRequirementsState.criteriaSections,
            [criteriaSectionName]: uniq([...newCriteriaSkillNames])
        };
    } else {
        const updatedCriteriaSkillNames = newCriteriaSkillNames.map(item => item.skillId);
        newCriteriaSections = {
            ...roleRequirementsState.criteriaSections,
            [criteriaSectionName]: uniq([...remainingFields, ...updatedCriteriaSkillNames])
        };
    }

    return { newCriteriaSections };
};

const createRequirementsReducer = (alias, initialState) => {
    return (state = initialState, action) => {
        switch (action.type) {
            case `${CRITERIA_ACTIONS.DUPLICATE_CRITERIA}_${alias}`: {
                const {
                    sourceRoleId,
                    targetRoleId
                } = action.payload;

                const sourceCriteriaSection = state.map[sourceRoleId];
                const sourceCriteriaDetails = sourceCriteriaSection.criteriaDetails;

                const sourceCriteriaFields = sourceCriteriaSection.criteriaSections[CRITERIA_SECTIONS.MUST_MEET].filter(criteriaFieldName => {
                    return !sourceCriteriaDetails[criteriaFieldName].isHidden;
                });

                const criteriaValues = [];
                const newCriteriaDetails = {};

                Object.keys(sourceCriteriaDetails)
                    .filter(criteriaField => sourceCriteriaFields.includes(criteriaField))
                    .forEach(criteriaField => {
                        const newCriteriaField = {
                            ...sourceCriteriaDetails[criteriaField],
                            rolecriteria_guid: getNewEntityId()
                        };
                        criteriaValues.push(newCriteriaField);

                        newCriteriaDetails[criteriaField] = newCriteriaField;
                    });

                const newCriteriaState = {
                    criteriaDetails: newCriteriaDetails,
                    criteriaSections: {
                        [CRITERIA_SECTIONS.MUST_MEET]: sourceCriteriaFields
                    }
                };

                let criteriaChanges = {
                    ...blankCriteriaChanges
                };

                newCriteriaState.criteriaChanges = changesCollectionAddFields(criteriaChanges, criteriaValues);

                const newCriteriaMap = {
                    ...state.map,
                    [targetRoleId]: newCriteriaState
                };

                return {
                    ...state,
                    byId: [...state.byId, targetRoleId],
                    map: newCriteriaMap
                };
            }
            case `${CRITERIA_ACTIONS.CREATE_ROLE_REQUIREMENTS_STATE}_${alias}`: {
                const {
                    newEntityId
                } = action.payload;
                const newCriteriaState = createBaseCriteriaState();

                const byIdState = [...state.byId, newEntityId];
                const criteriaMap = {
                    ...state.map,
                    [newEntityId]: newCriteriaState
                };

                return {
                    ...state,
                    byId: byIdState,
                    map: criteriaMap
                };
            }
            case `${CRITERIA_ACTIONS.POPULATE_CRITERIAS_FOR_ROLES}_${alias}`: {
                const {
                    roleGuids = [],
                    criterias = []
                } = action.payload;

                const byIdState = [...state.byId];
                let criteriaMap = {
                    ...state.map
                };

                roleGuids.forEach((guid) => {
                    byIdState.push(guid);
                    criteriaMap = buildCriteriaMap(guid, criterias, criteriaMap);

                    const { criteriaDetails } = criteriaMap[guid];
                    const criteriaValues = Object.values(criteriaDetails);

                    const selectedOptions = [];
                    const selectedValues = [];

                    criteriaValues.forEach(criteria => {
                        if (criteria[ROLECRITERIA_FIELDS.TYPE] !== ROLECRITERIA_TYPES.RESOURCE_SKILLS || criteria.isHidden) {
                            return;
                        }
                        const { rolecriteria_value } = criteria;
                        const { rolecriteria_skillId, rolecriteria_skillLevels, rolecriteria_skillTypeId } = rolecriteria_value || {};

                        if (!rolecriteria_skillId || !rolecriteria_skillTypeId) {
                            return;
                        }

                        const sectionId = rolecriteria_skillTypeId;
                        const skillId = rolecriteria_skillId;
                        const sectionLabel = sectionId;
                        const subCategoryID = undefined;
                        // TODO: Add subCategoryID when available from API
                        const skillLabel = skillId;
                        // TODO levels values should send when selected Values implemented in skillfilter
                        const levels = rolecriteria_skillLevels || [];

                        selectedValues.push([sectionId, subCategoryID, skillId, ...levels]);

                        const levelOptions = levels.map(level => {
                            const levelData = criteria.skillLevels?.find(l => l.guid === level);

                            return {
                                value: level,
                                label: levelData?.name || level
                            };
                        });

                        selectedOptions.push([
                            { value: sectionId, label: sectionLabel },
                            { value: subCategoryID, label: subCategoryID },
                            { value: skillId, label: skillLabel },
                            ...levelOptions
                        ]);
                    });

                    criteriaMap[guid] = {
                        ...criteriaMap[guid],
                        selectedOptions: selectedOptions.length > 0 ? selectedOptions : null,
                        selectedValues: selectedValues.length > 0 ? selectedValues : null
                    };
                });

                return {
                    ...state,
                    cleanByIdState: byIdState,
                    cleanMapState: criteriaMap,
                    byId: byIdState,
                    map: criteriaMap
                };
            }
            case `${CRITERIA_ACTIONS.POPULATE_CRITERIAS_FOR_ROLES_TEMPLATE}_${alias}`: {
                const {
                    roleGuids = [],
                    criterias = []
                } = action.payload;

                const byIdState = [...state.byId];
                let criteriaMap = { ...state.map };
                const addHiddenCriteria = false;

                roleGuids.forEach((guid) => {
                    byIdState.push(guid);

                    criteriaMap = buildCriteriaMap(guid, criterias, criteriaMap, addHiddenCriteria);
                    const { criteriaDetails } = criteriaMap[guid];
                    const criteriaValues = Object.values(criteriaDetails);

                    let criteriaChanges = {
                        ...blankCriteriaChanges
                    };

                    criteriaMap[guid].criteriaChanges = changesCollectionAddFields(criteriaChanges, criteriaValues);

                    const selectedOptions = [];
                    const selectedValues = [];

                    criteriaValues.forEach(criteria => {

                        if (criteria[ROLECRITERIA_FIELDS.TYPE] !== ROLECRITERIA_TYPES.RESOURCE_SKILLS || criteria.isHidden) {
                            return;
                        }

                        const { rolecriteria_value } = criteria;
                        const { rolecriteria_skillId, rolecriteria_skillLevels, rolecriteria_skillTypeId } = rolecriteria_value || {};

                        if (rolecriteria_skillId && rolecriteria_skillTypeId) {
                            const sectionId = rolecriteria_skillTypeId;
                            const skillId = rolecriteria_skillId;

                            const sectionLabel = sectionId;
                            // TODO levels values should send when selected Values implemented in skillfilter
                            const levels = rolecriteria_skillLevels || [];
                            const subCategoryID = undefined;
                            // TODO: Add subCategoryID when available from API
                            const valueEntry = [sectionId, subCategoryID, skillId, ...levels];
                            selectedValues.push(valueEntry);

                            const levelOptions = levels.map(level => ({
                                value: level,
                                label: level
                            }));

                            const optionEntry = [
                                { value: sectionId, label: sectionLabel },
                                { value: subCategoryID, label: subCategoryID },
                                { value: skillId, label: skillId },
                                ...levelOptions
                            ];
                            selectedOptions.push(optionEntry);
                        }
                    });

                    // Update criteriaMap with selectedOptions and selectedValues
                    criteriaMap[guid] = {
                        ...criteriaMap[guid],
                        selectedOptions: selectedOptions.length > 0 ? selectedOptions : null,
                        selectedValues: selectedValues.length > 0 ? selectedValues : null
                    };
                });

                return {
                    ...state,
                    cleanByIdState: byIdState,
                    cleanMapState: {
                        ...(state.cleanMapState || {})
                    },
                    byId: byIdState,
                    map: criteriaMap
                };
            }
            case `${CRITERIA_ACTIONS.POPULATE_CRITERIA_VALUES}_${alias}`: {
                const { valuesMap = {}, roleGuid } = action.payload;

                const roleRequirementsState = state.map[roleGuid];
                const newCriteriaDetails = getRequiremntsWithValues(roleRequirementsState, valuesMap);

                const cleanRoleRequirementsState = state.cleanMapState[roleGuid];
                const isRoleInCleanState = !isEmpty(cleanRoleRequirementsState);
                const newCleanCriteriaDetails = getRequiremntsWithValues(cleanRoleRequirementsState, valuesMap);

                return {
                    ...state,
                    map: {
                        ...state.map,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetails
                        }
                    },
                    cleanMapState: {
                        ...state.cleanMapState,
                        ...(isRoleInCleanState && {
                            [roleGuid]: {
                                ...cleanRoleRequirementsState,
                                criteriaDetails: newCleanCriteriaDetails
                            }
                        })
                    }
                };
            }
            case `${CRITERIA_ACTIONS.POPULATE_CRITERIA_VALUES_FOR_MULTIPLE_ROLES}_${alias}`: {
                const { valuesMap = {}, roleGuids } = action.payload;

                const newMapState = roleGuids.reduce((accumulator, roleGuid) => {
                    const roleRequirementsState = state.map[roleGuid];
                    const newCriteriaDetails = getRequiremntsWithValues(roleRequirementsState, valuesMap);
                    accumulator = {
                        ...accumulator,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetails
                        }
                    };

                    return accumulator;
                }, {});

                const newCleanMapState = roleGuids.reduce((accumulator, roleGuid) => {
                    const cleanRoleRequirementsState = state.cleanMapState[roleGuid];
                    const isRoleInCleanState = !isEmpty(cleanRoleRequirementsState);
                    const newCleanCriteriaDetails = getRequiremntsWithValues(cleanRoleRequirementsState, valuesMap);

                    accumulator = {
                        ...accumulator,
                        ...(isRoleInCleanState && {
                            [roleGuid]: {
                                ...cleanRoleRequirementsState,
                                criteriaDetails: newCleanCriteriaDetails
                            }
                        })
                    };

                    return accumulator;
                }, {});

                return {
                    ...state,
                    map: {
                        ...state.map,
                        ...newMapState
                    },
                    cleanMapState: {
                        ...state.cleanMapState,
                        ...newCleanMapState
                    }
                };
            }
            case `${CRITERIA_ACTIONS.SET_CRITERIA_FIELDS}_${alias}`: {
                const { roleGuid } = action.payload;
                const roleRequirementsState = state.map[roleGuid];

                const { newCriteriaDetails = {}, addedCriterias = [] } = extractResourceAttributesCriteriaDetails(roleRequirementsState, action.payload);
                const { removedCriterias = [] } = extractResourceAttributesRemovedCriterias(roleRequirementsState, action.payload);
                const { newCriteriaSections } = extractResourceAttributesCriteriaSections(roleRequirementsState, action.payload, removedCriterias);

                const newChangesState = getNewChangesState(roleRequirementsState.criteriaChanges, addedCriterias, removedCriterias);

                const newById = [...state.byId];

                if (!newById.includes(roleGuid)) {
                    newById.push(roleGuid);
                }

                return {
                    ...state,
                    byId: newById,
                    map: {
                        ...state.map,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetails,
                            criteriaSections: newCriteriaSections,
                            criteriaChanges: newChangesState
                        }
                    }
                };
            }
            case `${CRITERIA_ACTIONS.SET_CRITERIA_SKILLS}_${alias}`: {
                const { roleGuid, options, values } = action.payload;
                const roleRequirementsState = state.map[roleGuid];
                const roleRequirementsCleanMapState = (state.cleanMapState && state.cleanMapState[roleGuid]) || {};

                const { newCriteriaDetails = {}, addedCriterias = [], updatedCriterias = [] } = extractResourceSkillsCriteriaDetails(roleRequirementsState, action.payload, roleRequirementsCleanMapState);
                const { removedCriterias = [] } = extractResourceSkillsRemovedCriterias(roleRequirementsState, action.payload);
                const { newCriteriaSections } = extractResourceSkillsCriteriaSections(roleRequirementsState, action.payload, removedCriterias);

                const newChangesState = getNewChangesStateForSkills(addedCriterias, removedCriterias, updatedCriterias, roleRequirementsState);

                const newById = [...state.byId];

                if (!newById.includes(roleGuid)) {
                    newById.push(roleGuid);
                }

                return {
                    ...state,
                    byId: newById,
                    map: {
                        ...state.map,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetails,
                            criteriaSections: newCriteriaSections,
                            criteriaChanges: newChangesState,
                            selectedOptions: options,
                            selectedValues: values
                        }
                    },
                    skillsAutocomplete: {}
                };
            }
            case `${CRITERIA_ACTIONS.REMOVE_CRITERIA_FIELD}_${alias}`: {
                const {
                    roleGuid,
                    field,
                    section
                } = action.payload;

                const roleRequirementsState = state.map[roleGuid];
                const { criteriaSections, criteriaChanges, criteriaDetails, selectedOptions, selectedValues } = roleRequirementsState;
                const { inserts: { map: insertsMap }, updates: { map: updatesMap } } = criteriaChanges;

                let fields = [...criteriaSections[section]];
                const fieldIndex = fields.indexOf(field);
                const criteriaToRemove = criteriaDetails[field];
                fields.splice(fieldIndex, 1);

                const newCriteriaDetailsState = cloneDeep(criteriaDetails);
                delete newCriteriaDetailsState[field];

                const criteriaChangesNewState = getDeleteAction(insertsMap, updatesMap, criteriaChanges, criteriaToRemove);

                // Remove field from selectedOptions and selectedValues
                const newSelectedOptions = selectedOptions?.filter(option => option[2]?.value !== field);
                const newSelectedValues = selectedValues?.filter(value => value[2] && value[2] !== field);

                const newById = [...state.byId];
                newById.pop(roleGuid);

                return {
                    ...state,
                    map: {
                        ...state.map,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetailsState,
                            criteriaSections: {
                                ...roleRequirementsState.criteriaSections,
                                [section]: fields
                            },
                            criteriaChanges: criteriaChangesNewState,
                            selectedOptions: newSelectedOptions,
                            selectedValues: newSelectedValues
                        }
                    }
                };
            }
            case `${CRITERIA_ACTIONS.DISCARD_CRITERIA_CHANGES}_${alias}`: {
                return {
                    ...state,
                    byId: state.cleanByIdState,
                    map: state.cleanMapState,
                    skillsAutocomplete: {}
                };
            }
            case `${CRITERIA_ACTIONS.SET_CRITERIA_FIELDS_VALUE}_${alias}`: {
                const {
                    value,
                    fieldName,
                    roleGuid
                } = action.payload;

                const roleRequirementsState = state.map[roleGuid];
                const { criteriaChanges, criteriaDetails } = roleRequirementsState;
                const { inserts } = criteriaChanges;
                const newCriteriaDetails = cloneDeep(criteriaDetails);
                newCriteriaDetails[fieldName].rolecriteria_value = value;
                const rolecriteriaGuid = criteriaDetails[fieldName][ROLECRITERIA_FIELDS.GUID];
                const criteriaType = newCriteriaDetails[fieldName].rolecriteria_type;
                const newCriteriaChanges = getUpdateAction(inserts.map, criteriaChanges, fieldName, value, rolecriteriaGuid, criteriaType);

                return {
                    ...state,
                    map: {
                        ...state.map,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetails,
                            criteriaChanges: newCriteriaChanges
                        }
                    }
                };
            }
            case `${CRITERIA_ACTIONS.SET_CRITERIA_SKILLS_VALUE}_${alias}`: {
                const {
                    levels,
                    skillId,
                    roleGuid,
                    operator
                } = action.payload;

                const roleRequirementsState = state.map[roleGuid];
                const { criteriaChanges, criteriaDetails } = roleRequirementsState;
                const { inserts } = criteriaChanges;
                const newCriteriaDetails = cloneDeep(criteriaDetails);
                const value = {
                    ...newCriteriaDetails[skillId].rolecriteria_value,
                    rolecriteria_skillLevels: levels,
                    rolecriteria_operator: operator
                };
                newCriteriaDetails[skillId].rolecriteria_value = value;
                const rolecriteriaGuid = criteriaDetails[skillId][ROLECRITERIA_FIELDS.GUID];
                const newCriteriaChanges = getUpdateAction(inserts.map, criteriaChanges, skillId, value, rolecriteriaGuid, newCriteriaDetails[skillId].rolecriteria_type);


                return {
                    ...state,
                    map: {
                        ...state.map,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetails,
                            criteriaChanges: newCriteriaChanges
                        }
                    }
                };
            }
            case `${CRITERIA_ACTIONS.SET_CRITERIA_FIELDS_ERROR}_${alias}`: {
                const {
                    errors,
                    roleGuid
                } = action.payload;

                const roleRequirementsState = state.map[roleGuid];
                const { criteriaDetails } = roleRequirementsState;
                let newCriteriaDetails = criteriaDetails;

                Object.entries(errors).forEach(([key, value]) => {
                    if (criteriaDetails[key]) {
                        newCriteriaDetails = {
                            ...newCriteriaDetails,
                            [key]: {
                                ...newCriteriaDetails[key],
                                ...value
                            }
                        };
                    }
                });

                return {
                    ...state,
                    map: {
                        ...state.map,
                        [roleGuid]: {
                            ...roleRequirementsState,
                            criteriaDetails: newCriteriaDetails
                        }
                    }
                };
            }
            case CRITERIA_ACTIONS.AUTOCOMPLETE_SKILLS_SUCCESS: {
                const { skills, sectionId } = action.payload;

                return {
                    ...state,
                    skillsAutocomplete: {
                        ...state.skillsAutocomplete,
                        [sectionId]: skills
                    }
                };
            }
            case CRITERIA_ACTIONS.AUTOCOMPLETE_SKILLS_ERROR:
            case CRITERIA_ACTIONS.AUTOCOMPLETE_SKILLS_CLEAR: {
                return {
                    ...state,
                    skillsAutocomplete: {}
                };
            }
            default: {
                return state;
            }
        }
    };
};

export { createRequirementsReducer };