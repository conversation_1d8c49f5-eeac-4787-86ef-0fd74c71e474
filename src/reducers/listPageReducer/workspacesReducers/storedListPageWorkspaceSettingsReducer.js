import { LIST_PAGE_ACTIONS } from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import { constructObjectWithSameValues } from '../../../utils/commonUtils';

export default function storedListPageWorkspaceSettingsReducer(state = initialState.listPage.workspaces.storedWorkspaceSettings, action) {
    switch (action.type) {
        case LIST_PAGE_ACTIONS.STORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, targetView, targetViewConfig } = action.payload;

            return {
                ...state,
                [workspaceGuid]: {
                    viewsConfig:{
                        ...(state[workspaceGuid] || {}).viewsConfig || {},
                        [targetView]: targetViewConfig
                    }
                }
            };
        }
        case LIST_PAGE_ACTIONS.RESTORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, view } = action.payload;

            return {
                ...state,
                [workspaceGuid]: {
                    viewsConfig:{
                        ...(state[workspaceGuid] || {}).viewsConfig || {},
                        [view]: {}
                    }
                }
            };
        }
        case LIST_PAGE_ACTIONS.CLEAR_STORED_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, views } = action.payload;
            const clearedViews = constructObjectWithSameValues(views, {});

            return {
                ...state,
                [workspaceGuid]: {
                    viewsConfig:{
                        ...(state[workspaceGuid] || {}).viewsConfig || {},
                        ...clearedViews
                    }
                }
            };
        }
        default: {
            return state;
        }
    }
}
