import { values } from 'lodash';

import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import workspacesStructureReducer from './workspacesStructureReducer';
import listPageWorkspaceSettingsReducer from './listPageWorkspaceSettingsReducer';
import workspacesListPageSettingsChangesTrackerReducer from './workspacesListPageSettingsChangesTrackerReducer';
import createChangesReducer from '../../commonReducers/changesReducer';
import { getDefaultWorkspaceStructure, getWorkspacesStructureMap } from '../../../selectors/workspaceSelectors';
import storedListPageWorkspaceSettingsReducer from './storedListPageWorkspaceSettingsReducer';
import { buildWorkspaceSettings } from '../../../utils/workspaceUtils';
import { DEFAULT_FILTER } from '../../../constants/globalConsts';
import { JOBS_PAGE_ALIAS } from '../../../constants';

export const createWsFn = (change, options) => {
    return {
        change,
        options
    };
};

const wsStructureChangesReducer = createChangesReducer(createWsFn);
const wsSettingsChangesReducer = createChangesReducer(createWsFn);

export const configureWorkspaceSettings = (workspace) => {
    return workspace.workspace_settings;
};

export const configureWorkspaceStructure = (workspace) => {
    return {
        ...workspace,
        workspace_accesstype: workspace.workspace_accesstype.toLowerCase(),
        workspace_editrights: workspace.workspace_editrights.toLowerCase()
    };
};

const mostRecentlyUsedWorkspaceStructureLoaded = (mruWorkspaceGuid, allWorkspaces) => {
    let result = false;

    for (let currWSIndex = 0; currWSIndex < allWorkspaces.length; currWSIndex++) {
        if (mruWorkspaceGuid === allWorkspaces[currWSIndex].workspace_guid) {
            result = true;
            break;
        }
    }

    return result;
};

const getWorkspacesState = (state, workspaces) => {
    let newState = { ...state };
    const loadedWSStructures = workspaces[0] ? workspaces[0] : [];
    const loadFirstMostRecentlyUsedWorkspace = workspaces[1][0] ? buildWorkspaceSettings(workspaces[1][0]) : null;

    if (loadFirstMostRecentlyUsedWorkspace && loadFirstMostRecentlyUsedWorkspace.filtersGuid !== DEFAULT_FILTER
        && !loadFirstMostRecentlyUsedWorkspace.filterSettings
        && (!loadFirstMostRecentlyUsedWorkspace.filterState
        || !loadFirstMostRecentlyUsedWorkspace.filterState.views
        || Object.keys(loadFirstMostRecentlyUsedWorkspace.filterState.views).length <= 0)) {
        loadFirstMostRecentlyUsedWorkspace.filterState = {
            guid: loadFirstMostRecentlyUsedWorkspace.filtersGuid,
            title: '',
            selectedView: 'job',
            hasHiddenFilters: false,
            views: {}
        };
    }

    const allWorkspaces = loadedWSStructures.map((workspace) => configureWorkspaceStructure(workspace));
    const workspacesSettings = loadFirstMostRecentlyUsedWorkspace ? [loadFirstMostRecentlyUsedWorkspace] : [];
    const mostRecentlyUsed = workspaces[1].map(workspace => workspace.workspace_guid);

    return {
        ...newState,
        selected: loadFirstMostRecentlyUsedWorkspace ? loadFirstMostRecentlyUsedWorkspace.workspace_guid : '',
        workspacesStructure: allWorkspaces,
        workspacesSettings,
        mostRecentlyUsed,
        storedWorkspaceSettings: {},
        cachedMostRecentWorkspaces: workspaces[1]
    };
};

export default function listPageWorkspacesReducer(state = initialState.workspaces, action) {
    switch (action.type) {
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;
            const newState = getWorkspacesState(state, data);
            const { selected, workspacesStructure, workspacesSettings, mostRecentlyUsed, cachedMostRecentWorkspaces } = newState;

            return {
                ...state,
                selected,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, {
                    type: actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL,
                    payload: {
                        workspacesStructure
                    }
                }),
                workspacesSettings: listPageWorkspaceSettingsReducer(state.workspacesSettings, {
                    type: actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL,
                    payload: {
                        workspacesSettings
                    }
                }),
                mostRecentlyUsed,
                cachedMostRecentWorkspaces
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DIGEST_LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { data, loadDefaultWorkspace, addDefaultMostRecentlyUsed } = payload;
            const { loadDefaultWorkspaceResult, addDefaultMostRecentlyUsedResult } = data;

            const loadWorkspaceSuccessfulAction = {
                type: actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACE_SUCCESSFUL,
                payload: {
                    data: loadDefaultWorkspaceResult
                }
            };

            return {
                ...state,
                workspacesStructure: loadDefaultWorkspace ? workspacesStructureReducer(state.workspacesStructure, loadWorkspaceSuccessfulAction) : state.workspacesStructure,
                workspacesSettings: loadDefaultWorkspace ? listPageWorkspaceSettingsReducer(state.workspacesSettings, loadWorkspaceSuccessfulAction) : state.workspacesSettings,
                mostRecentlyUsed: addDefaultMostRecentlyUsed ? addDefaultMostRecentlyUsedResult.updatedMostRecentlyUsed.slice() : state.mostRecentlyUsed,
                selected : addDefaultMostRecentlyUsed ? addDefaultMostRecentlyUsedResult.updatedMostRecentlyUsed[0] : state.selected
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.GET_MOST_RECENTLY_USED_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { getWorkspaceDetail, data } = payload;

            let mostRecentlyUsedWorkspacesGuids = data;
            if (getWorkspaceDetail) {
                mostRecentlyUsedWorkspacesGuids = data.map((mruWorkspace) => {
                    return mruWorkspace.workspace_guid;
                });
            }

            //need to make sure mru structure is lodaded to prevent js errors with no longer visible workspaces
            const workspaceStructures = values(getWorkspacesStructureMap(state));
            const visibleMostRecentlyUsedWorkspaces = mostRecentlyUsedWorkspacesGuids.slice().filter(
                mruWorkspaceGuid => mostRecentlyUsedWorkspaceStructureLoaded(mruWorkspaceGuid, workspaceStructures)
            );

            return {
                ...state,
                mostRecentlyUsed: visibleMostRecentlyUsedWorkspaces
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.SELECT_WORKSPACE: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            return {
                ...state,
                selected: workspaceGuid
            };
        }
        case `${actionTypes.FILTERS_ACTIONS.FILTER_VALUE_CHANGE}_${JOBS_PAGE_ALIAS}_filters`: {
            return {
                ...state,
                workspacesSettings: listPageWorkspaceSettingsReducer(state.workspacesSettings, action),
                workspacesSettingsChangesTracker: workspacesListPageSettingsChangesTrackerReducer(state.workspacesSettingsChangesTracker, action)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE_FILTER_SETTINGS:
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL:
        case actionTypes.CHANGE_COLOUR_SCHEME:
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACE_SUCCESSFUL: {
            return {
                ...state,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, action),
                workspacesSettings: listPageWorkspaceSettingsReducer(state.workspacesSettings, action),
                workspacesSettingsChangesTracker: workspacesListPageSettingsChangesTrackerReducer(state.workspacesSettingsChangesTracker, action)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            const mostRecentlyUsedWithoutDeleted = state.mostRecentlyUsed.filter(item => item !== workspaceGuid);

            const getNextSelectedWorksapceGuid = (state, mostRecentlyUsed) => {
                return mostRecentlyUsed.length > 0 ? mostRecentlyUsed[0] : getDefaultWorkspaceStructure(state).workspace_guid;
            };

            return {
                ...state,
                selected: state.selected == workspaceGuid ? getNextSelectedWorksapceGuid(state, mostRecentlyUsedWithoutDeleted) : state.selected,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, action),
                workspacesSettings: listPageWorkspaceSettingsReducer(state.workspacesSettings, action),
                mostRecentlyUsed: mostRecentlyUsedWithoutDeleted
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.SET_CREATE_WORKSPACE_CHANGE: {
            const { payload } = action;
            const { newWSUUID, newWorkspaceStructure, newWorkspaceSettings } = payload;

            const createStructureActionValue = {
                id: newWSUUID,
                value: newWorkspaceStructure
            };
            const createSettingsActionValue = {
                id: newWSUUID,
                value: newWorkspaceSettings
            };

            const structureChangeAction = {
                type: 'DO_INSERT',
                payload: {
                    id: newWSUUID,
                    value: createStructureActionValue
                }
            };
            const settingsChangeAction = {
                type: 'DO_INSERT',
                payload: {
                    id: newWSUUID,
                    value: createSettingsActionValue
                }
            };

            return {
                ...state,
                workspacesStructureChanges: wsStructureChangesReducer(state.workspacesStructureChanges, structureChangeAction),
                workspacesSettingsChanges: wsSettingsChangesReducer(state.workspacesSettingsChanges, settingsChangeAction)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.RENAME_WORKSPACE_SUCCESS:
        case actionTypes.LIST_PAGE_ACTIONS.MOVE_WORKSPACE_SUCCESS: {
            return {
                ...state,
                workspacesStructure: workspacesStructureReducer(state.workspacesStructure, action)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DIGEST_WORKSPACE_STRUCTURE_CHANGE: {
            const { payload } = action;
            const { workspaceGuid, changes } = payload;

            const updateActionValue = {
                id: workspaceGuid,
                value: {
                    ...changes
                }
            };

            const changeAction = {
                type: 'DO_UPDATE',
                payload: {
                    id: workspaceGuid,
                    value: updateActionValue
                }
            };

            return {
                ...state,
                workspacesStructureChanges: wsStructureChangesReducer(state.workspacesStructureChanges, changeAction)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.REMOVE_CREATE_WORKSPACE_CHANGE: {
            const { payload } = action;
            const { workspaceChangeUUID } = payload;

            //clear changes
            const structureChangeAction = {
                type: 'REMOVE_CHANGE',
                id: workspaceChangeUUID
            };
            const settingsChangeAction = {
                type: 'REMOVE_CHANGE',
                id: workspaceChangeUUID
            };

            return {
                ...state,
                workspacesStructureChanges: wsStructureChangesReducer(state.workspacesStructureChanges, structureChangeAction),
                workspacesSettingsChanges: wsSettingsChangesReducer(state.workspacesSettingsChanges, settingsChangeAction)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DROP_MOST_RECENTLY_USED_WORKSPACE_SUCCESS: {
            return {
                ...state,
                workspacesSettings: listPageWorkspaceSettingsReducer(state.workspacesSettings, action)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.SAVE_WORKSPACE_SETTINGS_SUCCESSFUL: {
            return {
                ...state,
                workspacesSettingsChangesTracker: workspacesListPageSettingsChangesTrackerReducer(state.workspacesSettingsChangesTracker, action)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.STORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, targetView } = action.payload;
            const workspacesSettings = state.workspacesSettings.map[workspaceGuid] || {};
            const targetViewConfig = workspacesSettings.viewsConfig[targetView] || {};

            const storeAction = {
                ...action,
                payload: { ...action.payload, targetViewConfig }
            };

            return {
                ...state,
                storedWorkspaceSettings: storedListPageWorkspaceSettingsReducer(state.storedWorkspaceSettings, storeAction)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.RESTORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, view } = action.payload;
            const storedWorkspaceViewSettings = ((state.storedWorkspaceSettings[workspaceGuid] || {}).viewsConfig || {})[view] || {};

            const workspaceSettingsAction = {
                ...action,
                payload: {
                    ...action.payload,
                    viewConfig: storedWorkspaceViewSettings
                }
            };

            return {
                ...state,
                workspacesSettings: listPageWorkspaceSettingsReducer(state.workspacesSettings, workspaceSettingsAction),
                storedWorkspaceSettings: storedListPageWorkspaceSettingsReducer(state.storedWorkspaceSettings, action)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.CLEAR_STORED_WORKSPACE_VIEW_SETTINGS: {
            return {
                ...state,
                storedWorkspaceSettings: storedListPageWorkspaceSettingsReducer(state.storedWorkspaceSettings, action)
            };
        }
        default: {
            return state;
        }
    }
}
