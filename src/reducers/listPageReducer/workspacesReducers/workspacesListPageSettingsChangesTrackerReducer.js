import initialState from '../../../state/initialState';
import * as actionTypes from '../../../actions/actionTypes';
import createChangesReducer from '../../commonReducers/changesReducer';
import { JOBS_PAGE_ALIAS } from '../../../constants';

export const createWsFn = (change, options) => {
    return {
        change,
        options
    };
};
const wsSettingsChangesTrackerReducer = createChangesReducer(createWsFn);

export default function workspacesListPageSettingsChangesTrackerReducer(state = initialState.workspaces.workspacesSettingsChangesTracker, action) {
    switch (action.type) {
        case `${actionTypes.FILTERS_ACTIONS.FILTER_VALUE_CHANGE}_${JOBS_PAGE_ALIAS}_filters`: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            const settingsChange = {
                id: workspaceGuid,
                value: true
            };

            const settingsChangeAction = {
                type: 'DO_INSERT',
                payload: {
                    id: workspaceGuid,
                    value: settingsChange
                }
            };

            return {
                ...state,
                ...wsSettingsChangesTrackerReducer(state, settingsChangeAction)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACE_SUCCESSFUL:
        case actionTypes.LIST_PAGE_ACTIONS.SAVE_WORKSPACE_SETTINGS_SUCCESSFUL: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            const removeAllChangesAction = {
                type: 'REMOVE_CHANGE',
                id: workspaceGuid
            };

            return {
                ...state,
                ...wsSettingsChangesTrackerReducer(state, removeAllChangesAction)
            };
        }
        default: {
            return {
                ...state
            };
        }
    }
}
