import { omit } from '../../../utils/commonUtils';
import * as actionTypes from '../../../actions/actionTypes';
import initialState from '../../../state/initialState';
import { buildStructure, addItem } from '../../../state/skillStructure/mapCollection';
import { configureWorkspaceSettings } from '../workspacesReducer';


export default function listPageWorkspaceSettingsReducer(state = initialState.listPage.workspaces.workspacesSettings, action) {
    switch (action.type) {
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { workspacesSettings } = payload;

            return {
                ...state,
                ...buildStructure('workspace_guid', workspacesSettings)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL:
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACE_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;

            const loadedWorkspace = configureWorkspaceSettings(data[0]);
            payload.workspaceGuid = loadedWorkspace.workspace_guid;

            return {
                ...state,
                ...addItem(state, (workspace) => workspace, loadedWorkspace)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE_SUCCESS:
        case actionTypes.LIST_PAGE_ACTIONS.DROP_MOST_RECENTLY_USED_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { defaultWorkspaceGuid, workspaceGuid: deletedWorkspaceGuid } = payload;
            const orderedKeysWithoutRemoved = state.orderedKeys.filter(workspaceGuid => workspaceGuid !== deletedWorkspaceGuid);

            if (deletedWorkspaceGuid === defaultWorkspaceGuid) {
                return {
                    ...state
                };
            }

            return {
                ...state,
                orderedKeys: orderedKeysWithoutRemoved,
                map: { ...omit(state.map, [deletedWorkspaceGuid]) }
            };
        }
        case actionTypes.VIEW_SETTINGS_CHANGED: {
            const { payload } = action;
            const { workspaceGuid, newViewSetting } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        subRecTableName: state.map[workspaceGuid].masterRecTableName,
                        masterRecTableName: newViewSetting
                    }
                }
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE_FILTER_SETTINGS: {
            const { payload } = action;
            const { workspaceGuid } = payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...omit(state.map[workspaceGuid], ['filterSettings', 'filterState'])
                    }
                }
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.RESTORE_WORKSPACE_VIEW_SETTINGS: {
            const { workspaceGuid, view, viewConfig } = action.payload;

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        viewsConfig: {
                            ...state.map[workspaceGuid].viewsConfig,
                            [view]: viewConfig
                        }
                    }
                }
            };
        }
        default: {
            return state;
        }
    }
}
