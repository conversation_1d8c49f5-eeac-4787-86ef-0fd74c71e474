import * as actionTypes from '../../../actions/actionTypes';
import { omit } from '../../../utils/commonUtils';
import initialState from '../../../state/initialState';
import { buildStructure, addItem } from '../../../state/skillStructure/mapCollection';
import { configureWorkspaceStructure } from './index';
import { getUpdatedCustomColourFields } from '../../../utils/workspaceUtils';

export default function workspacesStructureReducer(state = initialState.workspaces.workspacesStructure, action) {
    switch (action.type) {
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { workspacesStructure } = payload;

            return {
                ...state,
                ...buildStructure('workspace_guid', workspacesStructure)
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_COPY_WORKSPACE_TEMPLATE_SUCCESSFUL:
        case actionTypes.LIST_PAGE_ACTIONS.LOAD_WORKSPACE_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;
            const workspaceData = configureWorkspaceStructure(data[0]);

            return {
                ...state,
                ... !state.map[workspaceData.workspace_guid] ? addItem(state, (workspace) => workspace, workspaceData) : {}
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.DELETE_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { workspaceGuid : deletedWorkspaceGuid } = payload;
            const orderedKeysWithoutRemoved = state.orderedKeys.filter(workspaceGuid => workspaceGuid !== deletedWorkspaceGuid);

            return {
                ...state,
                orderedKeys: orderedKeysWithoutRemoved,
                map: { ...omit(state.map, [deletedWorkspaceGuid]) }
            };
        }
        case actionTypes.LIST_PAGE_ACTIONS.RENAME_WORKSPACE_SUCCESS:
        case actionTypes.LIST_PAGE_ACTIONS.MOVE_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { workspaceGuid, workspaceData: change } = payload;

            return {
                ...state,
                map:{
                    ...state.map,
                    [workspaceGuid] : {
                        ...state.map[workspaceGuid],
                        ...change
                    }
                }
            };
        }
        case actionTypes.CHANGE_COLOUR_SCHEME: {
            const { customColorScheme, workspaceGuid } = action.payload;
            const { workspace_colourtheme_guid } = customColorScheme;
            const workspace = state.map[workspaceGuid];
            const { workspace_colourtheme_guid: old_colourtheme_guid } = workspace;
            const isCustomThemeSelected = !old_colourtheme_guid && !workspace_colourtheme_guid;
            const updatedCustomColourFields = getUpdatedCustomColourFields(workspace, customColorScheme);

            return {
                ...state,
                map: {
                    ...state.map,
                    [workspaceGuid]: {
                        ...state.map[workspaceGuid],
                        ...updatedCustomColourFields,
                        isCustomThemeSelected
                    }
                }
            };
        }
        default: {

            return state;
        }
    }
}
