import initialState from '../state/initialState';
import { LIST_PAGE_ACTIONS } from '../actions/actionTypes';
import { buildStructure } from '../state/skillStructure/mapCollection';
import { omit } from 'lodash';

const configureWorkspaceStructure = (workspace) => {
    return {
        ...workspace,
        workspace_accesstype: workspace.workspace_accesstype.toLowerCase(),
        workspace_editrights: workspace.workspace_editrights.toLowerCase()
    };
};

export default (state = initialState.listPage, action) => {
    switch (action.type) {
        case LIST_PAGE_ACTIONS.UPDATE_LIST_VIEW: {
            return {
                ...state,
                activeListView: action.payload
            };
        }
        case LIST_PAGE_ACTIONS.UPDATE_WORKSPACE_VIEW_TYPE: {
            return {
                ...state,
                wsViewType: action.payload
            };
        }
        case LIST_PAGE_ACTIONS.LOAD_WORKSPACES_SUCCESSFUL: {
            const { payload } = action;
            const { data } = payload;

            // The data comes as [workspaceStructures, mostRecentlyUsedWorkspaces] from the API
            const workspaceStructures = data[0] || [];
            const mostRecentlyUsedWorkspaces = data[1] || [];

            const configuredWorkspaces = workspaceStructures.map(workspace => configureWorkspaceStructure(workspace));

            // Transform the workspace data into the expected structure
            const workspacesStructure = buildStructure('workspace_guid', configuredWorkspaces);

            // Get the first most recently used workspace or first workspace as selected
            const selectedWorkspace = mostRecentlyUsedWorkspaces.length > 0
                ? mostRecentlyUsedWorkspaces[0].workspace_guid
                : (workspaceStructures.length > 0 ? workspaceStructures[0].workspace_guid : null);

            return {
                ...state,
                workspaces: {
                    ...state.workspaces,
                    workspacesStructure,
                    selected: state.workspaces?.selected || selectedWorkspace,
                    mostRecentlyUsed: mostRecentlyUsedWorkspaces.map(ws => ws.workspace_guid),
                    workspacesSettings: {
                        mapField: 'workspace_guid',
                        orderedKeys: configuredWorkspaces.map(ws => ws.workspace_guid),
                        map: configuredWorkspaces.reduce((acc, ws) => {
                            acc[ws.workspace_guid] = { workspace_guid: ws.workspace_guid };

                            return acc;
                        }, {})
                    },
                    workspacesStructureChanges: state.workspaces?.workspacesStructureChanges || {
                        mapField: 'id',
                        inserts: { orderedKeys: [], map: {} },
                        updates: { orderedKeys: [], map: {} },
                        deletes: { orderedKeys: [], map: {} }
                    }
                }
            };
        }
        case LIST_PAGE_ACTIONS.RENAME_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { workspaceGuid, workspaceData: change } = payload;

            return {
                ...state,
                workspaces: {
                    ...state.workspaces,
                    workspacesStructure: {
                        ...state.workspaces.workspacesStructure,
                        map: {
                            ...state.workspaces.workspacesStructure.map,
                            [workspaceGuid]: {
                                ...state.workspaces.workspacesStructure.map[workspaceGuid],
                                ...change
                            }
                        }
                    }
                }
            };
        }
        case LIST_PAGE_ACTIONS.DELETE_WORKSPACE_SUCCESS: {
            const { payload } = action;
            const { workspaceGuid: deletedWorkspaceGuid } = payload;
            const orderedKeysWithoutRemoved = state.workspaces.workspacesStructure.orderedKeys.filter(workspaceGuid => workspaceGuid !== deletedWorkspaceGuid);

            return {
                ...state,
                workspaces: {
                    ...state.workspaces,
                    workspacesStructure: {
                        ...state.workspaces.workspacesStructure,
                        orderedKeys: orderedKeysWithoutRemoved,
                        map: omit(state.workspaces.workspacesStructure.map, [deletedWorkspaceGuid])
                    },
                    mostRecentlyUsed: state.workspaces.mostRecentlyUsed.filter(item => item !== deletedWorkspaceGuid)
                }
            };
        }
        case LIST_PAGE_ACTIONS.SET_CREATE_WORKSPACE_CHANGE: {
            const { payload } = action;
            const { newWSUUID, newWorkspaceStructure } = payload;

            return {
                ...state,
                workspaces: {
                    ...state.workspaces,
                    workspacesStructureChanges: {
                        ...state.workspaces.workspacesStructureChanges,
                        inserts: {
                            ...state.workspaces.workspacesStructureChanges.inserts,
                            orderedKeys: [...(state.workspaces.workspacesStructureChanges.inserts.orderedKeys || []), newWSUUID],
                            map: {
                                ...state.workspaces.workspacesStructureChanges.inserts.map,
                                [newWSUUID]: {
                                    change: {
                                        id: newWSUUID,
                                        value: newWorkspaceStructure
                                    }
                                }
                            }
                        }
                    }
                }
            };
        }
        default: {
            return state;
        }

    }
};