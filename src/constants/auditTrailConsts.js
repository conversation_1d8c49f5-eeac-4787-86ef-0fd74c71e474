import { TABLE_NAMES } from './globalConsts';

export const AUDIT_TRAIL_ENTRY_CONSTS = {
    PREVIOUS_VALUES_PREFIX: 'from',
    ACTOR_PREFIX: 'by',
    NOT_FOUND_PREFIX: 'value',
    NOT_FOUND_SUFFIX: 'not found',
    ENTITY_CREATED_SUFFIX: 'created',
    ENTITY_DELETED_SUFFIX: 'deleted'
};

export const TIMELINE_DOT_COLOR = {
    DEFAULT: '#2A66E2', // blue (info/color)
    CREATE: '#018848', // green (primary.9)
    DELETE: '#D33737' // red (error/color)
};

export const TIMELINE_ACTION_TEXT = {
    CREATE: 'added',
    UPDATE: 'updated',
    UPDATE_SUFFIX: 'to',
    DELETE: 'removed',
    REMOVE: 'removed'
};

export const TIMELINE_ACTION_ALIAS = {
    CREATE: 'timeLineActionCreateAlias',
    UPDATE: 'timeLineActionUpdateAlias',
    UPDATE_SUFFIX: 'timeLineActionUpdateSuffixAlias',
    DELETE: 'timeLineActionDeleteAlias',
    REMOVE: 'timeLineActionRemoveAlias'
};

export const TIMELINE_BACK_TO_TOP = 'Back to top';
export const TIMELINE_SHOW_MORE = 'Show more';
export const TIMELINE_SHOW_MORE_LOGS_COUNT = 5;
export const TIMELINE_DEFAULT_LOGS_COUNT = 10;
export const TIMELINE_EXPAND_COLLAPSE_MIN_ITEMS_COUNT = 2;
export const TIMELINE_SORT_LABEL = 'Sort';

export const CUSTOM_FIELD_DATA_TYPES = {
    BOOKING_NON_WORK: 'BookingNonWorkType'
};

export const AUDIT_ACTION_TYPES = {
    INSERT: 'I',
    UPDATE: 'U',
    DELETE: 'D'
};

export const AUDIT_ACTIONS = {
    [AUDIT_ACTION_TYPES.INSERT]: {
        type: AUDIT_ACTION_TYPES.INSERT,
        color: TIMELINE_DOT_COLOR.CREATE,
        text: TIMELINE_ACTION_TEXT.CREATE,
        textAlias: TIMELINE_ACTION_ALIAS.CREATE
    },
    [AUDIT_ACTION_TYPES.UPDATE]: {
        type: AUDIT_ACTION_TYPES.UPDATE,
        color: TIMELINE_DOT_COLOR.DEFAULT,
        text: TIMELINE_ACTION_TEXT.UPDATE,
        textSuffix: TIMELINE_ACTION_TEXT.UPDATE_SUFFIX,
        textAlias: TIMELINE_ACTION_ALIAS.UPDATE,
        textSuffixAlias: TIMELINE_ACTION_ALIAS.UPDATE_SUFFIX
    },
    [AUDIT_ACTION_TYPES.DELETE]: {
        type: AUDIT_ACTION_TYPES.DELETE,
        color: TIMELINE_DOT_COLOR.DELETE,
        text: TIMELINE_ACTION_TEXT.DELETE,
        textAlias: TIMELINE_ACTION_ALIAS.DELETE
    }
};

export const ICON_TYPE_BY_TABLE_NAME = {
    [TABLE_NAMES.RESOURCE]: 'user',
    [TABLE_NAMES.JOB]: 'job',
    [TABLE_NAMES.BOOKING]: 'booking',
    [TABLE_NAMES.CLIENT]: 'client'
};

export const CUSTOM_FIELD_TYPES_MAP = {
    'booking_nonwork': CUSTOM_FIELD_DATA_TYPES.BOOKING_NON_WORK
};

export const SKILLS_APPROVAL_HISTORY_CONST = {
    ENTITY_LEVEL_PREFIX: 'Level',
    ENTITY_DATE_CERTIFIED: 'Date certified',
    PREVIOUS_VALUES_PREFIX: 'from',
    ACTOR_PREFIX: 'by',
    ENTITY_APPROVED: 'Approved',
    DEFAULT_PAGE_NUMBER: 1,
    ENTITY_REJECTED: 'Rejected'
};

export const SKILLS_APPROVAL_AUDIT_ACTION_TYPES = {
    INSERT: 'Insert',
    UPDATE: 'Update',
    DELETE: 'Delete'
};

export const SKILLS_APPROVAL_TIMELINE_ACTION_TEXT = {
    CREATE: 'New skill',
    UPDATE: 'Skill update',
    DELETE: 'Skill removal'
};

export const SKILLS_APPROVAL_STATUS_HISTORY = {
    APPROVED: 'approved',
    REJECTED: 'rejected'
};

export const SKILLS_APPROVAL_AUDIT_ACTIONS = {
    [SKILLS_APPROVAL_AUDIT_ACTION_TYPES.INSERT]: {
        type: SKILLS_APPROVAL_AUDIT_ACTION_TYPES.INSERT,
        color: TIMELINE_DOT_COLOR.CREATE,
        text: SKILLS_APPROVAL_TIMELINE_ACTION_TEXT.CREATE
    },
    [SKILLS_APPROVAL_AUDIT_ACTION_TYPES.UPDATE]: {
        type: SKILLS_APPROVAL_AUDIT_ACTION_TYPES.UPDATE,
        color: TIMELINE_DOT_COLOR.DEFAULT,
        text: SKILLS_APPROVAL_TIMELINE_ACTION_TEXT.UPDATE
    },
    [SKILLS_APPROVAL_AUDIT_ACTION_TYPES.DELETE]: {
        type: SKILLS_APPROVAL_AUDIT_ACTION_TYPES.DELETE,
        color: TIMELINE_DOT_COLOR.DELETE,
        text: SKILLS_APPROVAL_TIMELINE_ACTION_TEXT.DELETE
    }
};