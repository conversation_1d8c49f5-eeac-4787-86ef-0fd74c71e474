import { FILTER_FIELD_NAMES, TABLE_NAMES } from './globalConsts';

export const FIELD_DATA_TYPES = {
    ID: 'ID',
    DATE_TIME: 'DateTime',
    STRING: 'String',
    INT: 'Int',
    FLOAT: 'Float',
    BOOL: 'Bool',
    SKILL_LEVEL: 'SkillLevel'
};

export const FIELD_DATA_CATEGORIES = {
    CALCULATED: 'Calculated',
    MULTI_VALUE: 'MultiValue',
    HISTORY: 'History',
    SYSTEM_MAINTAINED: 'SystemMaintained',
    DATA: 'Data',
    FREE_TEXT_HISTORY: 'FreeTextHistory'
};

export const FIELD_DATA_UIFIELD_CATEGORIES = {
    SYSTEM_REQUIRED: 'SystemRequired',
    SYSTEM_READONLY: 'SystemReadonly',
    SYSTEM_HIDDEN: 'SystemHidden',
    DATABASE_FIELD: 'DatabaseField',
    CALC_FIELD: 'CalcField'
};

export const FIELD_DATA_ACCESS_LEVEL = {
    EDITABLE: 'Editable',
    READONLY: 'ReadOnly',
    HIDDEN: 'Hidden',
    MASKED: 'Masked',
    READ_ONLY: 'Read only'
};

export const BOOKING_FIXEDTIME_VALUES = {
    BOOKING_LOADING: 0,
    BOOKING_TIME: 1,
    BOOKING_HOURS_PER_DAY: 2
};

export const ROLEREQUEST_FIXEDTIME_VALUES = {
    ROLEREQUEST_LOADING: 0,
    ROLEREQUEST_TIME: 1,
    ROLEREQUEST_HOURS_PER_DAY: 2,
    ROLEREQUEST_FTE: 3,
    ROLEREQUEST_RESOURCE_DEMAND: 4
};

export const CHARGE_MODE_VALUES = {
    RESOURCE_CHARGE_RATE: 0,
    DIFFERENT_CHARGE_RATE: 1,
    CUSTOM_RATE: 2
};

export const BOOLEAN_FIELD_DB_VALUES = {
    TRUE: 1,
    FALSE: 0
};

export const CONTROL_FIELD_TYPES = {
    RADIO_OPTION: 'radioOption',
    RADIO_VALUE: 'radioValue',
    ADDITIONAL_FIELD: 'addtionalField',
    DATE_RANGE: 'dateRange',
    CONTAINER: 'container'
};

export const CONTROL_FIELD_DISPLAY_TYPES = {
    NESTED: 'nested'
};

export const FIELD_TYPES = {
    STRING: {
        PLAIN_TEXT: 'Plain text',
        FORMATTED_TEXT: 'Formatted text'
    },
    DECIMAL: 'Decimal Number',
    MULTI_VALUE_LOOKUP: 'Multi-select lookup',
    PLANNING_DATA_LOOKUP: 'Planning data lookup',
    LOOKUP: 'Lookup'
};

export const CUSTOM_READONLY_FIELD_TYPES = {
    TAG: 'Tag'
};

export const RESOURCE_FIRSTNAME = 'resource_firstname';
export const RESOURCE_LASTNAME = 'resource_lastname';
export const RESOURCE_DESCRIPTION = 'resource_description';
export const RESOURCE_ACTIVE_COUNT = 'resource_active_count';
export const RESOURCE_SURROGATE_ID = 'resource_surrogate_id';
export const RESOURCE_EMAIL = 'resource_email';
export const RESOURCE_USERSTATUS = 'resource_userstatus';
export const RESOURCE_OVERLAPPING_BOOKINGS = 'resource_overlapping_bookings';
export const RESOURCE_OVERLAPPING_ROLEREQUESTS = 'resource_overlapping_rolerequests';
export const RESOURCE_CAN_BE_ASSIGNED_TO_ROLE = 'resource_canbeassignedtorole';
export const RESOURCE_BOOKING_POTENTIAL_CONFLICT_DATES = 'resource_booking_potential_conflict_dates';
export const RESOURCE_BOOKING_CONFLICT_DATES = 'resource_booking_conflict_dates';
export const RESOURCE_IS_APPLICANT = 'resource_is_applicant';
export const RESOURCE_APPLICANT_APPLYDATE = 'resource_applicant_applydate';
export const RESOURCE_LAST_LOGIN = 'resource_last_login';
export const RESOURCE_PRIMARY_SKILL = 'resource_primary_skill';
export const RESOURCE_SECONDARY_SKILL = 'resource_secondary_skill';
export const RESOURCE_IS_SERVICE_ACCOUNT = 'resource_isserviceaccount';
export const RESOURCE_UPDATEDBY_RESOURCE_GUID = 'resource_updatedby_resource_guid';
export const RESOURCE_CREATEDBY_RESOURCE_GUID = 'resource_createdby_resource_guid';
export const RESOURCE_SECURITYPROFILE_GUID = 'resource_securityprofile_guid';
export const RESOURCE_ISSERVICEACCOUNT = 'resource_isserviceaccount';
export const RESOURCE_CMERED = 'resource_cmered';
export const RESOURCE_CMEBLUE = 'resource_cmeblue';
export const RESOURCE_CMEGREEN = 'resource_cmegreen';
export const RESOURCE_CMEYELLOW = 'resource_cmeyellow';
export const RESOURCE_CMETOPCOLOUR = 'resource_cmetopcolour';
export const CME_PERCENTAGES = 'cme_percentages';
export const CME_BLUE = 'blue';
export const CME_RED = 'red';
export const CME_GREEN = 'green';
export const CME_YELLOW = 'yellow';
export const SKILL_EXPIRY_STATUS = 'skill_expiry_status';
export const RECOMMENDATION_VIEWED = 'resource_recommendation_viewed';
export const RESOURCE_APPROVAL = 'resource_skill_approval_access';

export const RESOURCE_SECURITYPROFILE_DESCRIPTION = 'resource_securityprofile_description';

export const JOB_GUID = 'job_guid';
export const JOB_DESCRIPTION = 'job_description';
export const JOB_IS_EXCLUDE_FROM_UTILISATION = 'job_isExcludeFromUtilisation';
export const JOB_TOTAL_ACTIONABLE_REQUESTS = 'job_totalactionablerequests';
export const JOB_JOBSTATUS_DESCRIPTION = 'job_jobstatus_description';
export const JOB_JOBSTATUS_GUID = 'job_jobstatus_guid';
export const JOB_CLIENT_GUID = 'job_client_guid';
export const JOB_START_DATE = 'job_start';
export const JOB_END_DATE = 'job_end';
export const JOB_PREVIOUS_JOB_GUID = 'job_previous_job_guid';
export const JOB_NEXT_JOB_GUID = 'job_next_job_guid';
export const JOB_CODE = 'job_code';
export const JOB_TOTALROLEGROUPS = 'job_totalrolegroups';
export const JOB_CHARGETYPE_GUID = 'job_chargetype_guid';
export const JOB_TOTALPROFIT = 'job_totalprofit';
export const JOB_ENGAGEMENTLEAD_RESOURCE_GUID = 'job_engagementlead_resource_guid';
export const JOB_UPDATEDBY_RESOURCE_GUID = 'job_updatedby_resource_guid';
export const JOB_SURROGATE_ID = 'job_surrogate_id';
export const JOB_MILESTONES = 'job_milestones';
export const JOB_RESOURCE_WORKED_HOURS = 'job_resource_worked_hours';
export const JOB_BILLINGTYPE_GUID = 'job_billingtype_guid';
export const JOB_PROFITMARGIN = 'job_profitmargin';
export const JOB_PROFIT_MARGIN_TARGET = 'job_profitmargintarget';
export const JOB_FIXEDPRICE = 'job_fixedprice';
export const JOB_LONG_RUNNING_OPERATION = 'job_long_running_operation';
export const JOB_CREATEDBY_RESOURCE_GUID = 'job_createdby_resource_guid';
export const JOB_EARLIEST_BOOKING_START_DATE_IN_RANGE = 'job_earliest_booking_start_date_in_range';
export const JOB_EARLIEST_BOOKING_START_DATE = 'job_earliest_booking_start_date';
export const JOB_LATEST_BOOKING_END_DATE = 'job_latest_booking_end_date';
export const JOB_NEWSTART = 'job_newstart';
export const JOB_TIME_RAGHEALTH_GUID = 'job_time_raghealth_guid';
export const JOB_COST_RAGHEALTH_GUID = 'job_cost_raghealth_guid';
export const JOB_QUALITY_RAGHEALTH_GUID = 'job_quality_raghealth_guid';
export const JOB_TOTAL_RAGHEALTH_GUID = 'job_total_raghealth_guid';
export const JOB_TOTAL_RAGHEALTH_DESCRIPTION = 'job_total_raghealth_description';
export const JOB_BOOKING_COUNT_IN_RANGE = 'job_booking_count_inrange';
export const JOB_BOOKED_TIME_IN_RANGE = 'job_booked_time_inrange';
export const JOB_BOOKING_ROLL_FORWARD_TIME_IN_RANGE = 'job_booking_rollforward_time_inrange';
export const JOB_HOURS_BUDGET = 'job_hoursbudget';
export const BOOKING_RESOURCE_GUIDS = 'booking_resource_guids';
export const BOOKING_RESOURCE_GUID = 'booking_resource_guid';
export const BOOKING_JOB_GUID = 'booking_job_guid';
export const BOOKING_LOADING = 'booking_loading';
export const BOOKING_FIXEDTIME = 'booking_fixedtime';
export const BOOKING_CHARGEMODE = 'booking_chargemode';
export const BOOKING_STATUS = 'booking_bookingtype_guid';
export const BOOKING_START = 'booking_start';
export const BOOKING_END = 'booking_end';
export const BOOKING_GUID = 'booking_guid';
export const BOOKING_TIME_ALLOCATION = 'booking_time_allocation';
export const BOOKING_DIARY_HOURS = 'booking_diary_hours';
export const BOOKING_DIARY_DAYS = 'booking_diary_days';
export const BOOKING_DATE_RANGE = 'booking_date_range';
export const BOOKING_JOB_OVERBUDGET = 'booking_job_overbudget';
export const BOOKING_CREATEDBY_RESOURCE_GUID = 'booking_createdby_resource_guid';
export const BOOKING_UPDATEDBY_RESOURCE_GUID = 'booking_updatedby_resource_guid';
export const BOOKING_SERIES_GUID = 'booking_bookingseries_guid';
export const BOOKING_HOURS_PER_DAY = 'booking_hours_per_day';
export const BOOKING_TIME = 'booking_time';

export const START_DATE = 'start_date';
export const END_DATE = 'end_date';

export const BOOKINGTYPE_GUID = 'bookingtype_guid';
export const BOOKINGTYPE_DESCRIPTION = 'bookingtype_description';

export const REPEAT_BOOKING_EVERY = 'repeat_booking_every';
export const REPEAT_BOOKING_UNTIL = 'repeat_booking_until';

export const RESOURCE_GUIDS = 'resource_guids';
export const RESOURCE_GUID = 'resource_guid';

export const RESOURCE_SHORTLIST_ID = 'resource_shortlist_id';
export const RESOURCE_SHORTLISTED_BY_NAME = 'resource_shortlisted_by_name';

export const BOOKING_TOTAL_COST = 'booking_totalcost';
export const BOOKING_TOTAL_REVENUE = 'booking_totalrevenue';
export const BOOKING_TOTAL_PROFIT = 'booking_totalprofit';
export const BOOKING_COST_PER_HOUR = 'booking_costperhour';
export const BOOKING_REVENUE_PER_HOUR = 'booking_revenueperhour';

export const BOOKING_CHARGEMODE_SECTION_FIELD = 'booking_chargemode_section_field';
export const BOOKING_OVERRIDDEN_CHARGERATE_GUID = 'booking_overridden_chargerate_guid';
export const BOOKING_CHARGE_RATES_VIEW_MODE_FIELDS_ROW = 'Booking charge rates view mode fields row';
export const BOOKING_REVENUE_RATES_FIELDS_ROW = 'Booking revenue rates fields row';
export const BOOKING_COST_RATES_FIELDS_ROW = 'Booking cost rates fields row';
export const BOOKING_PROFIT_RATES_FIELDS_ROW = 'Booking profit rates fields row';
export const BOOKING_PROFITPERHOUR_FIELDNAME = 'booking_profitperhour_appinternalfield';

export const DIARY_GROUP = 'Diary Group';

export const FIELD_LOOKUP_VALUE_TABLE = 'fieldlookupvalue';
export const CUSTOM_FIELD_TYPES = {
    LOOKUP: 'Lookup',
    PLANNING_DATA_LOOKUP: 'PlanningDataLookup',
    MULTI_VALUE: 'MultiValue',
    HISTORY: 'History',
    PRIMITIVE: 'Primitive'
};

export const ROLEREQUEST_TIME_ALLOCATION = 'rolerequest_time_allocation';
export const CRITERIA_ROLEREQUEST_TIME_ALLOCATION = 'criteria_rolerequest_time_allocation';
export const ROLEREQUEST_DATE_RANGE = 'rolerequest_date_range';
export const ROLEREQUEST_CHARGEMODE = 'rolerequest_chargemode';

export const ROLEREQUEST_CHARGE_RATES_VIEW_MODE_FIELDS_ROW = 'Rolerequest charge rates view mode fields row';

export const ROLEREQUEST_REVENUE_RATES_FIELDS_ROW = 'Rolerequest revenue rates fields row';
export const ROLEREQUEST_ESTIMATE_REVENUE_RATES_FIELDS_ROW = 'Rolerequest estimate revenue rates fields row';

export const ROLEREQUEST_COST_RATES_FIELDS_ROW = 'Rolerequest cost rates fields row';
export const ROLEREQUEST_ESTIMATE_COST_RATES_FIELDS_ROW = 'Rolerequest estimate cost rates fields row';

export const ROLEREQUEST_PROFIT_RATES_FIELDS_ROW = 'Rolerequest profit rates fields row';
export const ROLEREQUEST_ESTIMATE_PROFIT_RATES_FIELDS_ROW = 'Rolerequest estimate profit rates fields row';

export const ROLEREQUEST_CHARGEMODE_SECTION_FIELD = 'rolerequest_chargemode_section_field';
export const ROLEREQUEST_ESTIMATE_CHARGEMODE_SECTION_FIELD = 'rolerequest_estimate_chargemode_section_field';

export const ROLEREQUESTRESOURCE_REVENUE_RATES_FIELDS_ROW = 'Rolerequestresource revenue rates fields row';
export const ROLEREQUESTRESOURCE_COST_RATES_FIELDS_ROW = 'Rolerequestresource cost rates fields row';
export const ROLEREQUESTRESOURCE_PROFIT_RATES_FIELDS_ROW = 'Rolerequestresource profit rates fields row';
export const ROLEREQUESTRESOURCE_CHARGEMODE_SECTION_FIELD = 'Rolerequestresource_chargemode_section_field';

export const ROLEREQUEST_ROLEGROUP_SELECTION_FIELD = 'rolerequest_rolegroup_selection_field';
export const ROLEREQUEST_PROFITPERHOUR_FIELDNAME = 'rolerequest_profitperhour_appinternalfield';
export const ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME = 'rolerequest_estimateprofitperhour_appinternalfield';
export const ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME = 'rolerequestresource_profitperhour_appinternalfield';

export const CHARGERATE_CURRENT_VALUE_FIELDNAME = 'chargerate_current_value';
export const CRITERIA_AUDIT_KEY = 'criterias';
export const ATTRIBUTE_CRITERIA_AUDIT_KEY = 'Attributes';
export const SKILLS_CRITERIA_AUDIT_KEY = 'Skills';

export const SKILL_FILTER_ALIAS = 'Skills';

const COMMON_PROFITERHOUR_FIELD_INFO = {
    category: FIELD_DATA_CATEGORIES.DATA,
    dataType: FIELD_DATA_TYPES.FLOAT,
    dateSensitivity: 'None',
    fieldtype: FIELD_TYPES.DECIMAL,
    iscustom: false,
    mandatory: false,
    max: null,
    min: null,
    readOnly: true,
    appInternalField: true
};

export const RAG_FIELDS = [
    JOB_TIME_RAGHEALTH_GUID,
    JOB_COST_RAGHEALTH_GUID,
    JOB_QUALITY_RAGHEALTH_GUID
];

export const APP_FIELDS_FIELDINFOS = {
    [TABLE_NAMES.BOOKING]: {
        [BOOKING_PROFITPERHOUR_FIELDNAME]: {
            ...COMMON_PROFITERHOUR_FIELD_INFO,
            name: BOOKING_PROFITPERHOUR_FIELDNAME,
            alias: BOOKING_PROFITPERHOUR_FIELDNAME
        }
    },
    [TABLE_NAMES.ROLEREQUEST]: {
        [ROLEREQUEST_PROFITPERHOUR_FIELDNAME]: {
            ...COMMON_PROFITERHOUR_FIELD_INFO,
            name: ROLEREQUEST_PROFITPERHOUR_FIELDNAME,
            alias: ROLEREQUEST_PROFITPERHOUR_FIELDNAME
        },
        [ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME]: {
            ...COMMON_PROFITERHOUR_FIELD_INFO,
            name: ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME,
            alias: ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME
        }
    },
    [TABLE_NAMES.RESOURCE]: {
        [FILTER_FIELD_NAMES.SKILL]: {
            name: FILTER_FIELD_NAMES.SKILL,
            alias: SKILL_FILTER_ALIAS,
            category: FIELD_DATA_CATEGORIES.DATA,
            dataType: FIELD_DATA_TYPES.SKILL_LEVEL,
            dateSensitivity: 'None',
            fieldtype: FIELD_TYPES.SKILL_LEVEL,
            iscustom: false,
            mandatory: false,
            max: null,
            min: null,
            readOnly: true,
            appInternalField: true
        }
    },
    [TABLE_NAMES.ROLEREQUESTRESOURCE]: {
        [ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME]: {
            ...COMMON_PROFITERHOUR_FIELD_INFO,
            name: ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME,
            alias: ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME,
            table: TABLE_NAMES.ROLEREQUESTRESOURCE
        }
    }
};

export const EW_REQUEST_JOINED_FIELDS = {
    [TABLE_NAMES.BOOKING]: [
        CHARGERATE_CURRENT_VALUE_FIELDNAME
    ],
    [TABLE_NAMES.ROLEREQUEST]: [
        CHARGERATE_CURRENT_VALUE_FIELDNAME
    ],
    [TABLE_NAMES.ROLEREQUESTRESOURCE]: [
        CHARGERATE_CURRENT_VALUE_FIELDNAME
    ]
};

export const BOOKING_BUDGET_DETAILS_TEXT = 'Budget calculations are using the charge rate that was valid on the first day of the booking.';
export const ROLE_BUDGET_DETAILS_TEXT = 'Budget calculations are using the charge rate that was valid on the first day of the role.';

export const ASSIGNEES_TOTALS_DIFFERENCE_TEXT = 'Actual totals may differ if assignees have different diaries or charge rates to the estimation.';

export const BOOKING_MULTIPLE_RESOURCES_BUDGET_DETAILS_TEXT = `Specific budget rates for each booking can be seen in their details after creation.
Budget calculations are using the charge rate that was valid on the first day of the booking.`;

export const ROLEREQUEST_UNASSIGNED_CRITERIA_ROLE_TEXT = 'Budget info will be calculated when resources are assigned to the role.';

export const CRITERIA_ROLE_ASSIGN_RESOURCE_TO_CALCULATE_BUDGET_TEXT = 'To calculate the budget you need to assign resources to the role.';

export const REQUIREMENTS_SECTION_INSUFFICIENT_PERMISSION_TEXT = 'Some requirements are hidden as you do not have sufficient permissions';

export const ASSIGN_RESOURCE_ROLE_TO_CRITERIA = 'Assign a resource to the role through the Suggestions pane.';

export const CHANGE_ASSIGNED_CRITERIA_RESOURCE = 'Change the assignment to the role through the Suggestions pane.';

export const ROLE_POST_ON_MARKETPLACE = 'The role post will end automatically after role end date - 18 Jul 22';

export const ROLE_APPLICATION_TEXT = 'You have applied to this role on 23 Jul 22.';

export const BOOKING_JOB_OVERBUDGET_TEXT = 'This booking will take this job over its budget.';

export const BOOKING_RESOURCE_OVERBUDGET_TEXT = 'Booking this resource will take this job over its budget.';

export const BOOKING_JOB_HOURS_OVERBUDGET_TEXT = 'This booking will put this job\'s Total hours at ${jobHoursPercentageBudget} of its Budget hours';

export const RESOURCE_CHARGE_RATE_AND_DIARY_TEXT = 'We recommend modifying ${chargeRateAlias} and ${diaryGroupAlias} outside of work hours as recalculating time and financials can take multiple hours depending on how many resources and bookings this change affects (this may include past bookings). Users will experience longer load times while this is happening.';

export const VALIDATION_REQUIRED_FIELDS = {
    [TABLE_NAMES.BOOKING]: [
        BOOKING_END,
        BOOKING_START
    ],
    [TABLE_NAMES.JOB]: [],
    [TABLE_NAMES.RESOURCE]: [],
    [TABLE_NAMES.CLIENT]: [],
    [TABLE_NAMES.ROLEREQUEST]: [
        'rolerequest_end',
        'rolerequest_start'
    ],
    [TABLE_NAMES.ROLEREQUESTRESOURCE]: [],
    [TABLE_NAMES.ROLEREQUESTGROUP]: []
};

export const ROLEREQUESTSTATUS_FIELDS = {
    GUID: 'rolerequeststatus_guid',
    DESCRIPTION: 'rolerequeststatus_description'
};

export const ROLEREQUEST_FIELDS = {
    GUID: 'rolerequest_guid',
    DESCRIPTION: 'rolerequest_description',
    TOTALCOST: 'rolerequest_totalcost',
    TOTALREVENUE: 'rolerequest_totalrevenue',
    TOTALPROFIT: 'rolerequest_totalprofit',
    COST_PER_HOUR: 'rolerequest_costperhour',
    REVENUE_PER_HOUR: 'rolerequest_revenueperhour',
    START: 'rolerequest_start',
    END: 'rolerequest_end',
    LOADING: 'rolerequest_loading',
    FIXEDTIME: 'rolerequest_fixedtime',
    FTE: 'rolerequest_fte',
    CHARGEMODE: 'rolerequest_chargemode',
    RESOURCE_GUID: 'rolerequest_resource_guid',
    RESOURCE_GUIDS: 'rolerequest_resource_guids',
    JOB_GUID: 'rolerequest_job_guid',
    STATUS: 'rolerequest_status',
    STATUS_GUID: 'rolerequest_rolerequeststatus_guid',
    STATUS_DESCRIPTION: 'rolerequest_rolerequeststatus_description',
    ROLE_GROUP_GUID: 'rolerequest_rolerequestgroup_guid',
    LOCATION_GUID: 'rolerequest_location_guid',
    CREATEDBY_RESOURCE_GUID: 'rolerequest_createdby_resource_guid',
    UPDATEDBY_RESOURCE_GUID: 'rolerequest_updatedby_resource_guid',
    CREATEON: 'rolerequest_createdon',
    UPDATEDON: 'rolerequest_updatedon',
    HASCRITERIA: 'rolerequest_hascriteria',
    HOURS_PER_DAY: 'rolerequest_hours_per_day',
    JOB_DESCRIPTION: 'rolerequest_job_description',
    TIME: 'rolerequest_time',
    DIARY_DAYS: 'rolerequest_diary_days',
    DIARY_HOURS: 'rolerequest_diary_hours',
    RESOURCE_DEMAND: 'rolerequest_resourcedemand_count',
    AVAILABLE_STATES: 'rolerequest_availablestates',
    CAN_ASSIGN: 'rolerequest_can_assign',
    FILLED_PERCENTAGE: 'rolerequest_filled_percentage',
    HAS_POTENTIAL_CONFLICTS: 'rolerequest_haspotentialconflicts',
    OVERRIDDEN_CHARGE_RATE_GUID: 'rolerequest_overridden_chargerate_guid',
    ROLEREQUESTRESOURCE_TOTALCOST: 'rolerequest_rolerequestresource_totalcost',
    ROLEREQUESTRESOURCE_TOTALREVENUE: 'rolerequest_rolerequestresource_totalrevenue',
    ROLEREQUESTRESOURCE_TOTALPROFIT: 'rolerequest_rolerequestresource_totalprofit',
    NONWORK: 'rolerequest_nonwork',
    POTENTIAL_CONFLICTS: 'rolerequest_booking_potential_conflict_dates',
    DEMAND_FULFILLED: 'rolerequest_demand_fulfilled',
    CAN_MANAGE_BUDGET: 'rolerequest_is_managable',
    CAN_MOVE_PENDING: 'rolerequest_move_pending',
    CAN_REMOVE_PENDING: 'rolerequest_remove_pending',
    NOTES: 'rolerequest_notes',
    REQUIREMENTS_TAGS: 'rolerequest_requirements_tags',
    CATEGORY: 'rolerequest_category',
    CATEGORY_GUID: 'rolerequest_category_guid',
    AVAILABILITY: 'rolerequest_caller_availability',
    PUBLISHEDON: 'rolerequest_publishedon',
    STARTON: 'rolerequest_starton',
    BAR_TEXT: 'rolerequest_bar_text',
    IS_TEMPLATE: 'rolerequest_isTemplate',
    INFO: 'rolerequest_info',
    SURROGATE_ID: 'rolerequest_surrogate_id',
    WORK_ACTIVITY: 'rolerequest_workactivity_guid',
    CAN_PUBLISH: 'rolerequest_marketplace_canpublish',
    CONTAINS_REQUIREMENTS: 'rolerequest_containsrequirements',
    ROLEREQUEST_IS_CALLER_APPLICANT: 'rolerequest_is_caller_applicant',
    ROLEREQUEST_CALLER_APPLY_DATE: 'rolerequest_caller_apply_date',
    PUBLICATION_GUID: 'rolerequest_publication_guid',
    CRITERIA_MATCH: 'rolerequest_criteriamatch',
    APPLICANT_COUNT: 'rolerequest_applicant_count',
    PENDING_DEMAND: 'rolerequest_pending_demand',
    ESTIMATE_COSTPERHOUR: 'rolerequest_estimatecostperhour',
    ESTIMATE_REVENUEPERHOUR: 'rolerequest_estimaterevenueperhour',
    ESTIMATE_PROFITPERHOUR: 'rolerequest_estimateprofitoerhour',
    ESTIMATE_TOTALPROFIT: 'rolerequest_estimatetotalprofit',
    ESTIMATE_TOTALCOST: 'rolerequest_estimatetotalcost',
    ESTIMATE_TOTALREVENUE: 'rolerequest_estimatetotalrevenue',
    ESTIMATE_CHARGEMODE: 'rolerequest_estimatechargemode',
    DIARY_GROUP: 'rolerequest_diarygroup_guid',
    CHARGE_RATE_GUID: 'rolerequest_chargerate_guid',
    ROLEREQUEST_IS_ASSIGNED: 'rolerequest_is_assigned'
};

export const ROLEREQUEST_ESTIMATE_FIELDS = [
    //Estimation per hour
    ROLEREQUEST_FIELDS.ESTIMATE_COSTPERHOUR,
    ROLEREQUEST_FIELDS.ESTIMATE_REVENUEPERHOUR,
    ROLEREQUEST_FIELDS.ESTIMATE_PROFITPERHOUR,

    //Charge mode
    ROLEREQUEST_FIELDS.ESTIMATE_CHARGEMODE,

    //Estimation totlas
    ROLEREQUEST_FIELDS.ESTIMATE_TOTALCOST,
    ROLEREQUEST_FIELDS.ESTIMATE_TOTALPROFIT,
    ROLEREQUEST_FIELDS.ESTIMATE_TOTALREVENUE,

    //Charge rate for estimation
    ROLEREQUEST_FIELDS.CHARGE_RATE_GUID
];

export const ROLEREQUESTRESOURCE_FIELDS = {
    GUID: 'rolerequestresource_guid',
    RESOURCE_GUID: 'rolerequestresource_resource_guid',
    ROLEREQUEST_GUID: 'rolerequestresource_rolerequest_guid',
    STATUS_GUID: 'rolerequestresource_rolerequeststatus_guid',
    FTE: 'rolerequestresource_fte',
    HOURS_PER_DAY: 'rolerequestresource_hours_per_day',
    TOTAL_COST: 'rolerequestresource_totalcost',
    COST_PER_HOUR: 'rolerequestresource_costperhour',
    TOTAL_PROFIT: 'rolerequestresource_totalprofit',
    TOTAL_REVENUE: 'rolerequestresource_totalrevenue',
    REVENUE_PER_HOUR: 'rolerequestresource_revenueperhour',
    CHARGE_MODE: 'rolerequestresource_chargemode',
    OVERRIDDEN_CHARGE_RATE_GUID: 'rolerequestresource_overridden_chargerate_guid',
    ROLEGROUP_GUID: 'rolerequestresource_rolegroup_guid',
    DESCRIPTION: 'rolerequestresource_description',
    START: 'rolerequestresource_start',
    END: 'rolerequestresource_end',
    JOB_GUID: 'rolerequestresource_job_guid',
    JOB_DESCRIPTION: 'rolerequestresource_job_description',
    ROLEGROUP_DESCRIPTION: 'rolerequestresource_rolegroup_description',
    ROLEREQUEST_DESCRIPTION: 'rolerequestresource_rolerequest_guid.rolerequest_description',
    FIXED_TIME: 'rolerequestresource_rolerequest_fixedtime',
    HASCRITERIA: 'rolerequestresource_rolerequest_hascriteria',
    BAR_TEXT: 'rolerequestresource_bar_text',
    AVAILABLE_STATES: 'rolerequestresource_availablestates',
    HAS_POTENTIAL_CONFLICTS: 'rolerequestresource_haspotentialconflicts'
};

export const ROLEMARKETPLACE_FIELDS = {
    ROLEREQUEST_GUID: 'rolemarketplace_rolerequest_guid',
    PUBLISHEDON: 'rolemarketplace_publishedon',
    CRITERIA_MATCH: 'rolemarketplace_criteriamatch',
    CATEGORY_GUID: 'rolemarketplace_rolecategory_guid',
    PUBLICATION_GUID: 'rolemarketplace_guid'
};

export const ROLEREQUESTRESOURCE_OVERRIDDEN_CHARGERATE_DESCRIPTION = 'uiOverriddenChargeRateDescription';

export const ROLEREQUESTRESOURCE_ASSIGNEE_GUID = 'rolerequestresource_assignee_guid';
export const ROLEREQUESTRESOURCE_STATUS_DESCRIPTION = 'rolerequestresourceStatusDescription';

export const ROLEREQUESTGROUP_FIELDS = {
    GUID: 'rolerequestgroup_guid',
    DESCRIPTION: 'rolerequestgroup_description',
    JOB_GUID: 'rolerequestgroup_job_guid',
    CREATEDON: 'rolerequestgroup_createdon',
    UPDATEDON: 'rolerequestgroup_updatedon',
    UPDATEDBY_RESOURCE_GUID: 'rolerequestgroup_updatedby_resource_guid',
    CREATEDBY_RESOURCE_GUID: 'rolerequestgroup_createdby_resource_guid',
    SURROGATE_ID: 'rolerequestgroup_surrogate_id',
    TOTALCOST: 'rolerequestgroup_totalcost',
    TOTALREVENUE: 'rolerequestgroup_totalrevenue',
    TOTALPROFIT: 'rolerequestgroup_totalprofit',
    TOTALROLES: 'rolerequestgroup_totalroles',
    TOTALACTIONABLEREQUESTS: 'rolerequestgroup_totalactionablerequests',
    ROLES_START: 'rolerequestgroup_roles_start',
    ROLES_END: 'rolerequestgroup_roles_end',
    INFO: 'rolerequestgroup_info'
};

export const JOB_BOOKINGS_CALC_FIELDS = {
    PLANNED_TOTAL_PROFIT: 'job_totalprofit',
    PLANNED_TOTAL_REVENUE: 'job_totalrevenue',
    PLANNED_TOTAL_COST: 'job_totalcost',
    PLANNED_FIXED_PRICE: 'job_fixedprice',
    PLANNED_BOOKINGS_START: 'job_planned_bookings_start',
    PLANNED_BOOKINGS_END: 'job_planned_bookings_end',
    UNCONFIRMED_TOTAL_COST: 'job_unconfirmed_totalcost',
    UNCONFIRMED_TOTAL_PROFIT: 'job_unconfirmed_totalprofit',
    UNCONFIRMED_TOTAL_REVENUE: 'job_unconfirmed_totalrevenue',
    UNCONFIRMED_BOOKINGS_START: 'job_unconfirmed_bookings_start',
    UNCONFIRMED_BOOKINGS_END: 'job_unconfirmed_bookings_end',
    HOURS_PERCENTAGE_BUDGET: 'job_hourspercentagebudget',
    TOTAL_HOURS_BOOKED: 'job_totalhoursbooked',
    JOB_REVENUE_AS_PERCENTAGE_OF_TARGET: 'job_revenuepercentagetarget'
};

export const JOB_UNGROUPED_ROLEREQUEST_CALC_FIELDS = {
    UNGROUPED_TOTAL_PROFIT: 'job_ungrouped_roles_totalprofit',
    UNGROUPED_TOTAL_REVENUE: 'job_ungrouped_roles_totalrevenue',
    UNGROUPED_TOTAL_COST: 'job_ungrouped_roles_totalcost',
    UNGROUPED_BOOKINGS_START: 'job_ungrouped_roles_start',
    UNGROUPED_BOOKINGS_END: 'job_ungrouped_roles_end',
    UNGROUPED_TOTAL_COUNT: 'job_ungrouped_total_count',
    UNGROUPED_TOTAL_ACTION_REQUESTS: 'job_ungrouped_totalactionablerequests'
};

export const CLIENT_FIELDS = {
    GUID: 'client_guid',
    DESCRIPTION: 'client_description'
};

export const JOB_BUDGET_CONSUMED = 'job_budgetconsumed';
export const JOB_BUDGET = 'job_budget';
export const JOB_OPPORTUNITY_PERCENT = 'job_opportunity_percent';

export const JOB_ROLEREQUESTGROUP_CALC_FIELD = {
    TOTAL_ROLES: 'job_totalroles',
    TOTAL_MY_ROLESGROUPS: 'job_totalmyrolegroups',
    ACTION_REQUEST: 'job_totalactionablerequests'
};

export const CHARGE_RATE_FIELDS = {
    GUID: 'chargerate_guid',
    DESCRIPTION: 'chargerate_description'
};

export const startDateFieldByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_START,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIELDS.START
};

export const endDateFieldByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_END,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIELDS.END
};

export const guidFieldByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_GUID,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIELDS.GUID,
    [TABLE_NAMES.ROLEREQUESTRESOURCE]: ROLEREQUESTRESOURCE_FIELDS.GUID
};

export const rolerequestGuidFieldByTableName = {
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIELDS.GUID,
    [TABLE_NAMES.ROLEREQUESTRESOURCE]: ROLEREQUESTRESOURCE_FIELDS.ROLEREQUEST_GUID
};

export const resourceGuidFieldByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_RESOURCE_GUID,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIELDS.RESOURCE_GUID,
    [TABLE_NAMES.ROLEREQUESTRESOURCE]: ROLEREQUESTRESOURCE_FIELDS.RESOURCE_GUID
};

export const jobGuidFieldByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_JOB_GUID,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIELDS.JOB_GUID
};

export const resourceGuidsFieldByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_RESOURCE_GUIDS,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_FIELDS.RESOURCE_GUIDS
};

export const chargeModeFieldByTableName = {
    [TABLE_NAMES.BOOKING]: BOOKING_CHARGEMODE,
    [TABLE_NAMES.ROLEREQUEST]: ROLEREQUEST_CHARGEMODE
};

export const ROLECRITERIA_FIELDS = {
    GUID: 'rolecriteria_guid',
    ROLEREQEST_GUID: 'rolecriteria_rolerequest_guid',
    VALUE: 'rolecriteria_value',
    VALUES: 'rolecriteria_values',
    FIELD: 'rolecriteria_field',
    ORDER: 'rolecriteria_order',
    TYPE: 'rolecriteria_type',
    SKILL_ID: 'rolecriteria_skillId',
    SKILL_VALUE: 'rolecriteria_skillValue',
    SKILL_TYPE_ID: 'rolecriteria_skillTypeId',
    SKILL_TYPE_NAME: 'rolecriteria_skillTypeName',
    SKILL_LEVELS: 'rolecriteria_skillLevels',
    OPERATOR: 'rolecriteria_operator',
    CREATEDON: 'rolecriteria_createdon',
    UPDATEDON: 'rolecriteria_updatedon',
    UPDATEDBY_RESOURCE_GUID: 'rolecriteria_updatedby_resource_guid',
    CREATEDBY_RESOURCE_GUID: 'rolecriteria_createdby_resource_guid'
};

export const SKILL_FIELDS = {
    SKILL_SKILLTYPE_GUID: 'skill_skilltype_guid'
};

export const RESOURCE_FIELDS = {
    GUID: FILTER_FIELD_NAMES.RESOURCE_GUID,
    DESCRIPTION: RESOURCE_DESCRIPTION,
    AVAILABILITY: FILTER_FIELD_NAMES.AVAILABILITY,
    GRADE: FILTER_FIELD_NAMES.RESOURCE_GRADE,
    DEPARTMENT: FILTER_FIELD_NAMES.RESOURCE_DEPARTMENT,
    AVAILABLE_TIME: FILTER_FIELD_NAMES.AVAILABLE_HOURS,
    JOB_TITLE: FILTER_FIELD_NAMES.RESOURCE_JOBTITLE,
    USER_STATUS: RESOURCE_USERSTATUS,
    SKILL: FILTER_FIELD_NAMES.SKILL,
    SKILL_EXPIRY_STATUS: SKILL_EXPIRY_STATUS
};

export const RESOURCE_CALC_FIELDS = {
    SKILLS: { LAST_UPDATED: FILTER_FIELD_NAMES.RESOURCE_SKILL_LAST_UPDATED },
    EDUCATION: { LAST_UPDATED: FILTER_FIELD_NAMES.RESOURCE_EDUCATION_LAST_UPDATED },
    EXPERIENCE: { LAST_UPDATED: FILTER_FIELD_NAMES.RESOURCE_EXPERIENCE_LAST_UPDATED }
};

export const RESOURCE_FTE_INPUT_FIELD = {
    MIN_VALUE: 0.01,
    STEP: 0.01,
    DECIMAL_PLACES: 2
};

export const CRITERIA_ROLE_FTE_INPUT_FIELD = {
    MIN_VALUE: 0.01,
    STEP: 0.01,
    MAX_VALUE: 20,
    DEFAULT_VALUE: 1
};

export const PROFIT_PER_HOUR_FIELDS = [
    BOOKING_PROFITPERHOUR_FIELDNAME,
    ROLEREQUEST_PROFITPERHOUR_FIELDNAME,
    ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME
];

export const ESTIMATE_PROFIT_PER_HOUR_FIELDS = [ROLEREQUEST_ESTIMATEPROFITPERHOUR_FIELDNAME];

export const ROLE_BUDGET_FIELDS = [
    ROLEREQUEST_FIELDS.COST_PER_HOUR,
    ROLEREQUEST_FIELDS.REVENUE_PER_HOUR,
    ROLEREQUEST_FIELDS.TOTALCOST,
    ROLEREQUEST_FIELDS.TOTALPROFIT,
    ROLEREQUEST_FIELDS.TOTALREVENUE,
    ROLEREQUEST_FIELDS.CHARGEMODE,
    ROLEREQUEST_FIELDS.OVERRIDDEN_CHARGE_RATE_GUID
];

export const PROGRESS_BAR_FILLED_PERCENTAGE_FIELD = {
    DEFAULT_VALUE: 0
};

export const HOURS_PER_DAY_FALLBACK_VALUE = 0;

export const ASSIGNEE_BUDGET_CHANGE_DEPENDANT_FIELDS = [ROLEREQUESTRESOURCE_PROFITPERHOUR_FIELDNAME];

export const EDUCATION_FIELDS = {
    EDUCATION_GUID: `${TABLE_NAMES.EDUCATION}_guid`,
    EDUCATION_INSTITUTION: `${TABLE_NAMES.EDUCATION}_${TABLE_NAMES.INSTITUTION}_guid`,
    EDUCATION_FIELD: `${TABLE_NAMES.EDUCATION}_${TABLE_NAMES.EDUCATION_FIELD}_guid`,
    EDUCATION_DEGREE: `${TABLE_NAMES.EDUCATION}_${TABLE_NAMES.DEGREE}_guid`,
    EDUCATION_START: `${TABLE_NAMES.EDUCATION}_start`,
    EDUCATION_END: `${TABLE_NAMES.EDUCATION}_end`,
    EDUCATION_DETAILS: `${TABLE_NAMES.EDUCATION}_details`,
    EDUCATION_RESOURCE_GUID: `${TABLE_NAMES.EDUCATION}_${TABLE_NAMES.RESOURCE}_guid`
};

export const EXPERIENCE_FIELDS = {
    EXPERIENCE_END: `${TABLE_NAMES.EXPERIENCE}_end`,
    EXPERIENCE_ROLE: `${TABLE_NAMES.EXPERIENCE}_role`,
    EXPERIENCE_GUID: `${TABLE_NAMES.EXPERIENCE}_guid`,
    EXPERIENCE_START: `${TABLE_NAMES.EXPERIENCE}_start`,
    EXPERIENCE_DETAILS: `${TABLE_NAMES.EXPERIENCE}_details`,
    EXPERIENCE_COMPANY: `${TABLE_NAMES.EXPERIENCE}_companyname`,
    EXPERIENCE_LOCATION: `${TABLE_NAMES.EXPERIENCE}_location`,
    EXPERIENCE_RESOURCE_GUID: `${TABLE_NAMES.EXPERIENCE}_${TABLE_NAMES.RESOURCE}_guid`
};

export const FIELD_LOOKUP_VALUE = 'fieldlookupvalue_value';

export const SECURITY_PROFILE_FIELDS = {
    GUID: 'securityprofile_guid',
    DESCRIPTION: 'securityprofile_description'
};

export const FIELDFORMAT_FIELDS = {
    GUID: 'fieldformat_guid',
    NAME: 'fieldformat_name',
    DISPLAYNAME: 'fieldformat_displayName'
};

export const DIARYGROUP_FIELDS = {
    GUID: 'diarygroup_guid',
    DESCRIPTION: 'diarygroup_description'
};

export const calcFields = [
    FILTER_FIELD_NAMES.AVAILABILITY,
    FILTER_FIELD_NAMES.AVAILABLE_HOURS,
    FILTER_FIELD_NAMES.UTILISATION
];

export const budgetHoursRevenueFields = new Set([
    FILTER_FIELD_NAMES.JOB_REVENUE_TARGET,
    FILTER_FIELD_NAMES.JOB_REVENUE_AS_BOOKED,
    FILTER_FIELD_NAMES.JOB_REVENUE_AS_PERCENTAGE_OF_TARGET,
    FILTER_FIELD_NAMES.JOB_COSTS_BUDGET,
    FILTER_FIELD_NAMES.JOB_COSTS_AS_BOOKED,
    FILTER_FIELD_NAMES.JOB_COSTS_AS_PERCENTAGE_OF_BUDGET,
    FILTER_FIELD_NAMES.JOB_PROFIT_MARGIN_TARGET,
    FILTER_FIELD_NAMES.JOB_PROFIT_MARGIN_AS_BOOKED,
    FILTER_FIELD_NAMES.JOB_MARGIN_AS_PERCENTAGE_OF_TARGET,
    FILTER_FIELD_NAMES.JOB_HOURS_BUDGET,
    FILTER_FIELD_NAMES.JOB_TOTAL_HOURS_BOOKED,
    FILTER_FIELD_NAMES.JOB_HOURS_AS_PERCENTAGE_OF_BUDGET
]);

export const WORKSPACE_VIEW_TYPE_FIELDS = {
    GUID: 'wsviewtype_guid',
    DESCRIPTION: 'wsviewtype_description',
};
