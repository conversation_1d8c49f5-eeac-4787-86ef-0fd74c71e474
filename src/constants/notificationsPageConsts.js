import { DATA_GRID_TABLE_DATAS_SUFFIX } from '../constants/dataGridConsts';

export const NOTIFICATIONS_PAGE_ALIAS = 'notificationsPage';

export const NOTIFICATION_SETTINGS_USER_PAGE = 'notificationsSettingsUserPage';

export const NOTIFICATIONS_PAGE_TABLE_DATA_ALIAS = `${NOTIFICATIONS_PAGE_ALIAS}_${DATA_GRID_TABLE_DATAS_SUFFIX}`;

export const NOTIFICATION_HISTORY = 'History';

export const NOTIFICATION_SETTINGS = 'Settings';

export const NOTIFICATIONS_BOOKING_ALIAS = 'notificationBookingCollection';

export const DEFAULT_NOTIFICATION_PAGESIZE = 10;

export const NOTIFICATION_TABS = [
    { tabName: NOTIFICATION_HISTORY }
    //{tabName: NOTIFICATION_SETTINGS} // Will be added in future
];

export const NOTIFICATION_EVENTS = {
    BOOKING_ASSIGN_TYPE: 'BookingAssign',
    BOOKING_UPDATE_TYPE: 'BookingUpdate',
    BOOKING_DELETE_TYPE: 'BookingDelete',
    ROLE_REQUEST_CREATE_TYPE: 'RoleRequestCreate',
    ROLE_REQUEST_REJECT_TYPE: 'RoleRequestReject',
    ROLE_REQUEST_LIVE_TYPE: 'RoleRequestLive',
    BOOKING_NOTIFICATION:'BookingNotification',
    ROLE_NOTIFICATION: 'RoleNotification',
    BOOKING_POST: 'BookingPost',
    BOOKING_PATCH: 'BookingPatch',
    BOOKING_DELETE: 'BookingDelete',
    EMAIL_FREQUENCY: 'emailFreq',
    GLOBAL_NOTIFICATION_SETTINGS: 'globalNotificationSettings',
    RESOURCE_SKILL_NOTIFICATION:'ResourceSkillNotification',
    RESOURCE_SKILL_EXPIRY: 'ResourceSkillExpiry',
    RESOURCE_MANAGER_SKILL_EXPIRY:'ResourceManagerSkillExpiry',
    RESOURCE_SKILL_RECOMMENDATION:'ResourceSkillRecommendation'
};

export const NOTIFICATIONS_HISTORY_ACTIONS = {
    CREATE_BOOKING: 'create-booking',
    MARK_AS_READ: 'mark-as-read',
    MARK_AS_UNREAD: 'mark-as-unread',
    DELETE_NOTIFICATION: 'delete-notification'
};

export const NOTIFICATION_TIME_ALLOCATION_TYPES = [
    { name: 'loading', suffix: 'loadingSectionSuffix', key: 0 },
    { name: 'time', suffix: 'timeSectionSuffix', key: 1 },
    { name: 'hoursPerDay', suffix: 'hoursPerDaySuffix', key: 2 },
    { name: 'resourceCapacity', suffix: 'loadingSectionSuffix', key: 3 },
    { name: 'resourceHours', suffix: 'timeSectionSuffix', key: 4 }
];

export const DUMMY_GUID = '00000000-0000-0000-0000-000000000000';

export const NOTIFICATION_TABS_KEY = 'notificationTabs';
export const NEW_NOTIFICATION_TABS_KEY = 'newNotificationTabs';

export const NOTIFICATION_SETTINGS_APPLICATION_TYPE = {
    WEB: 'web',
    EMAIL: 'email'
};

export const NOTIFICATION_SETTINGS_EMAIL_FREQUENCY = 'emailFreq_emailFreq';

export const GLOBAL_NOTIFICATION_SETTINGS_ADMIN = 'globalNotificationSettingsAdmin';

export const UNCONFIRMED_BOOKINGS_SETTINGS_ADMIN = 'unconfirmedBookingsSettingsAdmin';

export const NOTIFICATION_REQUEST_KEYS = ['ResourceSkillApproval']; // Add notificationPreferenceKey here for the notification to appear in request tab