import React, { useCallback, useEffect, useState, useMemo } from 'react';
import { Select, Tooltip } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import PropTypes from 'prop-types';
import { debounce } from 'lodash';
import styles from './configurableSelect.less';
import { fetchOptions } from '../../../actions/configurableSelectActions';

/** @typedef {import('../../../types/transformedOptions.jsdoc').TransformedOption} TransformedOption */
/** @typedef {import('../../../types/configurableSelectSourceState.jsdoc').ConfigurableSelectSourceState} ConfigurableSelectSourceState */

/**
 * ConfigurableSelect component that wraps Ant Design's Select
 * Supports both typeahead (API-based) and local filtering modes
 *
 * @component
 * @param {Object} props - Component props
 * @param {string} props.sourceId - Unique identifier for the data source
 * @param {boolean} [props.isTypeahead=false] - Enable/disable typeahead mode
 * @param {(string|number|Array)} [props.initialValue] - Initial selected value(s)
 * @param {string} [props.placeholder='Select an option'] - Placeholder text
 * @param {Object} [props.style={ width: 200 }] - Custom styles
 * @param {Function} [props.onChange] - Callback when selection changes
 * @param {Function} [props.optionRenderer] - Custom option renderer
 * @param {Function} [props.setTooltipOpenStatus] - Controls tooltip visibility
 * @param {boolean} [props.tooltipOpenStatus] - Current tooltip visibility state
 * @param {string} [props.className] - Additional CSS classes
 * @param {boolean} [props.disabled=false] - Disabled state
 * @param {boolean} [props.allowClear=false] - Show clear button
 * @param {('large'|'middle'|'small')} [props.size='middle'] - Component size
 * @param {('multiple'|'tags')} [props.mode] - Selection mode
 * @param {number} [props.maxTagCount] - Maximum visible tags
 * @param {boolean} [props.loading] - External loading state
 * @param {Function} [props.onDropdownVisibleChange] - Dropdown visibility change handler
 * @param {React.ReactNode} [props.tooltipTitle] - Tooltip content
 * @param {string} [props.methodType] Defines the type of method 'GET' or 'POST'
 * @param {object} [props.payload] Defines the type payload of the request
 * @returns {React.ReactElement} Rendered component
 */
const ConfigurableSelect = ({
    sourceId,
    isTypeahead = false,
    initialValue,
    placeholder = 'Select an option',
    style = { width: 200 },
    onChange,
    optionRenderer,
    setTooltipOpenStatus,
    tooltipOpenStatus,
    className,
    disabled = false,
    allowClear = false,
    size = 'middle',
    mode,
    maxTagCount,
    loading: externalLoading,
    onDropdownVisibleChange,
    tooltipTitle,
    methodType,
    payload
}) => {
    const dispatch = useDispatch();
    const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
    const [localSelectedValue, setLocalSelectedValue] = useState(initialValue || (mode === 'multiple' ? [] : undefined));

    /**
     * Select state from Redux store
     * @type {ConfigurableSelectSourceState}
     */
    const sourceState = useSelector(state => state.configurableSelect.sources[sourceId] || {});

    const {
        options = [],
        loading: internalLoading,
        error
    } = sourceState;

    /** Combined loading state from internal and external sources */
    const loading = externalLoading || internalLoading;

    /**
     * Initialize component with data and initial value
     * @type {React.EffectCallback}
     */
    useEffect(() => {
        if (!isTypeahead) {
            dispatch(fetchOptions('', sourceId, isTypeahead, methodType, payload));
        }
    }, [dispatch, sourceId, isTypeahead]);

    /**
     * Update local selected value if initialValue changes
     * @type {React.EffectCallback}
     */
    useEffect(() => {
        setLocalSelectedValue(initialValue);
    }, [initialValue]);

    /**
     * Debounced fetch for typeahead mode
     * @type {React.EffectCallback}
     */
    const debouncedFetchOptions = useCallback(
        debounce((searchTerm) => {
            if (searchTerm !== debouncedSearchTerm) {
                dispatch(fetchOptions(searchTerm, sourceId, isTypeahead, methodType, payload));
                setDebouncedSearchTerm(searchTerm);
            }
        }, 300),
        [dispatch, sourceId, isTypeahead, debouncedSearchTerm]
    );

    /**
     * Handles search input changes
     * @param {string} searchTerm - Current search input value
     */
    const handleSearch = (searchTerm) => {
        if (isTypeahead) {
            debouncedFetchOptions(searchTerm);
        }
    };

    /**
     * Handles selection changes
     * @param {(string|number|Array)} value - New selected value(s)
     */
    const handleChange = (value) => {
        setLocalSelectedValue(value); // Update local state
        if (onChange) {
            onChange(sourceId, value); // Notify parent component
        }
    };

    /**
     * Handles dropdown visibility changes
     * @param {boolean} open - Whether dropdown is visible
     */
    const handleDropdownVisibleChange = (open) => {
        if (setTooltipOpenStatus) {
            setTooltipOpenStatus(!open);
        }
        if (onDropdownVisibleChange) {
            onDropdownVisibleChange(open);
        }
    };

    /**
     * Renders option with custom renderer if provided
     * @param {TransformedOption} option - Option to render
     * @returns {TransformedOption} Processed option
     */
    const renderOption = (option) => {
        if (optionRenderer && option.originalItem) {
            return optionRenderer(option.originalItem);
        }

        return option;
    };

    /**
     * Memoize options to avoid unnecessary re-renders
     * @type {TransformedOption[]}
     */
    const memoizedOptions = useMemo(() => {
        return options.map(renderOption);
    }, [options, optionRenderer]);

    /**
     * Main Select component with all configurations
     * @type {React.ReactElement}
     */
    const selectComponent = (
        <Select
            value={localSelectedValue}
            placeholder={placeholder}
            style={style}
            onChange={handleChange}
            onDropdownVisibleChange={handleDropdownVisibleChange}
            loading={loading}
            options={memoizedOptions}
            className={`${styles.configurableSelect} ${className}`}
            disabled={disabled}
            allowClear={allowClear}
            size={size}
            mode={mode}
            maxTagCount={maxTagCount}
            showSearch
            optionFilterProp="label"
            onSearch={handleSearch}
            filterOption={!isTypeahead}
            tokenSeparators={[]}
            aria-label={placeholder}
        />
    );

    return tooltipTitle ? (
        <Tooltip title={tooltipTitle} open={tooltipOpenStatus} placement="top">
            {selectComponent}
        </Tooltip>
    ) : selectComponent;
};

/**
 * PropTypes for type checking and documentation
 */
ConfigurableSelect.propTypes = {
    /** Required unique identifier */
    sourceId: PropTypes.string.isRequired,
    /** Whether to use typeahead mode */
    isTypeahead: PropTypes.bool,
    /** Initial selection */
    initialValue: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number,
        PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.string, PropTypes.number]))
    ]),
    /** Placeholder text */
    placeholder: PropTypes.string,
    /** Custom styles */
    style: PropTypes.object,
    /** Selection change handler */
    onChange: PropTypes.func,
    /** Custom option renderer */
    optionRenderer: PropTypes.func,
    /** CSS class */
    className: PropTypes.string,
    /** Disabled state */
    disabled: PropTypes.bool,
    /** Show clear button */
    allowClear: PropTypes.bool,
    /** Component size */
    size: PropTypes.oneOf(['large', 'middle', 'small']),
    /** Selection mode */
    mode: PropTypes.oneOf(['multiple', 'tags', undefined]),
    /** Max visible tags */
    maxTagCount: PropTypes.number,
    /** Loading state */
    loading: PropTypes.bool,
    /** Dropdown visibility handler */
    onDropdownVisibleChange: PropTypes.func,
    /** Tooltip visibility controller */
    setTooltipOpenStatus: PropTypes.func,
    /** Tooltip visibility state */
    tooltipOpenStatus: PropTypes.bool,
    /** Tooltip content */
    tooltipTitle: PropTypes.node,
    /** Defines the type of request 'GET' or 'POST' */
    methodType: PropTypes.string,
    /** Defines the payload */
    payload: PropTypes.object
};

/**
 * Default props
 */
ConfigurableSelect.defaultProps = {
    isTypeahead: false,
    placeholder: 'Select an option',
    style: {},
    disabled: false,
    allowClear: false,
    mode: undefined,
    size: 'middle',
    methodType: 'GET',
    payload: {}
};

export default ConfigurableSelect;