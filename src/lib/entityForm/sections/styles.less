:local(.controlContextualMenuButton) {
    float: left;
    color: @primary-color;
    border-color: @primary-color;
}

:local(.dropDownStyle) {
    position: absolute;
    right: 0;

    &.__drop-down-open {
        background-color: transparent;
    }

    .ant-dropdown-menu-submenu .ant-dropdown-menu-sub {
        width: 200px;

        .ant-menu-submenu-title {
            text-overflow: ellipsis;
            white-space: unset;
        }

        ul.skills-filter-dropdown div span.dropdown-menu-submenu-arrow {
            right: 0;
        }
    }
}

:local(.searchIcon){
    .anticon-search {
        transform: rotate(180deg);
    }
    .ant-select-selection-search-input:focus-visible {
        outline: none !important;
    }
}

:local(.tabSectionContainer) {
    .ant-tabs-nav {
        &::before {
            border-bottom: none;
        }

        .ant-tabs-tab {
            transition: none;
        }

        .ant-tabs-tab-active {
            background: @primary-color !important;
        
            .ant-tabs-tab-btn {
                color: @white-color !important;
            }
        }

        .ant-tabs-tab, .ant-tabs-tab-active {
            border: none !important;

            &:hover {
                color: inherit;
            }

            &:not(.ant-tabs-tab-active) {
                background: @grey-2;
                color: @light-text-color;
            }
        }

        .ant-tabs-tab:first-of-type {
            border-right: none !important;
            border-top-left-radius: 6px;
            border-bottom-left-radius: 6px;
        }

        .ant-tabs-tab:last-of-type {
            border-top-right-radius: 6px;
            border-bottom-right-radius: 6px;
        }
    }
}

:local(.tooltipCard) {
    .ant-tooltip-inner {
        background: @white-color;
        color: @black-color;
        border-radius: 7px;
    }

    .ant-tooltip-arrow::before {
        color: @white-color;
        background: @white-color;
    }
}