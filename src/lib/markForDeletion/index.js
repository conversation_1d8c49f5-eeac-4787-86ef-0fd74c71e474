import React from 'react';
import { Icon } from '../';
import { string, bool, func, array } from 'prop-types';
import { Tag } from 'antd';
import './styles.css';
import SkillPreference from '../../components/skillPreference/skillPreference';

export const MarkForDeletion = ({
    text,
    markDeleted,
    markDeletedMessage,
    cancelActionMessage,
    onAction,
    onCancelAction,
    deleteLabel,
    cancelDeletionAriaLabel,
    skillPreferences,
    resourceSkillPreference,
    onSkillPreferenceChange,
    getIsSkillPending,
    entityId,
    skillId
}) => {

    const skillPreferenceProps = {
        skillPreferences,
        resourceSkillPreference,
        onSkillPreferenceChange
    };

    const isRequested = getIsSkillPending(entityId, skillId);

    return (
        <div className="mark-for-deletion">
            <div className={`mark-for-deletion-text ${markDeleted ? 'marked' : ''}`}>

                <span role="heading" aria-level="3" className={`${markDeleted ? 'line-through-text' : ''} popupTitle skillName`} >
                    {text}
                </span>

                {isRequested && (
                    <Tag className="requested-tag">{'Requested'}</Tag>
                )}

                {!markDeleted &&
                    <SkillPreference {...skillPreferenceProps}></SkillPreference>}

                {!markDeleted &&
                    <Icon
                        type="delete"
                        className="button-delete"
                        onClick={onAction}
                        role="button"
                        ariaHidden={false}
                        ariaLabel={deleteLabel}
                    />
                }
            </div>
            {
                markDeleted &&
                <div className="message-container">
                    <Icon type="info-circle" />
                    <span className="message">
                        {markDeletedMessage}
                    </span>
                    <a aria-label={cancelDeletionAriaLabel} onClick={onCancelAction}>
                        {cancelActionMessage}
                    </a>
                </div>
            }
        </div >
    );
};

MarkForDeletion.propTypes = {
    text: string.isRequired,
    markDeleted: bool.isRequired,
    markDeletedMessage: string,
    cancelActionMessage: string,
    onAction: func.isRequired,
    onCancelAction: func.isRequired,
    skillPreferences: array.isRequired,
    resourceSkillPreference: string.isRequired,
    onSkillPreferenceChange: func.isRequired,
    getIsSkillPending: func.isRequired,
    skillId: string.isRequired,
    entityId: string.isRequired
};
