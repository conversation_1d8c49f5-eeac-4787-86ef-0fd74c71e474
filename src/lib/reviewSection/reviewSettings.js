import React, { Component, createRef } from 'react';
import { Modal as AntdModal, Button, Form, Radio } from 'antd';
import { SettingOutlined } from '@ant-design/icons';
import Icon from '../icon';
import './reviewSection.less';
import { REVIEW_SETTINGS_FORM_VALUES } from '../../constants/entityWindowConsts';
import PropTypes from 'prop-types';
import ConfigurableSelect from '../../components/common-components/configurableSelect/configurableSelect';

class ReviewSettings extends Component {
    constructor(props) {
        super(props);
        this.state = {
            isModalOpen: false,
            payload: null
        };
        this.formRef = createRef();
    }

    /** The function is called on click of setting icon */
    handleSettingsClick = () => {
        this.setState({ isModalOpen: true });
    };

    /** The method is called on click of save changes */
    handleOk = () => {
        // handle save logic here
        this.setState({ isModalOpen: false });
    };

    /** The method is called on click of cancel button */
    handleCancel = () => {
        this.setState({ isModalOpen: false });
    };

    /**
     * Get the resources on whenever the review settings modal is opened
     * @param {Object} prevProps previous props before update
     * @param {Object} prevState Represent the previous state before update
     */
    componentDidUpdate(prevProps, prevState) {
        if (!prevState.isModalOpen && this.state.isModalOpen) {
            this.setState({
                payload: this.props.loadResource()
            });
        }
    }

    /** Method to handle the dropdown value change */
    handleReviewerChange() {
        // Logic as per the requirement
        return;
    }

    render() {
        const { isModalOpen } = this.state;

        const {
            headerProps,
            footerProps,
            bodyProps
        } = this.props;

        const { title } = headerProps;
        const { saveChangesButtonLabel, cancelBtnLabel } = footerProps;
        const { resourceSkillsReviewLabel, reviewerOnThisJobLabel, eligibleForReviewLabel, reviewerOnThisJobCaptionLabel, pastBookingLabel, allBookedLabel, clearSkillsReviewsLabel } = bodyProps;

        return (
            <>
                <SettingOutlined
                    className="job-review-section-content__settings"
                    tabIndex={0}
                    aria-label="settings"
                    role="button"
                    onClick={this.handleSettingsClick}
                />
                <AntdModal
                    open={isModalOpen}
                    title={title}
                    onOk={this.handleOk}
                    onCancel={this.handleCancel}
                    okText={saveChangesButtonLabel}
                    cancelText={cancelBtnLabel}
                    cancelButtonProps={{ className: 'ant-btn-tertiary' }}
                >
                    <Form
                        ref={this.formRef}
                        initialValues={{
                            [REVIEW_SETTINGS_FORM_VALUES.REVIEW_OPTIONS]: REVIEW_SETTINGS_FORM_VALUES.PAST_BOOKINGS
                        }}>
                        <Form.Item
                            label={<strong>{resourceSkillsReviewLabel}</strong>}
                            name={REVIEW_SETTINGS_FORM_VALUES.DELETE_REVIEWS}
                        >
                            <Button className="delete-reviews clear-skills-review-button" >
                                <Icon type="delete" />{clearSkillsReviewsLabel}
                            </Button>
                        </Form.Item>

                        {/* Should display all the users (needs implementation) */}
                        <Form.Item
                            label={reviewerOnThisJobLabel}
                            name={REVIEW_SETTINGS_FORM_VALUES.REVIEWER}
                        >
                            {
                                this.state.payload &&
                                <ConfigurableSelect
                                    sourceId="resource"
                                    isTypeahead={false}
                                    placeholder="Select reviewer"
                                    initialValue={[]}
                                    style={{ paddingLeft: '100px' }}
                                    onChange={this.handleReviewerChange}
                                    methodType="POST"
                                    payload={this.state.payload}
                                />
                            }

                            <div className="reviewer-dropdown">{reviewerOnThisJobCaptionLabel}</div>
                        </Form.Item>

                        <Form.Item
                            label={eligibleForReviewLabel}
                            name={REVIEW_SETTINGS_FORM_VALUES.REVIEW_OPTIONS}
                        >
                            <Radio.Group className="review-settings-radio-button reviews-radio-button">
                                <Radio value={REVIEW_SETTINGS_FORM_VALUES.PAST_BOOKINGS}>
                                    {pastBookingLabel}
                                </Radio>
                                <Radio value={REVIEW_SETTINGS_FORM_VALUES.ALL_BOOKINGS}>
                                    {allBookedLabel}
                                </Radio>
                            </Radio.Group>
                        </Form.Item>
                    </Form>
                </AntdModal>
            </>
        );
    }
}

export default ReviewSettings;

ReviewSettings.propTypes = {
    headerProps: PropTypes.object.isRequired,
    footerProps: PropTypes.object.isRequired,
    bodyProps: PropTypes.object.isRequired,
    loadResource: PropTypes.func
};