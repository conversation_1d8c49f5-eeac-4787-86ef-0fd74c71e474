.approval-requests-tab {
  height: calc(70vh - 85px);
  overflow: auto;
}

.nested-approval-tabs {
  .ant-tabs-nav.ant-tabs-nav-wrap {
    padding: 8px;
  }
  .ant-tabs-tab {
    background-color: @grey-2;
    color: @secondary-color;
    border: none;
    padding: 4px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  /* Active tab */
  .ant-tabs-tab.ant-tabs-tab-active {
    background-color: @primary-color;
    border-radius: 8px;
    .ant-tabs-tab-btn {
      color: @white-color !important;
    }
  }
  .ant-tabs-ink-bar {
    display: none;
  }
}

.skill-approval-component {
  .approval-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;

    .reject-all-button {
      margin-right: 8px;
    }
  }

  .skill-group-section {
    //margin-bottom: 16px;

    &:last-child .ant-divider {
      display: none; // Hide the last divider
    }
    .ant-row {
      margin-bottom: 8px;

      .ant-col:last-child {
        text-align: left;
      }
    }
  }

  .pending-skills-list {
    .skill-approval-card {
      margin-bottom: 8px;
      border-radius: 8px; // Rounded borders
      overflow: hidden;
      background-color: transparent; // Ensure card itself doesn't interfere
      border: 1px solid #d9d9d9; // Debugging border

      .ant-card-body {
        padding: 0;
      }

      .skill-name-row {
        padding: 12px 16px;
        background-color: @smoke-white; // Grey background
        display: flex;
        align-items: center;
        border-bottom: 1px solid #e8e8e8; // Subtle separation

        .skill-name-col {
          flex: 1;
          min-width: 0; // Crucial for ellipsis with flexbox
        }

        .skill-name-container {
          max-width: 400px; // Adjust as needed
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          .ant-typography {
            margin: 0;
          }
        }

        .skill-actions-col {
          flex-shrink: 0;
          margin-left: 16px;
        }
      }

      .level-row {
        padding: 12px 16px;
        background-color: @white-color; // White background
        border-top: 1px solid #e8e8e8; // Subtle separation

        .level-dropdown {
          width: 150px;
        }
      }
    }
  }

  .requested-tag {
    background-color: @requested-fill;
    border: 2px solid @requested-fill;
    color: @white-color;
  }
}

.skill-approval-history-message-label {
  padding: 14px;
}

.skill-history-timeline {
  height: calc(48vh - 70px);
  overflow: auto;
}

.skill-approval-history {
  display: flex;
}

.skill-history-check-filled-circle {
  color: @primary-color;
  margin-left: 10px;
  margin-right: 10px;
}

.skill-history-cross-filled-circle {
  color: red;
  margin-left: 10px !important;
  margin-right: 10px !important;
}

.skill-history-show-more-button {
  padding-left: 20px;
}

.skill-approval-history-button-sort {
  margin-left: 14px;
}

.skill-history-timeline-content {
  margin-right: 120px;
  padding: 10px;
}