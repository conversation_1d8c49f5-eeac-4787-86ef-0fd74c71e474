import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Timeline, Button } from 'antd';
import { formatLocalDate, getDisplayShortDateFormat } from '../../../utils/dateUtils';
import { SKILLS_APPROVAL_AUDIT_ACTIONS } from '../../../constants/auditTrailConsts';
import ShowMoreLessControl from '../../showMoreLessControl/showMoreLessControl';
import { DATE_FORMATS } from '../../../constants';
import { DISPLAY_DATE_TIME_FORMATS } from '../../../constants/globalConsts';
import { SORT_ASCENDING, SORT_DESCENDING } from '../../../constants';
import { IconSort } from '../../icons/iconSort';
import SkillApprovalTimelineDetails from './skillApprovalTimelineDetails';

/**
 * The component is used to display the history timeline.
 * @param {{ resourceId: string; approvalRequestsData: Object; loadSkillsApprovalsHistory: function; }}
 * @param {string} param0.resourceId Id of the current user.
 * @param {Object} param0.approvalRequestsData Request data object
 * @param {function} param0.loadSkillsApprovalsHistory Function to load kill approval history data.
 * @returns History timeline
 */
const SkillsApprovalHistoryTimeline = ({
    resourceId,
    approvalRequestsData,
    loadSkillsApprovalsHistory
}) => {
    // State to set the order of the timeline
    const [sortOrder, setSortOrder] = useState(SORT_DESCENDING);

    const {
        history,
        staticLabels,
        config: {
            constants: { defaultLogsCount }
        }
    } = approvalRequestsData;

    // Check if the count is more than zero or not to display show more.
    const isShowMoreVisible = history.count > 0;

    /** Dispatch function called on click of show more */
    const handleShowMore = () => {
        loadSkillsApprovalsHistory(resourceId, sortOrder, defaultLogsCount, history.pageNumber);
    };

    /** Dispatch function to handle the sorting */
    const handleSortToggle = () => {
        const newOrder = sortOrder === SORT_DESCENDING ? SORT_ASCENDING : SORT_DESCENDING;
        setSortOrder(newOrder);
        loadSkillsApprovalsHistory(resourceId, newOrder, defaultLogsCount, 1);
    };

    // Builds the timeline object which includes label,color and children
    const items = history.data.map(entry => ({
        key: entry.skill_guid,
        color: SKILLS_APPROVAL_AUDIT_ACTIONS[entry.type]?.color,
        label: (
            <div>
                {formatLocalDate(
                    entry.actiondateTime,
                    getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR)
                )}
                <br />
                {formatLocalDate(entry.actiondateTime, DATE_FORMATS.WEEK_DAY)}
            </div>
        ),
        children: <SkillApprovalTimelineDetails history={entry} />
    }));

    return (
        <>
            <Button
                className="skill-approval-history-button-sort"
                onClick={handleSortToggle}
            >
                <IconSort sortOrder={sortOrder} />
                <span className="label">{staticLabels?.sortLabel}</span>
            </Button>

            <div className="skill-history-timeline">
                <div className="skill-history-timeline-content">
                    <Timeline mode="left" items={items} />
                </div>
            </div>

            <ShowMoreLessControl
                className="show-more-button skill-history-show-more-button"
                allRecordsShown={false}
                visible={isShowMoreVisible}
                showMoreText={staticLabels.showMoreText}
                onClick={handleShowMore}
            />
        </>
    );
};

SkillsApprovalHistoryTimeline.propTypes = {
    resourceId: PropTypes.string.isRequired,
    approvalRequestsData: PropTypes.object.isRequired,
    loadSkillsApprovalsHistory: PropTypes.func.isRequired
};


export default SkillsApprovalHistoryTimeline;
