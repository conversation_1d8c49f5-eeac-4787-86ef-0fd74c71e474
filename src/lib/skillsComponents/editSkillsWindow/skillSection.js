import React, { Component } from 'react';
import { object, string, func, array, bool } from 'prop-types';
import { Card } from 'antd';
import { MarkForDeletion } from '../../markForDeletion';
import { SkillFieldsFormCreated, SkillFieldsForm } from '../skillFieldsForm';
import './styles.css';
import Icon from '../../icon';
import { SKILL_PREFERENCE } from '../../../constants/editSkillsWindowConsts';
import { RecommendationCardTitle } from '../recommendationsWindow/recommendationCardTitle';
export class SkillsSection extends Component {
    constructor(props) {
        super(props);

        this.onToggleExpandCollapse = this.onToggleExpandCollapse.bind(this);

        this.state = {
            collapsed: false
        };
    }

    onToggleExpandCollapse() {
        this.setState(prevState => {
            return {
                ...prevState,
                collapsed: !prevState.collapsed
            };
        });
    }

    render() {
        const {
            section: {
                sectionName,
                skills
            },
            config,
            onRemoveSkill,
            onRemoveSkillCancel,
            mapPropsToFields,
            getFieldsConfig,
            getUISkill,
            getSkillFieldInfo,
            onSkillFieldChange,
            onSkillsFieldsChange,
            form,
            entityId,
            renderFieldsOnly,
            getSkillInfo,
            staticMessages,
            getAriaLabelDeleteSkillButton,
            hiddenFieldIds = [],
            skillPreferences,
            getResourceSkillPreference,
            getIsSkillPending
        } = this.props;

        const { collapsed } = this.state;

        const skillContainerClassName = `skills-container ${collapsed && renderFieldsOnly ? 'hidden-skills-container' : ''}`;

        let renderSkills = true;
        if (!renderFieldsOnly) {
            renderSkills = !collapsed;
        }

        return (
            <div className="section-container">
                <div className="section-header" onClick={this.onToggleExpandCollapse}>
                    <Icon type="up" className={`icon-collapse ${collapsed ? 'collapsed' : ''}`} />
                    {sectionName}
                </div>
                <div className={skillContainerClassName}>
                    {
                        renderSkills && skills
                            .map(({ id, name, removed }, skillIndex) => {
                                const skillInfo = getSkillInfo(id);
                                skillInfo.fields = [...skillInfo.fields.filter(x => !hiddenFieldIds.includes(x))];
                                const deleteLabels = getAriaLabelDeleteSkillButton(name);
                                const resourceSkillPreference = getResourceSkillPreference(entityId, id);
                                const formProps = {
                                    renderFieldsOnly,
                                    entityId,
                                    getSkillFieldInfo,
                                    skillId: id,
                                    uiSkill: getUISkill(entityId, id) || {},
                                    skillName: name,
                                    // We are not filtering hidden fields here because it is taken care at lower level
                                    fieldsConfig: getFieldsConfig(id),
                                    skillInfo,
                                    onSkillFieldChange,
                                    staticMessages,
                                    hiddenFieldIds,
                                    popupControlsInBody: true
                                };

                                const onPreferenceChange = (value) => {
                                    onSkillFieldChange(entityId, id, { id: SKILL_PREFERENCE }, value, skillInfo);
                                };

                                return (
                                    <Skill
                                        {...config}
                                        key={`${sectionName}-${name}-${skillIndex}`}
                                        skill={{ id, name, removed }}
                                        onRemoveSkill={() => onRemoveSkill(entityId, id)}
                                        onRemoveSkillCancel={() => onRemoveSkillCancel(entityId, id)}
                                        skillInfo={skillInfo}
                                        deleteLabels={deleteLabels}
                                        skillPreferences={skillPreferences}
                                        resourceSkillPreference={resourceSkillPreference}
                                        onSkillPreferenceChange={onPreferenceChange}
                                        entityId={entityId}
                                        getIsSkillPending={getIsSkillPending}
                                    >
                                        {
                                            renderFieldsOnly ?
                                                <SkillFieldsForm {...formProps} form={form} expandIds />
                                                : <SkillFieldsFormCreated {...formProps} mapPropsToFields={mapPropsToFields} onFieldsChange={onSkillsFieldsChange} />
                                        }
                                    </Skill>
                                );
                            })
                    }
                </div>
            </div>
        );
    }
}

SkillsSection.propTypes = {
    section: object.isRequired,
    config: object.isRequired,
    onRemoveSkill: func.isRequired,
    onRemoveSkillCancel: func.isRequired,
    getIsSkillPending: func.isRequired
};

const Skill = ({
    entityId,
    skill: {
        id: skillId,
        name,
        removed
    },
    markDeletedMessage,
    cancelDeletionMessage,
    onRemoveSkill,
    onRemoveSkillCancel,
    deleteLabels,
    children = {},
    skillInfo: {
        fields = [],
        level = {}
    },
    skillPreferences,
    resourceSkillPreference,
    onSkillPreferenceChange,
    isRecommendationsView,
    onAcceptSkill,
    onIgnoreSkill,
    actionedSkillIds,
    getIsSkillPending
}) => {

    const hideCardBody = removed || (fields.length == 0 && !level.levelField);
    const getTitle = () => {
        if (isRecommendationsView) {
            const showActionButtons = !actionedSkillIds.includes(skillId);

            return (<RecommendationCardTitle
                skillName={name}
                skillId={skillId}
                onAcceptSkill={onAcceptSkill}
                onIgnoreSkill={onIgnoreSkill}
                showActionButtons={showActionButtons}
            />);
        } else {

            return (<MarkForDeletion
                text={name}
                markDeleted={removed}
                markDeletedMessage={markDeletedMessage}
                cancelActionMessage={cancelDeletionMessage}
                onAction={onRemoveSkill}
                onCancelAction={onRemoveSkillCancel}
                deleteLabel={deleteLabels.deleteLabel}
                cancelDeletionAriaLabel={deleteLabels.cancelDeletionSkillLabel}
                skillPreferences={skillPreferences}
                resourceSkillPreference={resourceSkillPreference}
                onSkillPreferenceChange={onSkillPreferenceChange}
                getIsSkillPending={getIsSkillPending}
                skillId={skillId}
                entityId={entityId}
            />);
        }
    };

    return (
        <Card
            id={skillId}
            className={`skill-container ${hideCardBody ? 'hidden-body' : ''} ${resourceSkillPreference}`}
            title={
                getTitle()
            }>
            <div role="group" aria-labelledby={skillId}>{!hideCardBody && children}</div>
        </Card>
    );
};

Skill.propTypes = {
    skill: object.isRequired,
    markDeletedMessage: string.isRequired,
    cancelDeletionMessage: string.isRequired,
    onRemoveSkill: func.isRequired,
    onRemoveSkillCancel: func.isRequired,
    children: object,
    skillInfo: object.isRequired,
    skillPreferences: array.isRequired,
    resourceSkillPreference: string.isRequired,
    onSkillPreferenceChange: func.isRequired,
    isRecommendationsView: bool,
    onAcceptSkill: func,
    onIgnoreSkill: func,
    actionedSkillIds: array,
    entityId: string.isRequired,
    getIsSkillPending: func.isRequired
};
