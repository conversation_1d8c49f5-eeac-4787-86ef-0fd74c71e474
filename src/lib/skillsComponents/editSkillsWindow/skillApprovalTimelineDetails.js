import React from 'react';
import PropTypes from 'prop-types';
import { CheckCircleFilled, CloseCircleFilled } from '@ant-design/icons';
import { formatLocalDate } from '../../../utils/dateUtils';
import { SKILLS_APPROVAL_AUDIT_ACTION_TYPES, SKILLS_APPROVAL_AUDIT_ACTIONS, SKILLS_APPROVAL_HISTORY_CONST, SKILLS_APPROVAL_STATUS_HISTORY } from '../../../constants/auditTrailConsts';
import { DATE_FORMATS } from '../../../constants';

/**
 * The component is used to build the timeline details.
 * @param {{ history: Object; }} param0 Represents the each history object
 * @returns Timeline details
 */
const SkillApprovalTimelineDetails = ({ history }) => {

    /**
     * The method is used to build the timeDetails
     * @param {string} type Defines the type of the history 'Insert', 'Update'
     */
    const buildTimelineDetails = (type) => {
        const newSkillLevel = history?.newValue?.resourceskill_skilllevel_description;
        const oldSkillLevel = history?.oldValue?.resourceskill_skilllevel_description;

        // If the type is 'Insert' then build Level of the skill
        if (type === SKILLS_APPROVAL_AUDIT_ACTION_TYPES.INSERT) {
            return <div><strong>{SKILLS_APPROVAL_HISTORY_CONST.ENTITY_LEVEL_PREFIX}: {newSkillLevel}</strong></div>;
        }

        // If the type is 'Update' then create a element with UpdatedLevel and from updatedLevel.
        if (type === SKILLS_APPROVAL_AUDIT_ACTION_TYPES.UPDATE) {
            return (
                <>
                    <div><strong>{SKILLS_APPROVAL_HISTORY_CONST.ENTITY_LEVEL_PREFIX}: {newSkillLevel}</strong></div>
                    <div>{SKILLS_APPROVAL_HISTORY_CONST.PREVIOUS_VALUES_PREFIX}</div>
                    <div>{SKILLS_APPROVAL_HISTORY_CONST.ENTITY_LEVEL_PREFIX}: {oldSkillLevel}</div>
                </>
            );
        }

        return null;
    };

    return (
        <div className="skill-approval-history">
            <div className="timeline-time-icon">
                {formatLocalDate(history.actiondateTime, DATE_FORMATS.HOUR_MINUTES)}
                {history.status === SKILLS_APPROVAL_STATUS_HISTORY.APPROVED ? (
                    <CheckCircleFilled className="skill-history-check-filled-circle" />
                ) : (
                    <CloseCircleFilled className="skill-history-cross-filled-circle" />
                )}
            </div>
            <div className="timeline-entry-details">
                <div>{SKILLS_APPROVAL_AUDIT_ACTIONS[history.type]?.text}</div>
                <strong>{history.skill_description}</strong>
                {buildTimelineDetails(history.type)}
                {history.status === SKILLS_APPROVAL_STATUS_HISTORY.APPROVED ? (
                    <div>{SKILLS_APPROVAL_HISTORY_CONST.ENTITY_APPROVED} {SKILLS_APPROVAL_HISTORY_CONST.ACTOR_PREFIX} {history.approvedBy}</div>
                ) :
                    <div>{SKILLS_APPROVAL_HISTORY_CONST.ENTITY_REJECTED} {SKILLS_APPROVAL_HISTORY_CONST.ACTOR_PREFIX} {history.rejectedBy}</div>
                }
            </div>
        </div>
    );
};

SkillApprovalTimelineDetails.propTypes = {
    history: PropTypes.object.isRequired
};

export default SkillApprovalTimelineDetails;
