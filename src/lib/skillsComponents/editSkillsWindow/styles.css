:root {
    --primary-color: #018848;
    --primary-10: #007A40;
    --dark-grey-color: #b6b4b4;
    --grey-color: #E6E6E6;
    --grey-3: #F4F4F4;
}

.skills-window {
    width: auto !important
}

.skills-window > .ant-modal-content {
    width: 800px;
    margin: auto;
}

.skills-window > .ant-modal-content > .ant-modal-footer {
    padding: 16px 24px;
}

.skills-window .button-save {
    margin: 0 6px 0 0;
}

.skills-window-content .skills-window-body {
    overflow-x: auto;
}

.skills-window-content .section-container {
    margin: 16px 24px 16px 0;
}

.skills-window-content .section-container .section-header {
    margin: 16px 0 0 0;
    font-size: 18px;
}

.skills-window-content .skills-container {
    margin: 16px 0 8px;
}

.skills-container .ant-card-bordered {
    border: 1px solid var(--grey-color);
    border-radius: 15px !important;
}

.PrimarySkill {
    border: 2px solid var(--primary-color) !important;
}

.SecondarySkill {
    border: 2px solid var(--dark-grey-color) !important;
}

.skills-container .ant-card-head {
    border-bottom: 1px solid var(--grey-color);
}

.skills-container .message-container a {
    color: var(--primary-10);
}

.skills-window-content .skill-container {
    background: var(--grey-3);
    font-size: 16px;
    margin: 0 0 8px;
}

.skills-window-content .skill-container > .ant-card-head {
    padding: 0 16px;
}

.skills-window-content .skill-container > .ant-card-head-title {
    padding: 8px 0;
}

.skill-container .button-delete {
    position: absolute;
    right: 16px;
}

.skills-window-header {
    margin: 0 24px 0 0;
}

.skills-window-header > .divider {
    margin: 24px 0 0 0 !important;
}

.skills-window-header-title {
    font-weight: bold;
    margin: 0 3.0em 0 0;
}

.skills-window-body {
    margin: 24px 0 0 0;
}

.line-through-text {
    text-decoration: line-through;
}

.hidden-body > .ant-card-body {
    display: none;
}

.hidden-body > .ant-card-head {
    border-bottom:0 !important;
}

.hidden-skills-container {
    display: none;
}

.icon-collapse {
    margin: 0 4px 0 0;
    font-size: 16px;
}

.section-header i {
    cursor: pointer;
}

.section-container .collapsed {
    transform: rotate(180deg);
}


.skills-window-body {
    margin: 0;
}

.skills-window-body .section-container {
    margin-top: 16px;
}

.skills-window-body .section-header {
    font-weight: 600;
    font-size: 16px;
}

.skills-window-body .skill-container {
    border-radius: 5px;
    margin-top: 16px;
}

.skills-window-body .skill-container .button-delete {
    right: 15px;
}

.skills-window-body .skill-container .ant-card-head {
    min-height: unset;
    padding: 0 15px;
}

.skills-window-body .skill-container .ant-card-head .ant-card-head-title {
    padding: 8px 0;
}

.skills-window-body .skill-container .ant-card-head .ant-card-head-title .mark-for-deletion-text {
    display: flex;
    align-items: center;
}

.skills-window-body .skill-container .ant-card-head .ant-card-head-title .mark-for-deletion-text span {
    font-weight: 600;
    font-size: 16px;
}

.skills-window-body .skill-container .ant-card-body {
    padding: 10px 15px 7px;
}
.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}
.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item .ant-switch {
    margin: 0;
}
.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item .ant-legacy-form-item-label {
    padding-left: 0;
    font-weight: 500;
    line-height: normal;
    padding-top: 0;
}

.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item-control {
    margin: 0;
    margin-top: 0;
}

.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item-control .ant-input-number {
    height: 2.2rem;
}

.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item-control .ant-input,
.skills-window-body .skill-container .ant-card-body .ant-select-selection--single {
    margin: 0;
}

.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item .ant-legacy-form-item-label .ant-legacy-form-item-required::before,
.skills-window-body .skill-container .ant-card-body .ant-legacy-form-item .ant-legacy-form-item-label .ant-legacy-form-item-required span::before {
    position: static;
    font-family: unset;
}

.skills-window-content .section-container .section-header {
    font-weight: 400;
}


/* Styles for the modal container */
.skills-window .ant-modal-content {
    padding: 0;
}

/* Styles for the modal body */
.skills-window .ant-modal-body {
    padding: 0;
    height: 70vh;
    overflow: hidden;
}

/* Styles for the tabs */
.skills-tabs .ant-tabs-nav {
    margin: 0;
}

/* Styles for the tab label count */
.skills-tabs .ant-tabs-tab .tab-count {
    color: #00796b;
}

/* Styles for the Skills tab content */
.skills-window-content {
    margin:10px;
    height: calc(70vh - 85px);
    overflow: auto;
}

/* Styles for the Skills tab content */
.skills-window-content-old {
    margin:10px;
    height: calc(70vh - 35px);
    overflow: auto;
} 

.skills-window-content-old .section-container {
    margin: 16px 24px 16px 0;
}

/* Styles for the Recommendations tab content */
.recommendations-tab {
    height: calc(70vh - 85px);
    padding: 0 20px;
    overflow: auto;

    .ant-spin-spinning {
        position: absolute;
    }

}

/* Styles for the Recommendations tab footer */
.recommendations-footer {
    display: flex;
    justify-content: flex-end;
    padding: 10px 20px;
}

.skills-window-content .ant-empty{
    position: relative;
    top: 32%;
}