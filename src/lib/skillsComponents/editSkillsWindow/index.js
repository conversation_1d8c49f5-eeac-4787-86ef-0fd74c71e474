import React, { Component } from 'react';
import { bool, object, func, string } from 'prop-types';
import { Empty, Tabs, Typography } from 'antd';
import './styles.css';
import { EditSkillsWindowFooter } from './footer';
import { EditSkillsWindowHeader } from './header';
import { EditSkillsWindowBody } from './body';
import AntdModal from '../../antdModal';
import { RecommendationsTab } from '../recommendationsWindow/RecommendationsPanel';
import './styles.css';
import { Spin } from 'antd';
import { Icon } from '../../../lib';
import { ConnectedSkillApprovalsTab } from './../../../connectedComponents/connectedEditResourceSkillsWindow/connectedSkillApprovalsTab';
import { Divider } from 'antd';
import { SkillsFilterCascader } from '../../filters/filterControls';
import styles from '../../../lib/entityForm/sections/styles.less';

const { TabPane } = Tabs;
export class EditSkillsWindow extends Component {
    constructor(props) {
        super(props);

        this.onCancel = this.onCancel.bind(this);
        this.onAddSkills = this.onAddSkills.bind(this);
        this.handleTabChange = this.handleTabChange.bind(this);
        this.onAcceptRecommendationSkills = this.onAcceptRecommendationSkills.bind(this);
        this.onIgnoreRecommendationSkills = this.onIgnoreRecommendationSkills.bind(this);
    }

    componentDidMount() {
        if (this.props.skillsFilterCascaderEnabled) {
            this.props.loadResourceSkillsAllowed();
        }
    }

    onCancel() {
        this.props.onCancel(this.props.entityId);
    }

    onAddSkills(skillIds = []) {
        const { entityId, bodyProps: { getSkillInfo } } = this.props;
        this.props.onAutoCompleteSkillsSelect(entityId, skillIds, skillIds.map(getSkillInfo));
    }

    onAcceptRecommendationSkills(skillIds = []) {
        const { entityId, bodyProps: { getSkillInfo } } = this.props;
        this.props.onAcceptRecommendSkills(entityId, skillIds, skillIds.map(getSkillInfo));
    }

    onIgnoreRecommendationSkills(skillIds = []) {
        const { entityId } = this.props;
        this.props.onIgnoreRecommendSkills(entityId, skillIds);
    }

    handleTabChange(activeKey) {
        this.props.onTabChange(activeKey);
    }

    renderFooter() {
        const { footerProps, onSave, onCancel } = this.props;

        return (
            <EditSkillsWindowFooter
                {...footerProps}
                onSave={onSave}
                onCancel={onCancel}
            />
        );
    }

    render() {
        const {
            visible,
            activeTab,
            recommendationsEnabled,
            headerProps,
            bodyProps,
            getAutoCompleteSkills,
            onClearAutoComplete,
            onRemoveSkill,
            onRemoveSkillCancel,
            onSkillFieldChange,
            onSkillsFieldsChange,
            entityId,
            skillsFilterCascaderEnabled
        } = this.props;

        const { recommendationSectionData, skillSections, staticMessages, skillApprovalEnabled, isResourceSkillsApprovalsPendingLoaded } = bodyProps;
        const { totalRecommendations, loading } = recommendationSectionData;
        const { noAddedSkills, recommendationTitle, mySkillsLabel } = staticMessages;
        const getRecommendedTabLabel = () => {
            const label = recommendationTitle;

            return (totalRecommendations ? <span>{label} <span className="tab-count">({totalRecommendations})</span></span> : label);
        };

        // show skillsFilterCascader if the TalentProfilePageSkillsFilterCascader feature is enabled else show the previous skills search
        const skillsSearchSection = skillsFilterCascaderEnabled ? (
            <div className="skills-window-header">
                <span className="skills-window-header-title">
                    {headerProps.title}
                </span>
                <SkillsFilterCascader
                    {...headerProps}
                    className={styles.searchIcon}
                />
                <Divider className="divider" />
            </div>
        ) : (
            <EditSkillsWindowHeader
                {...headerProps}
                getAutoCompleteSkills={getAutoCompleteSkills}
                clearSkills={onClearAutoComplete}
                onSkillsSelect={this.onAddSkills}
            />
        );

        return (
            <AntdModal
                className="skills-window"
                open={visible}
                closable={false}
                footer={this.renderFooter()}
                onCancel={this.onCancel}
                width={700}
            >
                {!recommendationsEnabled && <div className="skills-window-content-old">

                    {/* Skills search */}
                    {skillsSearchSection}

                    <EditSkillsWindowBody
                        {...bodyProps}
                        onRemoveSkill={onRemoveSkill}
                        onRemoveSkillCancel={onRemoveSkillCancel}
                        onSkillFieldChange={onSkillFieldChange}
                        onSkillsFieldsChange={onSkillsFieldsChange}
                    />
                </div>}
                {recommendationsEnabled &&
                    <Tabs
                        activeKey={activeTab}
                        onChange={this.handleTabChange}
                        className="skills-tabs"
                    >
                        <TabPane tab={mySkillsLabel} key="1">
                            {(skillApprovalEnabled && isResourceSkillsApprovalsPendingLoaded) ?? <Spin indicator={<Icon type="loading" style={{ fontSize: 35 }} spin />} spinning={loading} style={{ top: '50%', left: '50%' }} />}
                            <div className="skills-window-content">
                                {/* Skills search */}
                                {skillsSearchSection}

                                {!!skillSections.length && <EditSkillsWindowBody
                                    {...bodyProps}
                                    onRemoveSkill={onRemoveSkill}
                                    onRemoveSkillCancel={onRemoveSkillCancel}
                                    onSkillFieldChange={onSkillFieldChange}
                                    onSkillsFieldsChange={onSkillsFieldsChange}
                                />}
                                {!skillSections.length && <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description={
                                    <Typography.Text>
                                        {noAddedSkills}
                                    </Typography.Text>
                                } />}
                            </div>
                        </TabPane>

                        {skillApprovalEnabled &&
                            <TabPane tab={this.getApprovalRequestsTabLabel()} key="3">
                                <ConnectedSkillApprovalsTab />
                            </TabPane>}

                        <TabPane tab={getRecommendedTabLabel()} key="2">
                            <div className="recommendations-tab">
                                {loading && <Spin indicator={<Icon type="loading" style={{ fontSize: 35 }} spin />} spinning={loading} style={{ top: '50%', left: '50%' }} />}
                                {!loading &&
                                    <RecommendationsTab
                                        bodyProps={bodyProps}
                                        handleAcceptSkills={this.onAcceptRecommendationSkills}
                                        handleIgnoreSkills={this.onIgnoreRecommendationSkills}
                                        entityId={entityId}
                                        onSkillFieldChange={onSkillFieldChange}
                                        onSkillsFieldsChange={onSkillsFieldsChange}
                                    />}
                            </div>
                        </TabPane>
                    </Tabs>
                }
            </AntdModal>
        );
    }

    // Get the label for the Approval Requests tab
    // This method is used to display the number of pending requests in the tab label
    getApprovalRequestsTabLabel = () => {
        const { approvalRequestsData, staticMessages } = this.props.bodyProps;
        const pendingCount = approvalRequestsData?.pendingCount || 0;

        return (
            <span>
                {staticMessages.approvalRequestsLabel}
                {pendingCount > 0 && <span className="tab-count">({pendingCount})</span>}
            </span>
        );
    };
}

EditSkillsWindow.propTypes = {
    visible: bool.isRequired,
    headerProps: object.isRequired,
    bodyProps: object.isRequired,
    getAutoCompleteSkills: func.isRequired,
    onClearAutoComplete: func.isRequired,
    onRemoveSkill: func.isRequired,
    onRemoveSkillCancel: func.isRequired,
    onSkillFieldChange: func.isRequired,
    onSkillsFieldsChange: func.isRequired,
    entityId: string.isRequired,
    onSave: func.isRequired,
    onCancel: func.isRequired,
    loadSkillsAllowed: func.isRequired,
    skillsFilterCascaderEnabled: bool.isRequired
};
