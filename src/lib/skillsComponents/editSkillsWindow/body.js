import React from 'react';
import { func, object, array, bool } from 'prop-types';
import { SkillsSection } from './skillSection';
import SkillApprovalAlert from '../skillApprovalAlert/skillApprovalAlert';
import { isBoolean } from 'lodash';

export const EditSkillsWindowBody = ({
    skillSections,
    skillApprovalFeatureFlagEnabled,
    resourceSkillApprovalPermission,
    ...props
}) => (
    <div className="skills-window-body">
        {skillApprovalFeatureFlagEnabled && !!resourceSkillApprovalPermission && <SkillApprovalAlert />}
        {
            skillSections
                .map(({ sectionName, collapsed, ...section }, index) => (
                    <SkillsSection
                        {...props}
                        key={`${sectionName}-${index}`}
                        section={{
                            ...section,
                            collapsed,
                            sectionName
                        }}
                    />
                ))
        }
    </div>
);

EditSkillsWindowBody.propTypes = {
    skillSections: array.isRequired,
    config: object.isRequired,
    staticMessages: object.isRequired,
    onRemoveSkill: func.isRequired,
    skillsStaticLabels: object,
    onRemoveSkillCancel: func.isRequired,
    hiddenFieldIds: array.isRequired,
    onSkillPreferenceChange: func.isRequired,
    skillApprovalFeatureFlagEnabled: bool.isRequired,
    resourceSkillApprovalPermission: bool.isRequired,
    getIsSkillPending: func.isRequired
};
