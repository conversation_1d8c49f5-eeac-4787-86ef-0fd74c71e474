import React from 'react';
import { Tabs, Empty, Typography } from 'antd';
import PropTypes from 'prop-types';
import { ResendRequestSection } from './resendRequestSection';
import SkillApprovalComponent from './pendingSection';
import './approvalStyles.less';
import { ConnectedSkillApprovalHistory } from '../../../../connectedComponents/connectedEditResourceSkillsWindow/connectedSkillApprovalHistory';

const { TabPane } = Tabs;

const ApprovalSkillsWindow = ({
    resourceId,
    managerName,
    staticMessages,
    approvalRequestsData,
    onResendRequest,
    getSkillInfo
}) => {

    const { approvalRequestsLabel, noSkillPendingRequestsLabel, noSkillApprovalHistoryLabel, skillApprovalHistoryLabel } = staticMessages;
    const { pendingRequests, history } = approvalRequestsData;

    return (
        <div className="approval-requests-tab">
            <Tabs defaultActiveKey="pending" className="nested-approval-tabs">
                <TabPane tab="Pending" key="pending">
                    {approvalRequestsData && approvalRequestsData.pendingRequests?.data ? (
                        <>
                            {pendingRequests.data.some((request) => request.resendRequest === true) && (
                                <ResendRequestSection managerName={managerName} onResend={() => onResendRequest(resourceId)} />
                            )}

                            <SkillApprovalComponent
                                pendingRequests={pendingRequests.data}
                                getSkillInfo={getSkillInfo}
                                onApprove={(id, level) => console.log(`Approve ${id} with level: ${level}`)}
                                onReject={(id) => console.log(`Reject ${id}`)}
                            />

                        </>
                    ) : (
                        <Empty description={noSkillPendingRequestsLabel} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                </TabPane>
                <TabPane tab="Historic" key="historic">
                    {history?.data?.length > 0 ? (
                        <>
                            <div className="skill-approval-history-message-label">
                                <Typography.Text strong>{skillApprovalHistoryLabel}</Typography.Text>
                            </div>
                            {/* <ConnectedSkillApprovalHistory/> */}
                        </>
                    ) : (
                        <Empty description={noSkillApprovalHistoryLabel} image={Empty.PRESENTED_IMAGE_SIMPLE} />
                    )}
                </TabPane>
            </Tabs>
        </div>
    );
};

ApprovalSkillsWindow.propTypes = {
    approvalRequestsData: PropTypes.object,
    managerName: PropTypes.string,
    staticMessages: PropTypes.object,
    resourceId: PropTypes.string,
    onResendRequest: PropTypes.func,
    getSkillInfo: PropTypes.func
};

export default ApprovalSkillsWindow;
