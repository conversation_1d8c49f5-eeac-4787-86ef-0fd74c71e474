import React from 'react';
import { Typography, Space } from 'antd';
import PropTypes from 'prop-types';
import styles from './approvalStyles.less';

export const ResendRequestSection = ({
    managerName,
    onResend
}) => {
    return (
        <div className="resend-request-section">
            <Space align="center">
                <Typography.Link className={styles.resendButton} underline onClick={onResend}>
                    Resend
                </Typography.Link>
                <Typography.Text type="secondary">
                    your request to <Typography.Text strong>{managerName}</Typography.Text>
                </Typography.Text>
            </Space>
        </div>
    );
};

ResendRequestSection.propTypes = {
    managerName: PropTypes.string.isRequired,
    onResend: PropTypes.func.isRequired
};
