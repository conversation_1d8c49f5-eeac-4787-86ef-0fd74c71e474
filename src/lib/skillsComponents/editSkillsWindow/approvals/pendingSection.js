import React from 'react';
import { <PERSON>, Button, Typography, Tag, Divider, Space, Row, Col, Select } from 'antd';
import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import PropTypes from 'prop-types';
import { getActionText } from '../../../../utils/talentProfileUtils';
import { DISPLAY_DATE_TIME_FORMATS } from '../../../../constants/globalConsts';
import { formatLocalDate, getDisplayShortDateFormat } from '../../../../utils/dateUtils';
import './approvalStyles.less';

const { Text } = Typography;
const { Option } = Select;

const SkillApprovalComponent = ({ pendingRequests, onApproveAll, onRejectAll, onApproveSkill, onRejectSkill, onLevelChange, getSkillInfo = () => { } }) => {
    const handleLevelChange = (skillId, value) => {
        if (onLevelChange) {
            onLevelChange(skillId, value);
        }
    };

    // Group requests by type
    const groupedRequests = pendingRequests.reduce((acc, request) => {
        const type = request.type || 'unknown';
        acc[type] = acc[type] || [];
        acc[type].push(request);

        return acc;
    }, {});

    const renderSkillCards = (requests, type) => (
        <div className="pending-skills-list">
            {requests.map((request, index) => {
                const skillInfo = getSkillInfo(request.skillId);

                return (
                    <Card key={index} className="skill-approval-card" bordered={false}>
                        {/* Skill name row with grey background and round borders */}
                        <Row className="skill-name-row" justify="space-between" align="middle">
                            <Col flex="auto" className="skill-name-col">
                                <div className="skill-name-container">
                                    <Text strong ellipsis={{ tooltip: request.skillDescription }}>
                                        {request.skillDescription}
                                    </Text>
                                </div>
                            </Col>
                            <Col className="skill-actions-col">
                                <Space>
                                    <Button
                                        size="small"
                                        icon={<CloseOutlined />}
                                        onClick={() => onRejectSkill(request.skillId)}
                                        className="reject-skill-button"
                                        title="Reject this skill"
                                    ></Button>
                                    <Button
                                        size="small"
                                        type="primary"
                                        icon={<CheckOutlined />}
                                        onClick={() => onApproveSkill(request.skillId)}
                                        className="approve-skill-button"
                                        title="Accept this skill"
                                    ></Button>
                                </Space>
                            </Col>
                        </Row>
                        {/* Level row with white background */}
                        {request.type !== 'delete' && skillInfo?.level?.levels?.length > 0 && (
                            <Row className="level-row" align="middle">
                                <Col span={4}>
                                    <Text type="secondary">Level*</Text>
                                </Col>
                                <Col span={20}>
                                    <Select
                                        defaultValue={request.level}
                                        style={{ width: 200 }}
                                        onChange={(value) => handleLevelChange(request.skillId, value)}
                                        className="level-dropdown"
                                    >
                                        {skillInfo.level.levels.map((level) => (
                                            <Option key={level.id} value={level.id}>
                                                {level.name}
                                            </Option>
                                        ))}
                                    </Select>
                                </Col>
                            </Row>
                        )}
                    </Card>
                );
            })}
        </div>
    );

    return (
        <div className="skill-approval-component">
            <div className="approval-actions">
                <Space>
                    <Button
                        type="text"
                        icon={<CloseOutlined />}
                        onClick={onRejectAll}
                        className="reject-all-button"
                        title="Reject all skills"
                    >
                        Reject all
                    </Button>
                    <Button
                        type="primary"
                        icon={<CheckOutlined />}
                        onClick={onApproveAll}
                        className="approve-all-button"
                        title="Approve all skills"
                    >
                        Approve all
                    </Button>
                </Space>
            </div>

            <Divider />

            {/* Render grouped sections with date and Requested tag using getActionText */}
            {Object.keys(groupedRequests).map((type) => {
                const firstRequest = groupedRequests[type][0];
                const formattedDateTime = firstRequest
                    ? formatLocalDate(firstRequest.createdDateTime, getDisplayShortDateFormat(DISPLAY_DATE_TIME_FORMATS.SHORT_YEAR_WITH_TIME))
                    : '';

                return (
                    <div key={type} className="skill-group-section">
                        <Row justify="space-between" align="middle" style={{ marginBottom: 8 }}>
                            <Col>
                                <Text type="secondary" strong>
                                    {getActionText(type)}
                                    {formattedDateTime && <Text type="secondary">· {formattedDateTime}</Text>}
                                </Text>
                            </Col>
                            <Col>
                                <Tag className="requested-tag">Requested</Tag>
                            </Col>
                        </Row>
                        {renderSkillCards(groupedRequests[type], type)}
                        <Divider />
                    </div>
                );
            })}
        </div>
    );
};

SkillApprovalComponent.propTypes = {
    pendingRequests: PropTypes.arrayOf(
        PropTypes.shape({
            skillId: PropTypes.string.isRequired,
            type: PropTypes.oneOf(['add', 'update', 'delete']),
            createdDateTime: PropTypes.string,
            skillDescription: PropTypes.string,
            level: PropTypes.string,
            requested: PropTypes.bool
        })
    ).isRequired,
    onApproveAll: PropTypes.func,
    onRejectAll: PropTypes.func,
    onApproveSkill: PropTypes.func,
    onRejectSkill: PropTypes.func,
    onLevelChange: PropTypes.func,
    getSkillInfo: PropTypes.func.isRequired
};

export default SkillApprovalComponent;
