.approval-requests-tab {
  height: calc(70vh - 85px);
  overflow: auto;
}

.nested-approval-tabs {
  .ant-tabs-nav .ant-tabs-nav-wrap {
    padding: 8px;
  }

  .ant-tabs-tab {
    background-color: @grey-2;
    color: @secondary-color;
    border: none;
    padding: 4px 12px;
    border-radius: 8px;
    transition: all 0.2s ease;
  }

  /* Active tab */
  .ant-tabs-tab.ant-tabs-tab-active {
    background-color: @primary-color;
    border-radius: 8px;

    .ant-tabs-tab-btn {
      color: @white-color !important;
    }
  }

  .ant-tabs-ink-bar {
    display: none;
  }
}

.skill-approval-component {
  .approval-actions {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 16px;

    .reject-all-button {
      margin-right: 8px;
    }
  }

  .pending-skills-list {
    .skill-approval-card {
      margin-bottom: 16px;
      border-radius: 8px;
      overflow: hidden;

      .ant-card-body {
        padding: 0;
      }

      .action-row {
        padding: 8px 16px;
        background-color: #fafafa;
        border-bottom: 1px solid #f0f0f0;
      }

      .skill-name-row {
        padding: 12px 16px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;

        .skill-name-col {
          flex: 1;
          min-width: 0; // This is crucial for ellipsis to work with flexbox
        }

        .skill-name-container {
          max-width: 400px; // Adjust this value as needed
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          .ant-typography {
            margin: 0;
          }
        }

        .skill-actions-col {
          flex-shrink: 0;
          margin-left: 16px;
        }

        .ant-col:last-child {
          text-align: right;
        }
      }

      .level-row {
        padding: 12px 16px;

        .level-dropdown {
          width: 150px;
        }
      }

      .requested-tag {
        background-color: #f6ffed;
        border-color: #b7eb8f;
        color: #389e0d;
        border-radius: 4px;
      }
    }
  }
}

.skill-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 8px;
}

.ant-collapse {
  background-color: transparent;

  .ant-collapse-item {
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  .ant-collapse-header {
    padding: 12px 0 !important;
  }

  .ant-collapse-content-box {
    padding: 0 !important;
  }
}
