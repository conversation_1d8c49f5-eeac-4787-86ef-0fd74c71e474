import React from 'react';
import { Empty } from 'antd';
import './styles.less';
import PropTypes from 'prop-types';
import { bindAll } from 'lodash';
import ComponentTab from '../../components/common-components/tab-structure/componentTabs';
import { NotificationsHistory } from './notificationsHistory/notificationsHistory';
import { adminSettingConsts } from '../../constants';
import { NotificationSettingsForm } from './notificationsSettings/notificationsSettings';
import { pushHistoryWithState } from '../../components/common-components/helpers';
import { UnsavedChangesPrompt } from '../../connectedComponents/connectedPrompt/connectedPrompts/connectedUnsavedChangesPrompt';
import queryString from 'query-string';
import { ConnectedEditResourceSkillsWindow } from '../../connectedComponents/connectedEditResourceSkillsWindow';
import { NOTIFICATIONS_PAGE_ALIAS } from '../../constants/notificationsPageConsts';

const { ROUTE_ADMIN_PAGE_CONSTANTS } = adminSettingConsts;
const { QUERY_PARAMS } = ROUTE_ADMIN_PAGE_CONSTANTS;
const { TAB } = QUERY_PARAMS;

class NotificationsRoot extends React.Component {
    constructor(props) {
        super(props);
        this.childRef = React.createRef();
        bindAll(this, ['getTabComponent', 'onTabChange', 'renderEmptyComponent', 'renderSettingsComponent']);
        this.state = {
            promptActioned: false
        };
    }

    getMainContent() {
        return (
            <ComponentTab
                tabConfig={this.getTabConfig()}
                ref={this.tabRef}
                nofityParent={this.onTabChange}
                {...this.props}
            />
        );
    }

    getTabConfig() {
        const { notificationsTabConfig = {}, notificationHistory, hasManagerRequest = false } = this.props;
        let newNotificationsTabConfig = notificationsTabConfig;

        if (hasManagerRequest) {
            // ALTER only if skillApproval is enabled
            const { tabDetails = [] } = notificationsTabConfig;
            const { notificationsHistoryData = [], notificationsRequestData = [] } = notificationHistory;
            const newTabDetails = tabDetails.map(tab => {
                switch (tab.key) {
                    case 0: return { ...tab, tabName:`${tab.tabName} (${notificationsHistoryData.length})` };
                    case 1: return { ...tab, tabName:`${tab.tabName} (${notificationsRequestData.length})` };
                    default: return tab;
                }
            });

            newNotificationsTabConfig = {
                ...notificationsTabConfig,
                tabDetails:newTabDetails
            };
        }


        return { ...newNotificationsTabConfig, getTabComponent: this.getTabComponent };
    }

    getTabComponent(tabName) {
        let tabComponent = null;
        const { notificationHistoryLabel, notificationSettingsLabel, notificationRequestLabel } = this.props.notificationStaticMessagesTabs;
        // tabName here will be All (1) or Request (1) hence, compare without count
        switch (tabName.split(' ')[0]) {
            case notificationHistoryLabel: {
                tabComponent = this.renderHistoryComponent();
                break;
            }
            case notificationRequestLabel: {
                tabComponent = this.renderRequestComponent();
                break;
            }
            case notificationSettingsLabel: {
                tabComponent = this.renderSettingsComponent();
                break;
            }
            default:
                break;
        }

        return tabComponent;
    }

    renderEmptyComponent() {
        const { notificationEvents, isLoading = false, showMoreProps } = this.props;
        const { displayShowMore } = showMoreProps;
        const showEmptyComponent = notificationEvents.length === 0;

        return (
            <>
                {!displayShowMore && showEmptyComponent && !isLoading && (
                    <Empty
                        image={Empty.PRESENTED_IMAGE_SIMPLE}
                        description={this.props.noResultsText}
                    />
                )}
            </>
        );
    }

    renderRequestComponent() {
        const { notificationHistory, notificationEvents = [], onMarkAllReadButtonClick, headerProps = {} } = this.props;
        const { notificationsRequestData = [] } = notificationHistory;
        const requestIds = notificationsRequestData.map(x => x.guid);
        // Filter the notificationEvents to show only request notifications
        const newNotificationEvents = notificationEvents.filter(x => requestIds.includes(x.notificationId));
        const requestPreferenceKeys = newNotificationEvents.map(x => x.notificationPreferenceKey);
        // Override to only mark requests as read
        const onMarkAllRequestReadButtonClick = () => onMarkAllReadButtonClick(requestPreferenceKeys);
        const requestedHeaderProps = {
            ...headerProps,
            buttonConfig: {
                ...headerProps.buttonConfig,
                disableMarkAllRead: notificationHistory.isAllRequestNotificationRead || false
            }
        };

        return (
            <div className="notifications-list">
                <NotificationsHistory
                    {...this.props}
                    notificationEvents={newNotificationEvents}
                    onMarkAllReadButtonClick={onMarkAllRequestReadButtonClick}
                    headerProps= {requestedHeaderProps} />
                {this.renderEmptyComponent()}
                <ConnectedEditResourceSkillsWindow pageAlias={NOTIFICATIONS_PAGE_ALIAS}/>
            </div>
        );
    }

    renderHistoryComponent() {
        const { onMarkAllReadButtonClick } = this.props;
        const onMarkAllRequestReadButtonClick = () => onMarkAllReadButtonClick();

        return (
            <div className="notifications-list">
                <NotificationsHistory {...this.props} onMarkAllReadButtonClick={onMarkAllRequestReadButtonClick} />
                {this.renderEmptyComponent()}
                <ConnectedEditResourceSkillsWindow pageAlias={NOTIFICATIONS_PAGE_ALIAS} />
            </div>
        );
    }

    renderSettingsComponent() {
        const { notificationSettingsData,loadNotificationData, updatePageParams } = this.props;
        const { onFormReset, onNotificationSettingSave, unsavedChangesPromptProps, alias, changedFields = [] } = notificationSettingsData;

        const onPromptSaveChanges = (location) => {
            this.setState({ promptActioned: true }, () => {
                onNotificationSettingSave(alias);
                navigateToNextLocation(location);
            });
        };

        const onPromptDiscardChanges = (location) => {
            this.setState({ promptActioned: true }, () => {
                onFormReset(alias);
                navigateToNextLocation(location);
            });
        };

        function navigateToNextLocation(location) {
            const { pathname, state } = location;
            const { pathname: currentPathname } = notificationSettingsData.location;

            const queryParamKeys = queryString.parse(location.search);
            const currentTab = queryParamKeys['tab'];

            if (pathname !== currentPathname) {
                notificationSettingsData.history.push(pathname, state);
            } else {
                updatePageParams({ tab: currentTab });
                loadNotificationData({ tab: currentTab });
            }
        }

        const promptProps = {
            ...unsavedChangesPromptProps,
            shouldPrompt: this.state.promptActioned !== true && changedFields.length > 0,
            onSaveChanges:(location) => onPromptSaveChanges(location),
            onDiscardChanges: (location) => onPromptDiscardChanges(location),
            promptActioned: this.state.promptActioned
        };

        return (
            <div className="notifications-settings">
                <NotificationSettingsForm {...this.props.notificationSettingsData} />
                <UnsavedChangePromptForNotifications {...promptProps} />
            </div>
        );
    }

    onTabChange(currentTab) {
        const { notificationsTabConfig, match, updatePageParams, loadNotificationData, location, notificationStaticMessagesTabs } = this.props;
        const { notificationSettingsLabel } = notificationStaticMessagesTabs;
        const queryParamKeys = queryString.parse(location.search);
        const tab = queryParamKeys['tab'] || '';

        const { tabDetails = {} } = notificationsTabConfig;
        const currentTabName = tabDetails[currentTab] && tabDetails[currentTab].tabDefaultName;
        const queryParams = `?${TAB}=${currentTabName.toLowerCase()}`;
        if (this.props.notificationSettings.changedFields.length > 0 && tab.toLowerCase() === notificationSettingsLabel.toLowerCase()) {
            pushHistoryWithState(`${match.url}`, queryParams, this.props);
        } else {
            pushHistoryWithState(`${match.url}`, queryParams, this.props);
            updatePageParams({ tab: currentTabName });
            loadNotificationData({ tab: currentTabName.toLowerCase() });
            this.setState({ promptActioned: false });
        }
    }

    render() {
        return this.getMainContent();
    }
}

NotificationsRoot.propTypes = {
    notificationsTabConfig: PropTypes.object,
    hasManagerRequest : PropTypes.bool,
    notificationHistory: PropTypes.object,
    notificationEvents: PropTypes.array,
    headerProps: PropTypes.object,
    loadMoreButtonLabel: PropTypes.object,
    getItemsContextualMenuProps: PropTypes.func,
    noResultsText: PropTypes.object,
    showMoreProps: PropTypes.object,
    isLoading: PropTypes.bool,
    notificationStaticMessagesTabs: PropTypes.object,
    notificationSettings: PropTypes.object,
    match: PropTypes.object,
    updatePageParams: PropTypes.func,
    loadNotificationData: PropTypes.func,
    location: PropTypes.object,
    notificationSettingsData: PropTypes.object,
    onMarkAllReadButtonClick: PropTypes.func
};

export default NotificationsRoot;

class UnsavedChangePromptForNotifications extends UnsavedChangesPrompt {

    constructor(props) {
        super(props);
    }

    componentDidUpdate() {
        const { promptActioned } = this.props;

        if (this.state.confirmedNavigation !== promptActioned) {
            this.setState({
                confirmedNavigation: promptActioned
            });
        }
    }
}